name: Deploy AppConfig
on:
  push:
    branches: ["main"]
    paths:
      - 'deployment/cloudformation/app-config.yaml'
      - '.github/workflows/deploy-app-config.yaml'

permissions:
  id-token: write
  contents: read

env:
  PROD_REGION: us-east-2
  PROD_STACK_NAME: gestalt-client-appconfig

jobs:
  Update-AppConfig:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::505912074795:role/github-actions-assume-role
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.PROD_REGION }}

      - name: Create Change Set
        id: create-change-set
        run: |
          set -e
          CHANGE_SET_NAME="change-set-$(date +%s)"
          aws cloudformation create-change-set \
            --stack-name ${{ env.PROD_STACK_NAME }} \
            --template-body file://./deployment/cloudformation/app-config.yaml \
            --capabilities CAPABILITY_IAM \
            --change-set-type UPDATE \
            --change-set-name $CHANGE_SET_NAME \
            --query 'Id' \
            --output text
          echo "CHANGE_SET_NAME=$CHANGE_SET_NAME" >> $GITHUB_ENV

      - name: Wait for Change Set to be Created
        run: |
          set -e
          while true; do
            aws cloudformation describe-change-set \
              --stack-name ${{ env.PROD_STACK_NAME }} \
              --change-set-name ${{ env.CHANGE_SET_NAME }} > change-set.json
            cat change-set.json
            STATUS=$(jq -r '.Status' change-set.json)
            echo "Current status: $STATUS"
            if [[ "$STATUS" == "CREATE_COMPLETE" ]]; then
              break
            elif [[ "$STATUS" == "FAILED" ]]; then
              CHANGES=$(jq -r '.StatusReason' change-set.json)
              if [[ "$CHANGES" == *"No updates are to be performed"* ]]; then
                echo "NO_CHANGES=true" >> $GITHUB_ENV
                aws cloudformation delete-change-set \
                  --stack-name ${{ env.PROD_STACK_NAME }} \
                  --change-set-name ${{ env.CHANGE_SET_NAME }}
                break
              else
                echo "Change set creation failed: $CHANGES"
                exit 1
              fi
            fi
            sleep 5
          done

      - name: Stop Workflow if No Changes
        if: env.NO_CHANGES == 'true'
        run: echo "No changes to deploy. Exiting workflow."

      - name: Execute Change Set
        if: env.NO_CHANGES != 'true'
        run: |
          aws cloudformation execute-change-set \
            --stack-name ${{ env.PROD_STACK_NAME }} \
            --change-set-name ${{ env.CHANGE_SET_NAME }}

      - name: Retrieve CloudFormation Status and Outputs
        if: env.NO_CHANGES != 'true'
        id: get-cf-status-outputs
        run: |
          while true; do
            aws cloudformation describe-stacks \
              --stack-name ${{ env.PROD_STACK_NAME }} \
              --query "Stacks[0].{Status:StackStatus,Outputs:Outputs,StatusReason:StatusReason}" > cf-status-outputs.json
            cat cf-status-outputs.json
            STATUS=$(jq -r '.Status' cf-status-outputs.json)
            echo "Current status: $STATUS"
            if [[ "$STATUS" == "UPDATE_COMPLETE" || "$STATUS" == "CREATE_COMPLETE" ]]; then
              break
            elif [[ "$STATUS" == *"FAILED"* ]]; then
              CHANGES=$(jq -r '.StatusReason' cf-status-outputs.json)
              echo "Change set failed: $CHANGES"
              exit 1
            fi
            sleep 5
          done

      - name: Set Environment Variables from CF
        if: env.NO_CHANGES != 'true'
        id: set-env-vars
        run: |
          STACK_STATUS=$(jq -r '.Status' cf-status-outputs.json)
          echo "STACK_STATUS=$STACK_STATUS" >> $GITHUB_ENV
          echo "APP_CONFIG_APPLICATION_ID=$(jq -r '.Outputs[] | select(.OutputKey=="ApplicationId") | .OutputValue' cf-status-outputs.json)" >> $GITHUB_ENV
          echo "APP_CONFIG_ENVIRONMENT_ID=$(jq -r '.Outputs[] | select(.OutputKey=="EnvironmentId") | .OutputValue' cf-status-outputs.json)" >> $GITHUB_ENV
          echo "APP_CONFIG_CONFIGURATION_PROFILE_ID=$(jq -r '.Outputs[] | select(.OutputKey=="ConfigurationProfileId") | .OutputValue' cf-status-outputs.json)" >> $GITHUB_ENV
          echo "APP_CONFIG_CONFIGURATION_VERSION=$(jq -r '.Outputs[] | select(.OutputKey=="ConfigurationVersion") | .OutputValue' cf-status-outputs.json)" >> $GITHUB_ENV

      - name: Start AWS AppConfig Deployment if change occurred
        if: env.NO_CHANGES != 'true' && (env.STACK_STATUS == 'UPDATE_COMPLETE' || env.STACK_STATUS == 'CREATE_COMPLETE')
        run: |
          aws appconfig start-deployment \
            --application-id ${{ env.APP_CONFIG_APPLICATION_ID }} \
            --environment-id ${{ env.APP_CONFIG_ENVIRONMENT_ID }} \
            --configuration-profile-id ${{ env.APP_CONFIG_CONFIGURATION_PROFILE_ID }} \
            --configuration-version ${{ env.APP_CONFIG_CONFIGURATION_VERSION }} \
            --deployment-strategy-id AppConfig.AllAtOnce
