name: Deploy Gestalt Client
on:
  push:
    branches: ["main"]
    paths-ignore:
      - 'deployment/cloudformation/app-config.yaml'
      - '.github/workflows/deploy-app-config.yaml'

permissions:
  id-token: write
  contents: read

env:
  PROD_REGION: us-east-2

jobs:
  Gestalt-Client:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::505912074795:role/github-actions-assume-role
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.PROD_REGION }}

      - name: Create or Update ECR Repository
        run: |
          aws cloudformation deploy \
            --template-file ./deployment/cloudformation/ecr-repository.yaml \
            --stack-name gestalt-client-ecr \
            --parameter-overrides \
              RepositoryName=gestalt-client-app \
            --capabilities CAPABILITY_IAM \
            --no-fail-on-empty-changeset

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Install kubectl
        uses: azure/setup-kubectl@v4
        with:
          # version: 'v1.31.0'
          version: 'latest'
        id: install

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name gestalt-prod-cluster

      # - name: Debug kubeconfig
      #   run: |
      #     cat ~/.kube/config

      - name: Build and push the tagged docker image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: gestalt-client-app
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Apply Kubernetes manifests
        run: |
          kubectl apply -k ./deployment

      - name: Rollout restart
        run: |
          kubectl rollout restart deploy gestalt-client
