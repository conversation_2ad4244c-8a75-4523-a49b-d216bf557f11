name: Deploy Gestalt Client
on:
  push:
    branches: ["main"]
    paths-ignore:
      - 'deployment/cloudformation/app-config.yaml'
      - '.github/workflows/deploy-app-config.yaml'

permissions:
  id-token: write
  contents: read
  deployments: write

env:
  PROD_REGION: us-east-2

jobs:
  Gestalt-Client:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create GitHub deployment
        id: deployment
        uses: actions/github-script@v7
        with:
          script: |
            const { data: deployment } = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'production',
              description: `Deploy ${context.ref.replace('refs/heads/', '')} to production`,
              auto_merge: false,
              required_contexts: []
            });
            return deployment.id;

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::505912074795:role/github-actions-assume-role
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.PROD_REGION }}

      - name: Create or Update ECR Repository
        run: |
          aws cloudformation deploy \
            --template-file ./deployment/cloudformation/ecr-repository.yaml \
            --stack-name gestalt-client-ecr \
            --parameter-overrides \
              RepositoryName=gestalt-client-app \
            --capabilities CAPABILITY_IAM \
            --no-fail-on-empty-changeset

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Install kubectl
        uses: azure/setup-kubectl@v4
        with:
          # version: 'v1.31.0'
          version: 'latest'
        id: install

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name gestalt-prod-cluster

      # - name: Debug kubeconfig
      #   run: |
      #     cat ~/.kube/config

      - name: Build and push the tagged docker image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: gestalt-client-app
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build --no-cache -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Update production kustomization with new image tag
        env:
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          cd deployment/prod
          sed -i "s/newTag: image_tag/newTag: $IMAGE_TAG/" kustomization.yaml

      - name: Apply Kubernetes manifests
        run: |
          kubectl apply -k ./deployment/prod

      - name: Wait for deployment
        run: |
          # Wait for deployment rollout to complete
          kubectl rollout status deploy gestalt-client --timeout=300s

      - name: Update deployment status
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ steps.deployment.outputs.result }},
              state: '${{ job.status == 'success' && 'success' || 'failure' }}',
              environment_url: 'https://client.gestalttech.com'
            });
