name: Deploy Gestalt Client - Staging
on:
  workflow_dispatch:

permissions:
  id-token: write
  contents: read
  deployments: write

env:
  PROD_REGION: us-east-2

jobs:
  Gestalt-Client-Staging:
    runs-on: ubuntu-latest
    if: github.actor != 'dependabot[bot]'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create GitHub deployment
        id: deployment
        uses: actions/github-script@v7
        with:
          script: |
            const { data: deployment } = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'staging',
              description: `Deploy ${context.ref.replace('refs/heads/', '')}`,
              auto_merge: false,
              required_contexts: []
            });
            return deployment.id;

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::505912074795:role/github-actions-assume-role
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.PROD_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Install kubectl
        uses: azure/setup-kubectl@v4
        with:
          version: 'latest'
        id: install

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name gestalt-prod-cluster

      - name: Create staging namespace
        run: |
          kubectl create namespace staging --dry-run=client -o yaml | kubectl apply -f -

      - name: Build and push the tagged docker image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: gestalt-client-app
          IMAGE_TAG: staging-${{ github.sha }}
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          SHA="$GITHUB_SHA"
          docker build --no-cache \
            --build-arg BRANCH_NAME="$BRANCH_NAME" \
            --build-arg SHA="$SHA" \
            -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Update staging kustomization with new image tag
        env:
          IMAGE_TAG: staging-${{ github.sha }}
        run: |
          cd deployment/staging
          sed -i "s/newTag: image_tag/newTag: $IMAGE_TAG/" kustomization.yaml

      - name: Apply Kubernetes manifests for staging
        run: |
          kubectl apply -k deployment/staging

      - name: Wait for deployment
        run: |
          # Wait for deployment rollout to complete
          kubectl rollout status deploy gestalt-client -n staging --timeout=300s

      - name: Update deployment status
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ steps.deployment.outputs.result }},
              state: '${{ job.status == 'success' && 'success' || 'failure' }}',
              environment_url: 'https://staging.gestalttech.com'
            });
