{"name": "gestalt-client", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run generate-ssm-keys && next dev", "build": "npm run generate-ssm-keys && next build", "start": "next start", "lint": "next lint && tsc --noEmit", "plop": "plop", "test": "jest", "ci": "npm run lint", "cli": "npx tsx src/cli/index.ts", "generate-ssm-keys": "npx tsx deployment/local/aws-secrets-to-ssm-keys.ts"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@aws-sdk/client-appconfigdata": "^3.474.0", "@aws-sdk/client-s3": "^3.515.0", "@aws-sdk/client-ssm": "^3.454.0", "@aws-sdk/lib-storage": "^3.617.0", "@aws-sdk/s3-request-presigner": "^3.574.0", "@dagrejs/dagre": "^1.1.4", "@dotlottie/react-player": "^1.6.15", "@hello-pangea/dnd": "^18.0.1", "@omni-co/embed": "^0.10.0", "@slack/web-api": "^7.0.4", "@tisoap/react-flow-smart-edge": "^3.0.0", "@types/bunyan": "^1.8.11", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@xyflow/react": "^12.4.2", "antd": "^5.9.1", "auth0": "^4.18.0", "autoprefixer": "10.4.14", "axios": "^1.9.0", "bunyan": "^1.8.15", "daisyui": "^2.51.6", "dayjs": "^1.11.9", "eslint": "^8.56.0", "eslint-config-next": "13.4.1", "formidable": "^3.5.1", "formik": "^2.4.6", "next": "^14.2.30", "pg": "^8.11.1", "postcss": "8.4.34", "react": "18.2.0", "react-apexcharts": "^1.7.0", "react-archer": "^4.3.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-helmet": "^6.1.0", "react-rewards": "^2.1.0", "react-select": "^5.8.0", "react-switch": "^7.0.0", "react-tooltip": "^5.28.0", "reactjs-popup": "^2.0.6", "snowflake-sdk": "^2.0.4", "sqs-consumer": "^11.6.0", "sqs-producer": "^6.0.1", "tailwindcss": "3.3.2", "uuid": "^10.0.0", "vorpal": "^1.12.0", "xlsx-populate": "^1.21.0", "zustand": "^5.0.5"}, "overrides": {"lodash": "4.17.21", "form-data": "4.0.4", "@ant-design/charts": {"antd": "^5.9.1", "@ant-design/icons": "^5.2.6"}}, "devDependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.1.2", "@types/js-yaml": "^4.0.9", "@types/react": "18.2.42", "@types/snowflake-sdk": "^1.6.20", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "js-yaml": "^4.1.0", "plop": "^4.0.0", "ts-node": "^10.9.2", "tsx": "^4.15.6", "typescript": "^5.3.2"}}