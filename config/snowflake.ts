export default function snowflakeConfig() {
  return {
    username: process.env.SN<PERSON>FLAKE_USERNAME ?? '',
    password: process.env.SNOWFLAKE_PASSWORD ?? '',
    role: process.env.SNOWFLAKE_ROLE ?? '',
    warehouse: process.env.SNOWFLAKE_WAREHOUSE ?? '',
    account: process.env.SNOWFLAKE_ACCOUNT ?? '',
    application: 'gestalt-client',
    clientDb: process.env.NODE_ENV === 'production' ? 'gestalt_client_db' : 'gestalt_client_db_qa',
  };
}
