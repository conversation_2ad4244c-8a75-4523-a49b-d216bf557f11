import slackConfig from './slack';
import fivetranConfig from './fivetran';
import queueConfig from './queue';
import omniConfig from './omni';
import pandaDocConfig from './pandaDoc';
import snowflakeConfig from './snowflake';
import awsConfig from './aws';
import appConfigConfig from './appConfig';
import mapperConfig from './mapper';
import authConfig from './auth';

// config can't be defined statically because env vars are loaded
// programmatically, but getters lets you reference it like config.slack.token
const config = {
  get slack() {
    return slackConfig();
  },
  get fivetran() {
    return fivetranConfig();
  },
  get queue() {
    return queueConfig();
  },
  get omni() {
    return omniConfig();
  },
  get pandaDoc() {
    return pandaDocConfig();
  },
  get snowflake() {
    return snowflakeConfig();
  },
  get aws() {
    return awsConfig();
  },
  get appConfig() {
    return appConfigConfig();
  },
  get mapper() {
    return mapperConfig();
  },
  get auth() {
    return authConfig();
  },
};

export default config;
