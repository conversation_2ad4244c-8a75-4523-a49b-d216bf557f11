export default function queueConfig() {
  const isProd = process.env.NODE_ENV === 'production';

  // Default account id for localstack mock queues
  const awsAccountId = isProd ? process.env.AWS_ACCOUNT_ID : '************';

  const queueBaseUrl = isProd
    ? `https://sqs.us-east-2.amazonaws.com/${awsAccountId}`
    : `http://sqs.us-east-2.localhost.localstack.cloud:4566/${awsAccountId}`;

  return {
    queues: {
      client: {
        url: `${queueBaseUrl}/gestalt-client-queue`,
        region: 'us-east-2',
      },
    },
  };
}
