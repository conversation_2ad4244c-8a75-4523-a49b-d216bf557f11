import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { FileInfo } from '@/types';
import { HandleGenericUpload } from '@/jobs/dataSources';

type RequestBody = {
  customerKey: string;
  connectorSlug: string;
  fileInfo: FileInfo[];
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerKey, connectorSlug, fileInfo } = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const job = new HandleGenericUpload({ customerKey, connectorSlug, fileInfo }, ctx);

      await job.enqueue();
    });

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
