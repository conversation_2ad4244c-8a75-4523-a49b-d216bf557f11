import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getMapperResults } from '@/helpers/mapper';

type RequestQuery = {
  dataSourceKey: string;
  customerId: string;
  filterByProduct?: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const query = req.query as RequestQuery;
  const { dataSourceKey, customerId } = query;

  const filterByProduct = query.filterByProduct === 'true';

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const mappingMatches = await getMapperResults(
        { dataSourceKey, customerId, opts: { filterByProduct } },
        ctx,
      );

      res.status(200).json(mappingMatches);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
