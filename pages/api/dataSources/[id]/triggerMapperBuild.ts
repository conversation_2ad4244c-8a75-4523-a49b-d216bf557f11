import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { triggerMapperBuild } from '@/helpers/mapper';
import { TriggerMapperBuild } from '@/types/routes';

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { dataSourceKey, customerDataSourceId } = req.body as TriggerMapperBuild;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      await triggerMapperBuild({ dataSourceKey, customerDataSourceId }, ctx);

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
