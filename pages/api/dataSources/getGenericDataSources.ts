import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getGenericDataSources } from '@/helpers/snowflake/dataSources';

type RequestQuery = {
  customerId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId } = req.query as RequestQuery;

  const app = new App();

  try {
    const dataSources = await app.execute(async (ctx) => {
      const { db } = ctx;

      const connection = await db.connect();

      return await getGenericDataSources(connection, { customerId }, ctx);
    });

    res.status(200).json(dataSources);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
