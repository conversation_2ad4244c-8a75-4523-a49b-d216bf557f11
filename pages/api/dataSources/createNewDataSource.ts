import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { FileInfo, ConnectorSyncFrequency } from '@/types';
import { createNewDataSource } from '@/helpers/snowflake/dataSources';

type RequestBody = {
  customerId: string;
  customSourceName: string;
  sourceType: string;
  recurringEnabled: boolean;
  syncFrequency: ConnectorSyncFrequency;
  fileInfo: FileInfo[];
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const {
    customerId,
    customSourceName,
    sourceType,
    recurringEnabled: syncRecurringEnabled,
    syncFrequency,
    fileInfo,
  } = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      await createNewDataSource(
        { customerId, customSourceName, sourceType, syncRecurringEnabled, syncFrequency, fileInfo },
        ctx,
      );
    });

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
