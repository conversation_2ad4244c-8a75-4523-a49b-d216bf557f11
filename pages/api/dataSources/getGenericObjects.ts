import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { listGenericObjects } from '@/helpers/s3/dataSources';

type RequestQuery = {
  customerKey: string;
  sourceRecordName: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerKey, sourceRecordName } = req.query as RequestQuery;

  const app = new App();

  try {
    const genericObjects = await app.execute(async (ctx) =>
      listGenericObjects({ customerKey, sourceRecordName }, ctx),
    );

    res.status(200).json(genericObjects);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
