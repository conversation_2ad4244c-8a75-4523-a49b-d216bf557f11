import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getCustomerDataSource } from '@/helpers/snowflake/dataSources';

type RequestQuery = {
  dataSourceId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { dataSourceId } = req.query as RequestQuery;

  const app = new App();

  try {
    const dataSource = await app.execute(async (ctx) => {
      return await getCustomerDataSource({ dataSourceId }, ctx);
    });

    res.status(200).json(dataSource);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
