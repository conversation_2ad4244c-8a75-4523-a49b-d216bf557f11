import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getGenericSourceColumns } from '@/helpers/snowflake/dataSources';

type RequestQuery = {
  customerKey: string;
  sourceRecordName: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerKey, sourceRecordName } = req.query as RequestQuery;

  const app = new App();

  try {
    const sourceColumns = await app.execute(async (ctx) => {
      const { db } = ctx;

      const connection = await db.connect();

      return await getGenericSourceColumns(connection, { customerKey, sourceRecordName }, ctx);
    });

    res.status(200).json(sourceColumns);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
