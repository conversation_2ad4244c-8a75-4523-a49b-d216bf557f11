import { withApiAuthRequired, getSession } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import App from 'modules/app';
import { genOmniSession } from '@/helpers/reports';
import { invariant } from '@/utils/error';

type RequestBody = {
  contentId?: string;
  omniOrgName?: string;
  omniSecretPath?: string;
  omniConnectionId?: string;
  appModeEnabled?: boolean;
};

// api endpoint for generating the omni embed url, which requires our omni secret
export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { contentId, omniOrgName, omniSecretPath, omniConnectionId, appModeEnabled } =
    req.body as RequestBody;

  const app = new App();

  await app.execute(async (ctx) => {
    try {
      const session = await getSession(req, res);

      invariant(!!session, 'No session defined');

      const {
        user: { sub, org_name, name },
      } = session!;

      const url = await genOmniSession(
        {
          contentId,
          omniOrgName,
          omniSecretPath,
          omniConnectionId,
          appModeEnabled,
          orgName: org_name,
          userId: sub,
          userName: name,
        },
        ctx,
      );

      res.status(200).json({ url });
    } catch (error: any) {
      console.error({ error, stack: error.stack });

      res.status(400).json(error);
    }
  });
});
