import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import ApiClient from '../../../modules/omni';

const omniClient = new ApiClient();

type RequestBody = {
  modelId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { modelId } = req.body as RequestBody;

  try {
    await omniClient.refreshModel(modelId);

    res.status(200).json({ success: true });
  } catch (error) {
    console.error(error);

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
