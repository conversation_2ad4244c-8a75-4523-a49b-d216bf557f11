import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import { getSourceModelSummaries } from '@/helpers/snowflake/sourceDataModel';
import App from 'modules/app';

type RequestQuery = {
  sourceModelKey: string;
  opts?: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const query = req.query as RequestQuery;

  const { sourceModelKey } = query;

  const opts = query.opts ? JSON.parse(query.opts) : {};

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const sourceModels = await getSourceModelSummaries({ sourceModelKey, opts }, ctx);

      res.status(200).json({ sourceModels });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
