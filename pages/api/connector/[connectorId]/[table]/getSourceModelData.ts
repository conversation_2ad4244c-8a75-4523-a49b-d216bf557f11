import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import {
  getSourceDataDictionary,
  getSourceModelMappings,
} from '@/helpers/snowflake/sourceDataModel';
import { addSourceFieldMappings } from '@/utils/data';

type RequestQuery = {
  modelId: string;
  table: string;
  sourceModelKey: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { modelId, table: modelName, sourceModelKey } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const [sourceDataDictionary, sourceModelMappings] = await Promise.all([
        getSourceDataDictionary({ sourceModelId: modelId }, ctx),
        getSourceModelMappings({ sourceModelKey, modelName }, ctx),
      ]);

      const { sourceFields, sourceMappings } = addSourceFieldMappings({
        sourceDataDictionary,
        sourceModelMappings,
        sourceModelId: modelId,
      });

      res.status(200).json({ sourceFields, sourceMappings });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
