import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { omit, pick, sortBy } from 'lodash';

type RequestQuery = {
  connectorId: string;
  customerId: string;
  sourceModelKey: string;
};

const sqlText = `
  with cte_mapping_counts as (
    select
      split_part(s.value:name, '__', 2) as source_name,
      count(distinct maps.value:target_field) as mapping_count

    from dynamic_transformation.config.models m,
    lateral flatten(input => m.transformations) t,
    lateral flatten(input => get_path(t.value, 'sources')) s,
    lateral flatten(input => get_path(s.value, 'mappings')) maps
    
    where t.key = :1
    and m.origin = 'GESTALT'
    group by 1
  )

  select
    c.connector_id as "connector_id",
    c.connector_type as "connector_type",
    c.display_name as "display_name",
    m.id as "model_id",
    m.name as "model_name",
    coalesce(mc.mapping_count, 0) as "mapping_count"
  
  from fivetran.connectors c
  inner join config.customer_data_sources cds
  on c.id = cds.connector_id
  inner join dynamic_transformation.config.models m
  on m.origin = cds.source_model_key
  left join cte_mapping_counts mc
  on mc.source_name = lower(m.name)
  
  where c.connector_id = :2
  and cds.customer_id = :3
  `;

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { connectorId, customerId, sourceModelKey } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { db } = ctx;

      const connection = await db.connect();

      const rows = await db.executeAsync(connection, {
        sqlText,
        binds: [sourceModelKey, connectorId, customerId],
      });

      const connector = omit(rows?.[0], ['model_name', 'mapping_count', 'model_id']);

      const tables = sortBy(
        rows?.map((row) => pick(row, ['model_name', 'mapping_count', 'model_id'])),
        'model_name',
      );

      res.status(200).json({ connector, tables });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
