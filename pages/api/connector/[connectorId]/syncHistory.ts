import { connect, executeAsync } from '../../../../modules/snowflake';
import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';

type RequestQuery = {
  connectorId: string;
};

type FivetranLogData = {
  event: string;
  data: Record<string, any>;
  created: string;
  connector_type: string;
  connector_id: string;
  connector_name: string;
  sync_id: string;
};

type FivetranLog = {
  time: string;
  type: string;
  data: FivetranLogData;
};

type RequestBody = FivetranLog[];

const insertSql = `
    insert into fivetran.connector_history (
        connector_id, sync_id, event, created, data, status
    )
    select
        :1, :2, :3, :4, parse_json(:5), :6
`;

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    if (req.method !== 'POST') {
      throw new Error('Invalid request method');
    }

    const { connectorId: connectorSlug } = req.query as RequestQuery;
    const historyLogs = req.body as RequestBody;

    const connection = await connect();

    const snowflakeRes = await executeAsync(connection, {
      sqlText: `select id as "id" from fivetran.connectors where connector_id = :1 limit 1`,
      binds: [connectorSlug],
    });

    const connectorId = snowflakeRes[0].id;

    const historyRows = historyLogs
      .filter((log) => log.data.event === 'sync_start' || log.data.event === 'sync_end')
      .map((log) => [
        connectorId,
        log.data.sync_id,
        log.data.event,
        log.time,
        log.data.data ? JSON.stringify(log.data.data) : null,
        log.data.data?.status || null,
      ]);

    for (const historyRow of historyRows) {
      await executeAsync(connection, {
        sqlText: insertSql,
        binds: historyRow,
      });
    }

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json({ error });
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
