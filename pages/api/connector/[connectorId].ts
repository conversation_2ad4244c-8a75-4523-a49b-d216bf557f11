import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getConnectorDetails } from '@/helpers/snowflake/connector';

type RequestQuery = {
  connectorId: string;
  pageNum: string;
  pageSize?: string;
  includeEmpty?: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  // @ts-ignore
  const query = req.query as RequestQuery;
  const { connectorId } = query;

  // handle string params
  const pageNum = parseInt(query.pageNum);
  const pageSize = query.pageSize ? parseInt(query.pageSize) : undefined;
  const includeEmpty = query.includeEmpty === 'true';

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const connector = await getConnectorDetails(
        { connectorSlug: connectorId, pageNum, pageSize, includeEmpty },
        ctx,
      );

      res.status(200).json({ data: connector });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
