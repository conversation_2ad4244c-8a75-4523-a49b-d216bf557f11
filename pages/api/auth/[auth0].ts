import {
  handleAuth,
  handleLogin,
  handleLogout,
  handleCallback,
  getSession,
} from '@auth0/nextjs-auth0';
import { getBaseUrl } from '@/utils/auth';

// set this to the appropriate organization slug to use a different customer site
const LOCAL_HOST_ORG = 'gestalt';

function getOrgName(hostname) {
  if (hostname.startsWith('localhost')) {
    return LOCAL_HOST_ORG;
  }

  const matches = hostname.match(/([^.]*)\.+/i);
  const orgName = Array.isArray(matches) ? matches[1] : null;

  return orgName === 'client' ? 'gestalt' : orgName;
}

export default handleAuth({
  async login(req, res) {
    try {
      // Check if user already has a valid session
      const baseUrl = getBaseUrl(req.headers.host);
      //   console.log('org:', getOrgName(req.headers.host));
      await handleLogin(req, res, {
        authorizationParams: {
          redirect_uri: `${baseUrl}/api/auth/callback`,
          //   prompt: "login",
          response_type: 'code',
          organization: getOrgName(req.headers.host),
          returnTo: baseUrl,
          scope: 'openid profile email',
        },
        returnTo: baseUrl,
      });
    } catch (error) {
      res.status(error.status || 500).end(error.message);
    }
  },
  async logout(request, response) {
    try {
      const { user } = (await getSession(request, response)) || {};

      const baseUrl = getBaseUrl(request.headers.host);
      const logoutUrl = `${process.env.AUTH0_ISSUER_BASE_URL}/v2/logout?returnTo=${baseUrl}&client_id=${process.env.AUTH0_CLIENT_ID}`;
      console.log('logout url', logoutUrl);
      await handleLogout(request, response, {
        // explicitly set returnTo if user is unauthorized:
        returnTo: user == null ? logoutUrl : undefined,
      });
    } catch (error) {
      console.error(error);
      response.status(error.status ?? 500).end(error.message);
    }
  },
  async callback(req, res) {
    try {
      console.log('headers host:', req.headers.host);

      const orgName = getOrgName(req.headers.host);

      if (!orgName) {
        return res.status(400).end('Unable to resolve organization');
      }

      const baseUrl = getBaseUrl(req.headers.host);

      await handleCallback(req, res, {
        redirectUri: `${baseUrl}/api/auth/callback`,
      });
    } catch (error) {
      res.status(error.status || 400).end(error.message);
    }
  },
});
