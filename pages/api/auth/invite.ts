import { handleLogin } from '@auth0/nextjs-auth0';
import { getBaseUrl } from '@/utils/auth';

export default async function invite(req, res) {
  const { invitation, organization, organization_name } = req.query;

  try {
    const { host } = req.headers;
    const currentSubdomain = host.split('.')[0];

    // first, redirect to appropriate organization subdomain
    if (organization_name && organization_name !== currentSubdomain) {
      const searchParams = new URLSearchParams(req.query).toString();
      const redirectUrl = `https://${organization_name}.gestalttech.com/api/auth/invite?${searchParams}`;

      return res.redirect(302, redirectUrl);
    }

    const baseUrl = getBaseUrl(host);

    await handleLogin(req, res, {
      authorizationParams: {
        invitation,
        organization,
        redirect_uri: `${baseUrl}/api/auth/callback`,
        response_type: 'code',
        returnTo: baseUrl,
        scope: 'openid profile email',
      },
      returnTo: baseUrl,
    });

    res.status(200);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400);
  }
}
