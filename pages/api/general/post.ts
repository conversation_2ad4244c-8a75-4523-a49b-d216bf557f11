import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { handlePost } from '@/helpers/web';

type RequestBody = {
  service: string;
  customerKey: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { service, customerKey } = req.body as RequestBody;

  try {
    const app = new App();

    await app.execute(async (ctx) => {
      const { key, url } = await handlePost({ service, customerKey }, ctx);

      res.status(200).json({ key, url });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
