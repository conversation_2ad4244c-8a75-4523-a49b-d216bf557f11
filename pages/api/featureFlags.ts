import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import App from 'modules/app';

// returns the config object (parameters set in AWS AppConfig) for all feature flags
export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { appConfig } = ctx;

      const featureFlagsConfig = await appConfig.getAppConfig();

      res.status(200).json({ featureFlagsConfig });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json({ error });
  }
});
