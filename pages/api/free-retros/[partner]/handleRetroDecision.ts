import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import { connect, executeAsync } from '../../../../modules/snowflake';
import SlackClient from '../../../../modules/slack';

type RequestBody = {
  customerRetroId: string;
  status: 'accepted' | 'declined';
  customerName: string;
  retroName: string;
};

const sqlText = `
  update config.customer_retros
  set status = :1
  where id = :2
`;

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerRetroId, status, customerName, retroName } = req.body as RequestBody;

  const connection = await connect();
  const slack = new SlackClient();

  try {
    await Promise.all([
      executeAsync(connection, {
        sqlText,
        binds: [status, customerRetroId],
      }),
      slack.retro.post('A new customer retro decision is ready.', {
        level: 'neutral',
        fields: [
          { title: 'Customer Name', value: customerName },
          { title: 'Partner Name', value: retroName },
          { title: 'Decision', value: status },
        ],
      }),
    ]);

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
