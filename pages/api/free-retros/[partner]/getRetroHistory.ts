import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { camelCase } from 'lodash';
import { getCustomerRetroHistory } from '@/helpers/snowflake/retros';
import App from 'modules/app';

type RequestQuery = {
  customerId: string;
  partner: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId, partner } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const retroHistory = await getCustomerRetroHistory(
        {
          customerId,
          partner: camelCase(partner),
        },
        ctx,
      );

      res.status(200).json({ retroHistory });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
