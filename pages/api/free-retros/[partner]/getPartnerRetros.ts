import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import { getRetros, getLatestCustomerRetros } from '@/helpers/snowflake/retros';
import { Retro } from '@/types';
import App from 'modules/app';

type RequestQuery = {
  customerId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId } = req.query as RequestQuery;

  try {
    const app = new App();

    await app.execute(async (ctx) => {
      const [partnerRetros, latestCustomerRetros] = await Promise.all([
        getRetros(ctx),
        getLatestCustomerRetros({ customerId }, ctx),
      ]);

      // add history and latest retro info to retros objects
      const retros: Retro[] = partnerRetros.map((retro) => {
        const latestCustomerRetro = latestCustomerRetros.find(
          (latestCustomerRetro) => retro.id === latestCustomerRetro.retroId,
        );

        return {
          ...retro,
          hasHistory: !!latestCustomerRetro,
          latestCustomerRetroStatus: latestCustomerRetro?.status,
          latestCustomerRetroId: latestCustomerRetro?.customerRetroId,
        };
      });

      res.status(200).json({ retros });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
