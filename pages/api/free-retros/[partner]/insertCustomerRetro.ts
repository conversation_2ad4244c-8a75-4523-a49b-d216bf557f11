import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { insertCustomerRetro } from '@/helpers/snowflake/retros';
import { InsertCustomerRetro } from '@/types/routes';

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId, retroId, userName } = req.body as InsertCustomerRetro;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { customerRetroId } = await insertCustomerRetro({ customerId, retroId, userName }, ctx);

      // return id for redirect
      res.status(200).json({ customerRetroId });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
