import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { GenerateRetroData } from '@/jobs/retros';

type RequestBody = {
  customerKey: string;
  partner: string;
  customerRetroId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const reqBody = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const job = new GenerateRetroData(reqBody, ctx);

      await job.enqueue();
    });

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
