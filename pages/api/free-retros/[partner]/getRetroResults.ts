import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { getRetroResults } from '@/helpers/snowflake/retros';
import App from 'modules/app';

type RequestQuery = {
  customerKey: string;
  customerRetroId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const reqQuery = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { resultsData, resultsUrl, additionalResultsUrl } = await getRetroResults(
        reqQuery,
        ctx,
      );

      res.status(200).json({ resultsData, resultsUrl, additionalResultsUrl });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
