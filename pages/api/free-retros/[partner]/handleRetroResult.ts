import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { handleRetroResult } from '@/helpers/snowflake/retros';

type RequestQuery = {
  customerKey: string;
  partner: string;
  customerRetroId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const reqQuery = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { db } = ctx;

      await db.poolTransaction(async () => {
        const { newStatus, resultsUrl } = await handleRetroResult(reqQuery, ctx);

        res.status(200).json({ newStatus, resultsUrl });
      });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
