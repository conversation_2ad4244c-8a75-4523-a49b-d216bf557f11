import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { handleRetroConfigUpdate } from '@/helpers/snowflake/retros';
import { uploadPandaDocAgreement } from '@/helpers/file';

type RequestBody = {
  customerId: string;
  customerKey: string;
  partner: string;
  documentId: string;
  documentName: string;
  retroId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId, customerKey, partner, documentId, documentName, retroId } =
    req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const newRetroConfig = { agreementSigned: true };

      await Promise.all([
        uploadPandaDocAgreement({ customerKey, partner, documentId, documentName }, ctx),
        handleRetroConfigUpdate({ customerId, retroId, newConfig: newRetroConfig }, ctx),
      ]);

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
