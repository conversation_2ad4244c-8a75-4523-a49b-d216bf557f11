import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getPandaAgreementSession } from '@/helpers/pandaDoc';

type RequestQuery = {
  partner: string;
  userEmail: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { partner, userEmail } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { sessionId, documentId, documentName, documentCompleted } =
        await getPandaAgreementSession({ partner, userEmail }, ctx);

      res.status(200).json({ sessionId, documentId, documentName, documentCompleted });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
