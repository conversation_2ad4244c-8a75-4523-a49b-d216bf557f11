import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getDataStudyDoc } from '@/helpers/s3/retros';

type RequestQuery = {
  partner: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { partner } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const base64Doc = await getDataStudyDoc({ partner }, ctx);

      res.status(200).json({ data: base64Doc });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
