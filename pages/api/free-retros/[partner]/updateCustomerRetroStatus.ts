import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { RetroStatus } from '@/types';
import { updateCustomerRetroStatus } from '@/helpers/snowflake/retros';

type RequestBody = {
  customerRetroId: string;
  status: RetroStatus;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerRetroId, status } = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      await updateCustomerRetroStatus({ customerRetroId, status }, ctx);

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
