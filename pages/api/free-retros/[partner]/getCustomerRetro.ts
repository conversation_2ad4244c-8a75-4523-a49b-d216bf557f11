import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { getCustomerRetro } from '@/helpers/snowflake/retros';
import App from 'modules/app';

type RequestQuery = {
  customerRetroId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerRetroId } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const customerRetro = await getCustomerRetro({ customerRetroId }, ctx);

      res.status(200).json(customerRetro);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
