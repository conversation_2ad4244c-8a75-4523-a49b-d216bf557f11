import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import App from 'modules/app';
import { getSourceDataDictionaries } from '@/helpers/snowflake';

type RequestQuery = {
  customerId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const models = await getSourceDataDictionaries({ customerId }, ctx);

      res.status(200).json(models);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
