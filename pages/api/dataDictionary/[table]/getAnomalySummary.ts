import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import { connect, executeAsync } from '../../../../modules/snowflake';

type RequestQuery = {
  customerKey: string;
  table: string;
};

const sqlText = `
  select
    count(*) as "count"
  from ml_application_db.public.anomaly_detection_result
  where table_name = :1
`;

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const query = req.query as RequestQuery;
  const { customerKey, table } = query;

  try {
    const connection = await connect();

    const tableName = `${customerKey}_db.grand_vault.${table}`;

    const [{ count }] = await executeAsync(connection, {
      sqlText,
      binds: [tableName],
    });

    res.status(200).json({ count });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
