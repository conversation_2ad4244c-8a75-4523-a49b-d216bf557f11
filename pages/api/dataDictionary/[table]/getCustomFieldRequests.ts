import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { getCustomerRequests } from '@/helpers/snowflake/customerRequests';
import App from 'modules/app';

type RequestQuery = {
  customerId: string;
  modelId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId, modelId } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const customFieldRequests = await getCustomerRequests(
        {
          customerId,
          kind: 'custom_field',
          statuses: ['requested', 'pending'],
          modelId,
        },
        ctx,
      );

      res.status(200).json({ customFieldRequests });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
