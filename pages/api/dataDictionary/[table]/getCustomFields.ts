import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getCustomFields } from '@/helpers/snowflake/dataDictionary';

type RequestQuery = {
  customerKey: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerKey } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const customFields = await getCustomFields({ customerKey }, ctx);

      res.status(200).json({ customFields });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
