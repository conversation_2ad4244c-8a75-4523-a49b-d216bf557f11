import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { CustomField } from '@/types';
import { addCustomFieldRequest } from '@/helpers/snowflake/customerRequests';

type RequestBody = {
  customerId: string;
  customerName: string;
  customField: CustomField;
  modelName: string;
  modelId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId, customerName, customField, modelName, modelId } = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      await addCustomFieldRequest(
        { customerId, customerName, customField, modelName, modelId },
        ctx,
      );

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
