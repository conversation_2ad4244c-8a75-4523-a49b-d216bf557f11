import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getListTargetModels } from '@/helpers/mapper';
import { GetListTargetModels } from '@/types/routes';

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { customerId, modelType } = req.query as GetListTargetModels;
  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const listModels = await getListTargetModels({ customerId, modelType }, ctx);

      res.status(200).json(listModels);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
