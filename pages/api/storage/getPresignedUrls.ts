import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { ObjectInfo } from '@/types';
import { getPresignedUrls } from '@/helpers/s3';

type RequestQuery = {
  objectInfo: string;
  method: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const query = req.query as RequestQuery;

  try {
    const { method } = query;
    const objectProps = JSON.parse(query.objectInfo) as ObjectInfo[];

    const app = new App();

    await app.execute(async (ctx) => {
      const keyUrls = await getPresignedUrls({ method, objectProps }, ctx);

      res.status(200).json(keyUrls);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
