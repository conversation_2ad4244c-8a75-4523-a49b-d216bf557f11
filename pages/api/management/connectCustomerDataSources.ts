import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON><PERSON><PERSON>equired, getSession } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { ConnectCustomerDataSourcesProps } from '@/types';
import { invariant } from '@/utils/error';
import { ConnectCustomerDataSources } from '@/jobs/dataSources';

// TODO: make admin validation modular, either middleware or helper
export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { dataSources, customerKey } = req.body as ConnectCustomerDataSourcesProps;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { auth } = ctx;
      const session = await getSession(req, res);

      invariant(!!session, 'No session defined');

      const {
        user: { sub },
      } = session!;

      // After retrieving the user's session, we query the management API for the user's roles to check if they are an admin.
      // If not, throw an error to prevent non-admin users from doing admin stuff.
      const isAdmin = await auth.validateAdmin(sub);

      invariant(isAdmin, `User ${sub} is not an administrator`);

      const job = new ConnectCustomerDataSources({ dataSources, customerKey }, ctx);

      await job.enqueue();

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
