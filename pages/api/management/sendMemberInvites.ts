import { NextApiRequest, NextApiResponse } from 'next';
import { with<PERSON>pi<PERSON>uthRequired, getSession } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { SendMemberInvites } from '@/types/routes';
import { invariant } from '@/utils/error';
import { sendMemberInvites } from '@/helpers/management';

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { invites, customerKey } = req.body as SendMemberInvites;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { auth } = ctx;
      const session = await getSession(req, res);

      invariant(!!session, 'No session defined');

      const {
        user: { sub, org_id, name },
      } = session!;

      // After retrieving the user's session, we query the management API for the user's roles to check if they are an admin.
      // If not, throw an error to prevent non-admin users from doing admin stuff.
      const isAdmin = await auth.validateAdmin(sub);

      invariant(isAdmin, `User ${sub} is not an administrator`);

      await sendMemberInvites({ orgId: org_id, inviterName: name, invites, customerKey }, ctx);

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
