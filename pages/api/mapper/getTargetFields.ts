import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getTargetDataDictionary } from '@/helpers/mapper';
import { MapperGetTargetFields } from '@/types/routes';

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { modelId, version } = req.query as MapperGetTargetFields;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const targetFields = await getTargetDataDictionary({ modelId, version }, ctx);

      res.status(200).json(targetFields);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
