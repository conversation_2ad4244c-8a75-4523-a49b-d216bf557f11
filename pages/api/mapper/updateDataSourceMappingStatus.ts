import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { MappingStatus } from '@/types';
import { updateSourceMappingStatus } from '@/helpers/snowflake/dataSources';

type RequestBody = {
  dataSourceId: string;
  mappingStatus: MappingStatus;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { dataSourceId, mappingStatus } = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      await updateSourceMappingStatus({ dataSourceId, mappingStatus }, ctx);

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
