import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { getAllSourceFields } from '@/helpers/mapper';
import { MapperGetSourceFields } from '@/types/routes';

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { origin, version } = req.query as MapperGetSourceFields;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const sourceFields = await getAllSourceFields({ origin, version }, ctx);

      res.status(200).json(sourceFields);
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
