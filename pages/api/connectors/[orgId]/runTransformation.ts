import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import ApiClient from '../../../../modules/fivetran';
import { pollConnectors } from '@/helpers/fivetran';

const fivetranClient = new ApiClient();

// don't poll data oceans connector for now
const EXCLUDED_CONNECTORS = ['boldness_nucleus'];

const TRANSFORMATIONS_TO_RUN = ['leopard_karaoke'];

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { connectorSlugs } = req.body;

  try {
    console.log('Polling connectors');

    const allSyncsComplete = await pollConnectors({
      connectorSlugs: connectorSlugs.filter((slug) => !EXCLUDED_CONNECTORS.includes(slug)),
    });

    console.log({ allSyncsComplete });
    if (allSyncsComplete) {
      console.log('Running transformations');
      // currently hard-coded to mvp transformations
      await Promise.all(
        TRANSFORMATIONS_TO_RUN.map((transformationId) =>
          fivetranClient.runTransformation(transformationId),
        ),
      );
    }

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
