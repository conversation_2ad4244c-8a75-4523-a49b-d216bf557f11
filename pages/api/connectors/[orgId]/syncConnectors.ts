import { NextApiRequest, NextApiResponse } from 'next';
import { with<PERSON>piAuthRequired } from '@auth0/nextjs-auth0';
import ApiClient from '../../../../modules/fivetran';
import { connect, executeAsync } from '../../../../modules/snowflake';

const fivetranClient = new ApiClient();

// don't sync data oceans connector for now
const EXCLUDED_CONNECTORS = ['boldness_nucleus'];

const defilosSql = `
insert into customer_mvp_db.raw.defilos
select *
from customer_mvp_db.raw.defilos_demo;
`;

const lendisoftSql = `
insert into customer_mvp_db.raw.lendisoft
select *
from customer_mvp_db.raw.lendisoft_demo;
`;

const megasysSql = `
insert into customer_mvp_db.raw.megasys
select *
from customer_mvp_db.raw.megasys_demo;
`;

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { connectorSlugs } = req.body;

  try {
    // adding demo files to raw data
    console.log('adding demo files to raw data');
    const connection = await connect();

    await Promise.all([
      executeAsync(connection, {
        sqlText: defilosSql,
      }),
      executeAsync(connection, {
        sqlText: lendisoftSql,
      }),
      executeAsync(connection, {
        sqlText: megasysSql,
      }),
    ]);

    // initiate the fivetran sync
    console.log('syncing connectors');
    await Promise.all(
      connectorSlugs
        .filter((slug) => !EXCLUDED_CONNECTORS.includes(slug))
        .map((slug) => fivetranClient.connectorSync(slug)),
    );

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
