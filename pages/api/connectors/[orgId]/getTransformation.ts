import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import ApiClient from '../../../../modules/fivetran';

const fivetranClient = new ApiClient();

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { transformationId } = req.query;

  try {
    const transformationData = await fivetranClient.fetchTransformation(transformationId);

    res.status(200).json(transformationData);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
