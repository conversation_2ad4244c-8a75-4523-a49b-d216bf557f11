import { NextApiRequest, NextApiResponse } from 'next';
import { withApi<PERSON>uthRequired, getSession } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { invariant } from '@/utils/error';

// This uses the auth0 management API to add roles to the user info we get from the web session. This is used
// to restrict certain front-end activites, like the admin console.
export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { auth } = ctx;
      const session = await getSession(req, res);

      invariant(!!session, 'No session defined');

      const { user } = session!;

      invariant(!!user, 'No user found in session');

      const roles = await auth.getUserRoles(user.sub);

      res
        .status(200)
        .json({ ...user, roles, isAdmin: roles.find((role) => role.name === 'admin') });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
