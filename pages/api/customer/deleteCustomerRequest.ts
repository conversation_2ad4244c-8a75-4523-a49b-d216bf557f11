import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import App from 'modules/app';
import { deleteCustomerRequest } from '@/helpers/snowflake/customerRequests';

type RequestBody = {
  requestId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { requestId } = req.body as RequestBody;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { db } = ctx;

      const connection = await db.connect();

      await deleteCustomerRequest(connection, { requestId }, ctx);

      res.status(200).json({ success: true });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
