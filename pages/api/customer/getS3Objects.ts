import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import S3Client from '../../../modules/aws-s3';

const filterObjects = (contents: Record<string, any>[], startDate?: string, endDate?: string) =>
  contents.filter((obj) => {
    let condition = true;

    if (startDate) {
      condition =
        condition && new Date(obj.LastModified).getTime() >= new Date(startDate).getTime();
    }

    if (endDate) {
      condition = condition && new Date(obj.LastModified).getTime() <= new Date(endDate).getTime();
    }

    return condition;
  });

type RequestQuery = {
  bucket: string;
  // prefix to filter on
  prefix?: string;
  limit?: string;
  // ISO dates for filtering results
  startDate?: string;
  endDate?: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { bucket, prefix, limit, startDate, endDate } = req.query as RequestQuery;

  const s3Client = new S3Client();

  try {
    const contents = await s3Client.listBucketObjects({
      bucket,
      prefix,
      limit: limit ? parseInt(limit) : undefined,
    });

    // Unfortunately, the s3 sdk doesn't have params to filter by date in the query, so you have to get
    // the full list of objects and filter manually. That makes this a bit fragile in situations where there are
    // a very large number of objects, so use with caution. Without setting a very high limit, you can't be guaranteed
    // that you have the full list of objects before you filter.
    const filtered = filterObjects(contents, startDate, endDate);

    console.log({ objectCount: filtered.length });

    res.status(200).json(filtered);
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
