import { NextApiRequest, NextApiResponse } from 'next';
import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { getCustomer } from '@/helpers/snowflake/customer';
import { getDataSourcesByAuthId } from '@/helpers/snowflake/dataSources';
import App from 'modules/app';

type RequestQuery = {
  authOrgId: string;
};

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { authOrgId } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { appConfig } = ctx;

      const [featureFlagsConfig, customer, dataSources] = await Promise.all([
        appConfig.getAppConfig(),
        getCustomer({ authOrgId }, ctx),
        getDataSourcesByAuthId({ authOrgId }, ctx),
      ]);

      res.status(200).json({ featureFlagsConfig, customer, dataSources });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});
