import { withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import App from 'modules/app';

type RequestQuery = {
  orgId: string;
};

// join customer info with connectors to get list of a customer's connector types (used in model transformation filtering)
const sqlText = `
  select 
    id as "id"
    , name as "name"
    , key as "key"
    , slug as "slug"
    , auth_id as "auth_id"
    , dashboards as "dashboards"
    , fivetran_destination_id as "fivetran_destination_id"
    , fivetran_transformation_id as "fivetran_transformation_id"
    , config as "config"
  from config.customers
  where auth_id = :1
`;

export default withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse) => {
  const { orgId } = req.query as RequestQuery;

  const app = new App();

  try {
    await app.execute(async (ctx) => {
      const { db } = ctx;

      const connection = await db.connect();

      const [customerInfo] = await db.executeAsync(connection, {
        sqlText,
        binds: [orgId],
      });

      res.status(200).json({ customerInfo });
    });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    res.status(400).json(error);
  }
});

export const config = {
  api: {
    externalResolver: true,
  },
};
