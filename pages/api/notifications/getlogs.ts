import { connect } from 'modules/snowflake';
import { withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';

type RequestQuery = {
  fivetranDestId: string;
};

const daysBack = 7;
const minutesBack = -1 * Math.abs(60 * 24 * daysBack);

console.log(`minutesBack: ${minutesBack}`);

const db = 'GESTALT_CLIENT_DB';

const sqlText = `
SELECT top 1000 l.CONNECTOR_ID, TRANSFORMATION_ID, TIME_STAMP, EVENT, MESSAGE_EVENT, MESSAGE_DATA
from ${db}.fivetran_log.log l
join ${db}.fivetran_log.connector c
    on l.CONNECTOR_ID = c.CONNECTOR_ID
where c.destination_id = :1
  and event in ('WARNING', 'SEVERE')
  and TIME_STAMP > DATEADD(minute, :2, CURRENT_TIMESTAMP)
order by TIME_STAMP desc;`;

export default withApiAuthRequired(async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    console.log('connecting to snowflake');

    const { fivetranDestId } = req.query as RequestQuery;

    await connect()
      .then((conn) => {
        console.log('query run');
        conn.execute({
          sqlText: sqlText,
          binds: [fivetranDestId, minutesBack],
          streamResult: true,
          complete: function (err, stmt) {
            streamRows(err, stmt, res);
          },
        });
      })
      // @ts-ignore
      .catch((error, res) => {
        console.log(`caught getlogs error: ${error.message}`);
        res.status(error.status || 500).json({ error });
      });
  } catch (error) {
    res.status(error.status || 500).json({ error });
  }
});

function streamRows(err, stmt, res) {
  const stream = stmt.streamRows();

  const rows: any[] = [];
  // Read data from the stream when it is available
  stream
    .on('readable', function (row) {
      console.log('stream readable');
      while ((row = this.read()) !== null) {
        console.log(row);
        rows.push(row);
      }
    })
    .on('end', function () {
      console.log('stream end');
      res.status(200).json(rows);
    })
    .on('error', function (err) {
      console.error('Failed to execute statement due to the following error: ' + err.message);
      res.status(err.status || 500).json({ err });
    });
}

export const config = {
  api: {
    externalResolver: true,
  },
};
