@startuml Container-fivetran-hook

title
Fivetran Connector Sync Process
end title

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

!define ICONURL https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/v2.4.0
!includeurl ICONURL/common.puml
!includeurl ICONURL/weather/snowflake_cold.puml

LAYOUT_LEFT_RIGHT()

!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v16.0/dist
!include AWSPuml/AWSCommon.puml
!include AWSPuml/Compute/Lambda.puml
!include AWSPuml/ApplicationIntegration/APIGateway.puml
!include AWSPuml/Storage/SimpleStorageService.puml

Component_Ext(Fivetran, "Fivetran", "sync_start\nsync_end\ncreate_connector\n{modify}_connector")
Component_Ext(FivetranWebhook, "Fivetran\nWebhooks Api", "Http")
Component_Ext(FivetranApi, "Fivetran\nConnectors Api", "Http")
Lambda(fivetranhook, "fivetran-hook-\nprod-stack-FivetranWebhookFunction", "AWS Lambda")
APIGateway(postConnHistory, "/Prod/connector-history", "AWS Api Gateway", "fivetran-hook-prod-stack")
WEATHER_SNOWFLAKE_COLD(sp_history, "GESTALT_CLIENT_DB\n.FIVETRAN\n.sp_history", "database", "lightblue")
WEATHER_SNOWFLAKE_COLD(sp_connector, "GESTALT_CLIENT_DB\n.FIVETRAN\n.sp_connector", "database", "lightblue")
Component(sync_startend, "event==sync_start\nevent==sync_end")
Component(sync_connector, "event==sync_end\nevent==create_connector\nevent=={modify}_connector")

Fivetran ..> FivetranWebhook
Rel(FivetranWebhook, postConnHistory, "POST")
Rel(postConnHistory, fivetranhook, "triggers")

Rel(fivetranhook, sync_startend, "")
Rel(sync_startend, sp_history, "CALL")

Rel(fivetranhook, sync_connector, "")
Rel(sync_connector, sp_connector, "CALL")
Rel_Back(sync_connector, FivetranApi, "GET")

@enduml