@startuml System-file-upload-event

title
File Count and Last File Received Process
end title

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_LEFT_RIGHT()

!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v16.0/dist
!include AWSPuml/AWSCommon.puml
!include AWSPuml/Compute/Lambda.puml
!include AWSPuml/ApplicationIntegration/EventBridgeRule.puml
!include AWSPuml/Storage/SimpleStorageService.puml

!define ICONURL https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/v2.4.0
!includeurl ICONURL/common.puml
!includeurl ICONURL/weather/snowflake_cold.puml

System_Ext(dataSource, "Data Source")
Lambda(fileupload, "file-upload-event-prod-FileUploadEventFunction", "AWS Lambda")
SimpleStorageService(s3, "customer-<bucket>", "AWS S3")
EventBridgeRule(eb, "s3 suffix: .json", "AWS Event Bridge", "Rule")
WEATHER_SNOWFLAKE_COLD(sp_file_received, "GESTALT_CLIENT_DB\n.FIVETRAN\n.sp_file_received", "database", "lightblue")

Rel(dataSource, s3, "upload\n.json file")
Rel(s3, eb, "")
Rel(eb, fileupload, "triggers")
Rel(fileupload, sp_file_received, "CALL")

@enduml