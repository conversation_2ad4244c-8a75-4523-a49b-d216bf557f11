@startuml System-data-source-run-history

title
Data Source Run History
end title

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v16.0/dist
!include AWSPuml/AWSCommon.puml
!include AWSPuml/Storage/SimpleStorageService.puml

!define ICONURL https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/v2.4.0
!includeurl ICONURL/common.puml
!includeurl ICONURL/weather/snowflake_cold.puml

' LAYOUT_TOP_DOWN()
LAYOUT_LEFT_RIGHT()

System_Ext(dataSource, "Data Source", "")
SimpleStorageService(s3, "customer-bucket", "AWS S3")
WEATHER_SNOWFLAKE_COLD(snowflake, "Snowflake", "database", "lightblue")

Rel(dataSource, s3, "Total times files received + 1\n- - - - -\nLast received = NOW()")
Rel(s3, snowflake, "'Frequency' (6 hours)\n- - - - -\nCreate Run history for each 'sync', Runtime = NOW() ")

@enduml