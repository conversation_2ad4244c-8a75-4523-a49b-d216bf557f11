@startuml System-gestalt-client

title
Gestalt Web Client
end title

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

!define ICONURL https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/v2.4.0
!includeurl ICONURL/common.puml
!includeurl ICONURL/weather/snowflake_cold.puml

LAYOUT_TOP_DOWN()

Component(client, "Gestalt Client", "nextjs")
WEATHER_SNOWFLAKE_COLD(snowflake, "GESTALT_CLIENT_DB", "database", "lightblue")

Rel(client, snowflake, "")

@enduml