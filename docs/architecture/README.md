# C4 Plant UML Docs

## [C4 Model](https://c4model.com/)

A model for defining sets of hierarchical diagrams (system context, containers, components, and code) at various levels
of detail.

System is the highest level of abstraction, and least detailed. Code is the lowest level, most detailed, but
typically only created by tooling, such as an IDE.

Container is the most common required level of detail, but for some parts of the system, Component level makes sense if it adds value.

## [Plant UML](https://plantuml.com/)

Diagram creation as code. There is a [VSCode
extension](https://marketplace.visualstudio.com/items?itemName=jebbs.plantuml) that allows for live previewing as you
code as well as exporting .puml files to png image files.

## C4 Plant UML

[C4](https://c4model.com/) + [PlantUML](https://plantuml.com/) =
[C4-PlantUML](https://github.com/plantuml-stdlib/C4-PlantUML)

Provides a set of macros and VSCode snippets to make creating C4 diagrams with PlantUML easier.