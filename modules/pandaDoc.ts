import axios from 'axios';
import { forEach } from 'lodash';
import { poll } from '@/utils/async';
import config from 'config';

const BASE_URL = 'https://api.pandadoc.com/public/v1';

const SENT_STATUSES = ['document.sent', 'document.viewed', 'document.completed'];

type ExecuteProps = {
  method: string;
  path: string;
  data?: Record<string, any>;
  queryParams?: Record<string, string>;
  opts?: Record<string, any>;
};

type ListProps = {
  templateId?: string;
  userEmail?: string;
};

type Recipient = {
  email: string;
  first_name?: string;
  last_name?: string;
  role: string; // role required for filling fields and signing
};

type CreateDocProps = {
  templateId: string; // template used to generate the document
  docName: string;
  recipients: Recipient[];
  retroPartner: string;
  docFolderId?: string;
  prefillFields?: Record<string, any>;
};

type DocumentResponse = {
  documentId: string;
  status: string;
  name: string;
};

type SessionProps = {
  documentId: string;
  recipientEmail: string;
  sessionLifetime?: number; // seconds the session is valid. Defaults to 3600 (1 hour)
};

type SessionResponse = {
  sessionId: string;
  expiresAt: string;
};

export default class PandaClient {
  apiKey: string;

  constructor() {
    this.apiKey = config.pandaDoc.apiKey!;
  }

  async execute(props: ExecuteProps) {
    const { method, path, data, queryParams, opts = {} } = props;

    console.log(`${method}, ${path}`);

    const response = await axios({
      method,
      baseURL: BASE_URL,
      url: path,
      data,
      params: queryParams,
      headers: {
        Authorization: `API-Key ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      ...opts,
    });

    console.log(response.status);

    if (response.status >= 400) {
      throw Error(response.data);
    }

    return response.data;
  }

  async listDocuments(props: ListProps): Promise<DocumentResponse[]> {
    // optionally filter by template (associated with retro parter) and user email (metadata tag)
    const { templateId, userEmail } = props || {};

    const queryParams: Record<string, any> = {};

    if (templateId) {
      queryParams.template_id = templateId;
    }

    if (userEmail) {
      queryParams.metadata = `metadata_userEmail=${userEmail}`;
    }

    const { results } = await this.execute({
      method: 'GET',
      path: '/documents',
      queryParams,
    });

    return results.map((doc) => ({ documentId: doc.id, name: doc.name, status: doc.status }));
  }

  async getDocument(documentId: string): Promise<DocumentResponse> {
    const { name, status } = await this.execute({
      method: 'GET',
      path: `/documents/${documentId}`,
    });

    return { documentId, name, status };
  }

  async createDocument(props: CreateDocProps): Promise<DocumentResponse> {
    const {
      templateId,
      docName,
      recipients,
      docFolderId,
      retroPartner,
      prefillFields = {},
    } = props;

    const userEmail = recipients[0].email;

    const fields: Record<string, Record<string, any>> = {};

    forEach(prefillFields, (val, key) => {
      fields[key] = { value: val };
    });

    const { id, name, status } = await this.execute({
      method: 'POST',
      path: `/documents`,
      data: {
        name: docName,
        template_uuid: templateId,
        folder_uuid: docFolderId,
        recipients,
        metadata: { retroPartner, userEmail },
        fields,
      },
    });

    return { documentId: id, name, status };
  }

  async sendDocument(documentId): Promise<DocumentResponse> {
    // if document has already been sent, don't send it
    // let status: string
    const existingDoc = await this.getDocument(documentId);

    if (SENT_STATUSES.includes(existingDoc.status)) {
      return existingDoc;
    }

    // poll document to make sure it has the 'draft' status before sending – the creation process is async and initially
    // has a status of 'uploaded'
    const callback = async () => {
      const { status } = await this.getDocument(documentId);

      if (status === 'document.draft') {
        return true;
      }

      return false;
    };

    const { complete, iterationCount } = await poll(callback, { interval: 1000 });

    console.log(`Send document iteration count: ${iterationCount}`);

    if (!complete) {
      throw new Error(
        `Max polling reached before document moved to 'draft' status. Document ID: ${documentId}`,
      );
    }

    const { id, name, status } = await this.execute({
      method: 'POST',
      path: `/documents/${documentId}/send`,
      // don't send email to recipient because we're embedding the signing
      data: { silent: true },
    });

    return { documentId: id, name, status };
  }

  async createDocSession(props: SessionProps): Promise<SessionResponse> {
    const { documentId, recipientEmail, sessionLifetime } = props;

    // poll to make sure document has already been sent
    const callback = async () => {
      const { status } = await this.getDocument(documentId);

      if (SENT_STATUSES.includes(status)) {
        return true;
      }

      return false;
    };

    const { complete, iterationCount } = await poll(callback, { interval: 1000 });

    console.log(`Create session iteration count: ${iterationCount}`);

    if (!complete) {
      throw new Error(
        `Max polling reached before document moved to 'sent' status. Document ID: ${documentId}`,
      );
    }

    const { id, expires_at } = await this.execute({
      method: 'POST',
      path: `/documents/${documentId}/session`,
      data: { recipient: recipientEmail, lifetime: sessionLifetime },
    });

    return { sessionId: id, expiresAt: expires_at };
  }

  async downloadDocument(documentId) {
    return await this.execute({
      method: 'GET',
      path: `/documents/${documentId}/download`,
      opts: {
        responseType: 'arraybuffer',
        responseEncoding: 'binary',
      },
    });
  }
}
