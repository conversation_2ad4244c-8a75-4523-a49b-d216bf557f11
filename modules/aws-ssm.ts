import { SSMClient, GetParametersCommand } from '@aws-sdk/client-ssm';
import { SSM_KEYS } from '@/constants/index';

let lastRefresh: any = null;
const minDelaySecondsBetweenRefreshes = 120;
let keysLoaded = 0;

async function getParameters() {
  const ssm = new SSMClient({ region: process.env.AWS_DEFAULT_REGION });
  const parameterNames = SSM_KEYS.map(key => key.path);

  // SSM GetParameters API can only handle 10 parameters at a time
  const chunkSize = 10;

  // Split parameter names into chunks of 10
  const parameterChunks: string[][] = [];
  for (let i = 0; i < parameterNames.length; i += chunkSize) {
    parameterChunks.push(parameterNames.slice(i, i + chunkSize));
  }

  // Fetch all parameters in parallel, chunk by chunk
  const parameters = await Promise.all(
    parameterChunks.map(async (chunk) => {
      const command = new GetParametersCommand({
        Names: chunk,
        WithDecryption: true,
      });

      const response = await ssm.send(command);
      return response.Parameters || [];
    })
  );

  // Flatten the array of parameter arrays
  return parameters.flat();
}


function mapParamsToEnvironmentVars(parameterList) {
  parameterList.map((parameter) => {
    const key = SSM_KEYS.find(({ path }) => path === parameter.Name);
    if (key) {
      process.env[key.envVar] = parameter.Value;
      keysLoaded++;
    }
    else console.warn(`${parameter.Name} not found in SSM_KEYS`);
  });
}

async function loadEnvVars() {
  try {
    const parameterList = await getParameters();
    // console.debug('param list', parameterList);
    mapParamsToEnvironmentVars(parameterList);
  } catch (err) {
    console.warn(`Can't resolve ssm params, error: ${JSON.stringify(err)}`);
  }
}

export async function refreshAllParameters() {
  if (lastRefresh && Date.now() < lastRefresh + minDelaySecondsBetweenRefreshes) {
    console.info(
      `You must wait at least ${minDelaySecondsBetweenRefreshes} seconds between refreshes. The last refresh was at '${lastRefresh}'. Come back later...`,
    );
    return;
  }
  console.info(`Loading vars...`);
  keysLoaded = 0;

  await loadEnvVars();
  // console.log('env in aws-ssm', JSON.stringify(process.env, null, 2));

  console.info(`Loaded ${keysLoaded} ssm vars.`);
  lastRefresh = Date.now();
}
