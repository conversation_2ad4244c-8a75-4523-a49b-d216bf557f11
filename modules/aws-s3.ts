import {
  S3Client,
  // This command supersedes the ListObjectsCommand and is the recommended way to list objects.
  ListObjectsV2Command,
  PutObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import { FileMetadata, Json } from '@/types';

// 5mb part size
const PARALLEL_PART_SIZE = 1024 * 1024 * 5;

type ListProps = {
  bucket: string;
  prefix?: string;
  limit?: number;
  // key to start after for pagination
  startAfter?: string;
};

type UploadProps = {
  bucket: string;
  key: string;
  body: any;
};

type GenUrlProps = {
  bucket: string;
  key: string;
  method?: string;
};

type DownloadProps = {
  bucket: string;
  key: string;
};

type DownloadResult = {
  code?: number;
  body: any;
};

type CheckExistsResult = {
  code: number;
};

export default class Client {
  client;

  constructor() {
    this.client = new S3Client({ region: process.env.AWS_S3_REGION });
  }

  async listBucketObjects(props: ListProps): Promise<FileMetadata[]> {
    const { bucket, prefix, limit = 1000, startAfter } = props;

    const command = new ListObjectsV2Command({
      Bucket: bucket,
      Prefix: prefix,
      MaxKeys: limit,
      StartAfter: startAfter,
    });

    const res = await this.client.send(command);

    return res.Contents.map((obj) => ({
      key: obj.Key,
      lastModified: obj.LastModified,
      size: obj.Size,
    }));
  }

  async uploadObject(props: UploadProps) {
    const { bucket, key, body } = props;

    const command = new PutObjectCommand({ Bucket: bucket, Key: key, Body: body });

    try {
      return await this.client.send(command);
    } catch (error) {
      console.error({ error, stack: error.stack });

      return null;
    }
  }

  async parallelUpload(props: UploadProps) {
    const { bucket, key, body } = props;

    try {
      const parallelUpload = new Upload({
        client: this.client,
        params: { Bucket: bucket, Key: key, Body: body },
        // (optional) concurrency configuration
        queueSize: 4,
        // (optional) size of each part, in bytes, at least 5MB
        partSize: PARALLEL_PART_SIZE,
        // (optional) when true, do not automatically call AbortMultipartUpload when
        // a multipart upload fails to complete. You should then manually handle
        // the leftover parts.
        leavePartsOnError: false,
      });

      parallelUpload.on('httpUploadProgress', (progress) => {
        console.log(progress);
      });

      await parallelUpload.done();
    } catch (e) {
      console.log(e);

      throw e;
    }
  }

  async genObjectPresignedUrl(props: GenUrlProps): Promise<string> {
    const { bucket, key, method } = props;

    try {
      let command = new GetObjectCommand({
        Bucket: bucket,
        Key: key,
        ResponseContentDisposition: `attachment;`,
      });

      if (method === 'PUT') {
        command = new PutObjectCommand({ Bucket: bucket, Key: key });
      }

      // library typing issue – lib type defs are not correct for getSignedUrl
      // @ts-ignore
      return getSignedUrl(this.client, command, { expiresIn: 3600 });
    } catch (error) {
      console.error({ error, stack: error.stack });

      throw error;
    }
  }

  async downloadObject(props: DownloadProps): Promise<DownloadResult> {
    const { bucket, key } = props;

    const command = new GetObjectCommand({ Bucket: bucket, Key: key });

    try {
      const res = await this.client.send(command);

      return { code: res['$metadata'].httpStatusCode, body: res.Body };
    } catch (error: any) {
      if (error.Code === 'NoSuchKey') {
        console.log('Object not found', props);

        return { code: 404, body: null };
      }
      console.error({ error, stack: error.stack });

      throw error;
    }
  }

  async downloadJson(props: DownloadProps): Promise<Json> {
    const { body } = await this.downloadObject(props);

    const strBody = await body.transformToString();

    return JSON.parse(strBody);
  }

  async checkObjectExists(props: DownloadProps): Promise<CheckExistsResult> {
    const { bucket, key } = props;

    const command = new HeadObjectCommand({ Bucket: bucket, Key: key });

    try {
      const res = await this.client.send(command);

      return { code: res['$metadata'].httpStatusCode };
    } catch (error: any) {
      const code = error['$metadata']?.httpStatusCode;

      if (code === 404) {
        return { code };
      }

      console.error({ error, stack: error.stack });

      throw error;
    }
  }
}
