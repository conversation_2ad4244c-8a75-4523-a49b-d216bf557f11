import { ManagementClient } from 'auth0';
import config from 'config';
import { MemberInvite, UserRole, Auth0Connection } from '@/types';

type OrgInviteProps = {
  orgId: string;
  connectionId?: string;
  invites: MemberInvite[];
  inviterName: string;
};

class Auth {
  client: any;
  appClientId: string;

  constructor() {
    // api client id and secret are used to query the management API; app client id is the identifier
    // for the Gestalt SPA
    const { domain, apiClientId, apiClientSecret, appClientId } = config.auth;

    this.client = new ManagementClient({
      domain,
      clientId: apiClientId!,
      clientSecret: apiClientSecret!,
    });

    this.appClientId = appClientId!;
  }

  async getUser(id: string) {
    const { data } = await this.client.users.get({ id });

    return data;
  }

  async getUserRoles(id: string): Promise<UserRole[]> {
    const { data } = await this.client.users.getRoles({ id });

    return data;
  }

  async validateAdmin(userId: string): Promise<boolean> {
    const userRoles = await this.getUserRoles(userId);

    return !!userRoles.find((role) => role.name === 'admin');
  }

  async getEnabledConnections(orgId: string): Promise<Auth0Connection[]> {
    const { data } = await this.client.organizations.getEnabledConnections({ id: orgId });

    return data;
  }

  async sendOrgInvites(props: OrgInviteProps) {
    const { orgId, connectionId, invites, inviterName } = props;

    await Promise.all(
      invites.map((invite) => {
        const { email, appMetadata } = invite;

        return this.client.organizations.createInvitation(
          { id: orgId },
          {
            client_id: this.appClientId,
            inviter: { name: inviterName },
            invitee: { email },
            app_metadata: appMetadata,
            connection_id: connectionId,
          },
        );
      }),
    );
  }
}

export default Auth;
