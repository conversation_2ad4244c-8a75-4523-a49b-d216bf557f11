import { loadSsmKeysToEnv } from './aws-ssm-keys-to-env';
import { Context } from '@/types/app';
import QueueClient from './aws-sqs';
import JobsClient from './jobs';
import S3Client from './aws-s3';
import FivetranClient from './fivetran';
import OmniClient from './omni';
import PandaDocClient from './pandaDoc';
import SlackClient from './slack';
import SnowflakeClient from './snowflake';
import AppConfigClient from './appConfig';
import MapperClient from './mapper';
import AuthClient from './auth';
import { createLogger } from 'bunyan';

// no NODE_ENV set when running the worker locally, so default to refreshing parameters
export async function handleEnv() {
  if (process.env.NODE_ENV === 'production') {
    console.info('Using env vars from ssm in eks container.');
  } else {
    await loadSsmKeysToEnv();
  }
}

type ServiceHandler = (ctx: Context) => Promise<any>;

export default class App {
  context: Context;

  constructor() {
    this.setContext();
  }

  setContext() {
    const queue = new QueueClient();
    const jobs = new JobsClient(queue);
    const log = createLogger({ name: 'app' });
    const storage = new S3Client();
    const fivetran = new FivetranClient();
    const reports = new OmniClient();
    const documents = new PandaDocClient();
    const slack = new SlackClient();
    const db = new SnowflakeClient();
    const appConfig = new AppConfigClient();
    const mapper = new MapperClient();
    const auth = new AuthClient();

    this.context = {
      queue,
      jobs,
      log,
      storage,
      fivetran,
      reports,
      documents,
      slack,
      db,
      appConfig,
      mapper,
      auth,
    };
  }

  // used for worker and cli to load env vars and set context again, not needed for instantiating app
  // on web requests. Context has to be reset because config is not available until setting the env.
  async init() {
    await handleEnv();

    this.setContext();
  }

  // used for executing services/actions with app context, e.g:
  // await app.execute(async (ctx) => { await ctx.jobs.enqueue([...jobs]) })
  async execute(serviceHandler: ServiceHandler) {
    return await serviceHandler(this.context);
  }
}
