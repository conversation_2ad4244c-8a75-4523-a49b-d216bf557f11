/* eslint no-var: 0 */
import { loadSsmKeysToEnv } from './aws-ssm-keys-to-env';
import { Context } from '@/types/app';
import QueueClient from './aws-sqs';
import JobsClient from './jobs';
import S3Client from './aws-s3';
import FivetranClient from './fivetran';
import OmniClient from './omni';
import PandaDocClient from './pandaDoc';
import SlackClient from './slack';
import SnowflakeClient from './snowflake';
import AppConfigClient from './appConfig';
import MapperClient from './mapper';
import AuthClient from './auth';
import { createLogger } from 'bunyan';

declare global {
  var app: App | undefined;
}

// no NODE_ENV set when running the worker locally, so default to refreshing parameters
export async function handleEnv() {
  if (process.env.NODE_ENV === 'production') {
    console.info('Using env vars from ssm in eks container.');
  } else {
    await loadSsmKeysToEnv();
  }
}

type ServiceHandler = (ctx: Context) => Promise<any>;

export default class App {
  context: Context;

  constructor() {
    // Store the app instance in global namespace to ensure a single initialization (or as few as possible).
    // This conditional ensures both global.app and new App() both return the instance in memory if one exists.
    if (global.app) {
      console.log('[App] Using global app');
      return global.app;
    }

    console.log('[App] Instantiating context');
    this.setContext();

    console.log('[App] Setting global app');
    global.app = this;
  }

  setContext() {
    const queue = new QueueClient();
    const jobs = new JobsClient(queue);
    const log = createLogger({ name: 'app' });
    const storage = new S3Client();
    const fivetran = new FivetranClient();
    const reports = new OmniClient();
    const documents = new PandaDocClient();
    const slack = new SlackClient();
    const db = new SnowflakeClient();
    const appConfig = new AppConfigClient();
    const mapper = new MapperClient();
    const auth = new AuthClient();

    this.context = {
      queue,
      jobs,
      log,
      storage,
      fivetran,
      reports,
      documents,
      slack,
      db,
      appConfig,
      mapper,
      auth,
    };
  }

  // used for worker and cli to load env vars and set context again, not needed for instantiating app
  // on web requests. Context has to be reset because config is not available until setting the env.
  async init() {
    await handleEnv();

    this.setContext();
  }

  // used for executing services/actions with app context, e.g:
  // await app.execute(async (ctx) => { await ctx.jobs.enqueue([...jobs]) })
  async execute(serviceHandler: ServiceHandler) {
    return await serviceHandler(this.context);
  }

  // Warm up snowflake connections to avoid cold-start latency. This can be handled async from
  // API requests to ensure readiness.
  async warmup() {
    await this.execute(async (ctx) => {
      await ctx.db.execute({ sqlText: 'select 1' });
    });
  }
}
