import {
  Connection,
  ConnectionOptions,
  createConnection,
  createPool,
  configure,
} from 'snowflake-sdk';
import { handleSnowflakeError, coalesceBinds } from '@/utils/snowflake';
import config from 'config';

type ExecuteAsyncProps = {
  sqlText: string;
  binds?: any[];
};

export async function connect(): Promise<Connection> {
  const username = process.env.SNOWFLAKE_USERNAME;
  const password = process.env.SNOWFLAKE_PASSWORD;
  const role = process.env.SNOWFLAKE_ROLE;
  const warehouse = process.env.SNOWFLAKE_WAREHOUSE;
  return new Promise((resolve, reject) => {
    const connection = createConnection({
      account: process.env.SNOWFLAKE_ACCOUNT ?? '',
      username: username,
      password: password,
      application: 'gestalt-client',
      role: role,
      warehouse: warehouse,
    });

    try {
      connection.connect(function (err, conn) {
        if (err) {
          console.error('Unable to connect: ' + err.message);
          reject({ message: err.message });
        } else {
          // console.log('Successfully connected to Snowflake.');
          // Optional: store the connection ID.
          // const connection_ID = conn.getId();
          resolve(conn);
        }
      });

      //res.status(200).json({accessToken: accessToken, idToken: idToken});
    } catch (error) {
      reject({ message: error.message });
    }
  });
}

// promisify snowflake execute method so that we can await the results
export async function executeAsync<RowType>(
  connection: Connection,
  executeProps: ExecuteAsyncProps,
): Promise<RowType | any> {
  return new Promise((resolve, reject) => {
    const { sqlText, binds } = executeProps;

    connection.execute({
      sqlText,
      binds,
      complete: (err, stmt, rows) => {
        if (handleSnowflakeError(err)) {
          reject(err);

          return;
        }

        resolve(rows);
      },
    });
  });
}

export async function beginTransaction(connection: Connection) {
  await executeAsync(connection, {
    sqlText: 'begin transaction',
  });
}

export async function commitTransaction(connection: Connection) {
  await executeAsync(connection, {
    sqlText: 'commit',
  });
}

export async function rollbackTransaction(connection: Connection) {
  await executeAsync(connection, {
    sqlText: 'rollback',
  });
}

// This tells the connection pool how often to run eviction checks. At each interval (ms),
// it will check the if the connections are idle and evict if so.
const CONNECTION_EVICTION_RUN_INTERVAL = 60 * 1000;

// Connections are considered idle if they are not used for this length of time (ms). Establishing connections
// comes with significant overhead, so we want connections to remain available.
const CONNECTION_IDLE_THRESHOLD = 10 * 60 * 1000;

const MAX_CONNECTIONS = 10;
// Keep some number of connections warm to reduce cold-start times.
const MIN_CONNECTIONS = 2;

// global pool variable to maintain pool while the server is live
let connectionPool: any;

export default class SnowflakeClient {
  auth: ConnectionOptions;
  connection?: Connection | null;

  constructor() {
    this.auth = {
      username: config.snowflake.username!,
      password: config.snowflake.password!,
      role: config.snowflake.role!,
      warehouse: config.snowflake.warehouse!,
      account: config.snowflake.account!,
      application: config.snowflake.application!,
      database: config.snowflake.clientDb!,
    };

    this.instantiatePool();

    // For local dev, disable using snowflake cached results. This allows us to see worst-case-scenario load times.
    if (config.snowflake.clientDb === 'gestalt_client_db_qa') {
      this.disableCache();
    }

    // sets snowflake to log warnings and above – info level logs create unnecessary noise
    configure({
      logLevel: 'WARN',
    });

    console.log(`[SnowflakeClient] Using database in client: ${this.auth.database}`);
  }

  instantiatePool() {
    if (!connectionPool) {
      console.log('[SnowflakeClient] Creating new connection pool');

      connectionPool = createPool(this.auth, {
        max: MAX_CONNECTIONS,
        min: MIN_CONNECTIONS,
        evictionRunIntervalMillis: CONNECTION_EVICTION_RUN_INTERVAL,
        idleTimeoutMillis: CONNECTION_IDLE_THRESHOLD,
      });
    }
  }

  async connect(): Promise<Connection> {
    return new Promise((resolve, reject) => {
      try {
        const connection = createConnection(this.auth);

        connection.connect(function (err, conn) {
          if (err) {
            console.error('Unable to connect: ' + err.message);
            reject({ message: err.message });
          } else {
            // console.log('Successfully connected to Snowflake.');
            // Optional: store the connection ID.
            // const connection_ID = conn.getId();
            resolve(conn);
          }
        });

        //res.status(200).json({accessToken: accessToken, idToken: idToken});
      } catch (error: any) {
        reject({ message: error.message });
      }
    });
  }

  // promisify snowflake execute method so that we can await the results
  async executeAsync<RowType>(
    connection: Connection,
    executeProps: ExecuteAsyncProps,
  ): Promise<RowType | any> {
    return new Promise((resolve, reject) => {
      const { sqlText, binds } = executeProps;

      connection.execute({
        sqlText,
        binds: binds ? coalesceBinds(binds) : undefined,
        complete: (err, stmt, rows) => {
          if (handleSnowflakeError(err)) {
            reject(err);

            return;
          }

          resolve(rows);
        },
      });
    });
  }

  async executeWithPool<RowType>(executeProps: ExecuteAsyncProps): Promise<RowType | any> {
    return new Promise((resolve, reject) => {
      connectionPool.use(async (connection) => {
        try {
          const res = await this.executeAsync<RowType>(connection, executeProps);

          resolve(res);
        } catch (error: any) {
          reject(error);
        }
      });
    });
  }

  async beginTransaction(connection: Connection) {
    await this.executeAsync(connection, {
      sqlText: 'begin transaction',
    });
  }

  async commitTransaction(connection: Connection) {
    await this.executeAsync(connection, {
      sqlText: 'commit',
    });
  }

  async rollbackTransaction(connection: Connection) {
    await this.executeAsync(connection, {
      sqlText: 'rollback',
    });
  }

  async transaction<T>(connection: Connection, executeFn: () => Promise<T>): Promise<T> {
    try {
      await this.beginTransaction(connection);

      const result = await executeFn();

      await this.commitTransaction(connection);

      return result;
    } catch (error: any) {
      console.error('Error in transaction', { error, stack: error.stack });

      await this.rollbackTransaction(connection);

      throw error;
    }
  }

  async poolTransaction<T>(executeFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      connectionPool.use(async (connection) => {
        try {
          await this.instantiateConnection(connection);

          await this.beginTransaction(connection);

          const result = await executeFn();

          await this.commitTransaction(connection);

          await this.uninstantiateConnection();

          resolve(result);
        } catch (error: any) {
          reject(error);
        }
      });
    });
  }

  async execute(executeProps: ExecuteAsyncProps) {
    if (this.connection) {
      return await this.executeAsync(this.connection, executeProps);
    }

    return this.executeWithPool(executeProps);
  }

  async instantiateConnection(connection: Connection) {
    this.connection = connection;
  }

  async uninstantiateConnection() {
    this.connection = null;
  }

  async disableCache() {
    await this.execute({ sqlText: 'alter session set use_cached_result = false' });
  }

  async enableCache() {
    await this.execute({ sqlText: 'alter session set use_cached_result = true' });
  }
}
