import axios from 'axios';
import { CreateConnectorBody } from '@/types';
import config from 'config';

type FivetranResponse = {
  code: string;
  message: string;
  data: Record<string, any>;
};

class ApiClient {
  auth: any;
  baseUrl: string;

  constructor() {
    this.auth = { username: config.fivetran.apiKey, password: config.fivetran.apiSecret };
    this.baseUrl = config.fivetran.baseUrl;
  }

  async connectorSync(id) {
    const responseData = await this.execute(`post`, `/connectors/${id}/sync`, { force: true });
    return responseData.data;
  }

  async fetchConnector(id) {
    const responseData = await this.execute(`get`, `/connectors/${id}`);

    if (responseData.code === 'Success') {
      return responseData.data;
    } else {
      console.log('error reason : ', responseData.message);
      return null;
    }
  }

  async createConnector(data: CreateConnectorBody): Promise<FivetranResponse> {
    try {
      return await this.execute('post', 'connectors', data);
    } catch (error: any) {
      console.error({ error: error.response, stack: error.stack });

      throw error;
    }
  }

  async fetchDbtProjects(groupId) {
    const responseData = await this.execute('get', `/dbt/projects?group_id=${groupId}`);

    if (responseData.code === 'Success') {
      return responseData.data;
    } else {
      console.log('error reason : ', responseData.message);
      return null;
    }
  }

  async fetchTransformation(transformationId) {
    const responseData = await this.execute('get', `/dbt/transformations/${transformationId}`);

    if (responseData.code === 'Success') {
      return responseData.data;
    } else {
      console.log('error reason : ', responseData.message);
      return null;
    }
  }

  async runTransformation(transformationId) {
    const responseData = await this.execute('post', `/dbt/transformations/${transformationId}/run`);

    if (responseData.code === 'Success') {
      return responseData.data;
    } else {
      console.log('error reason : ', responseData.message);
      return null;
    }
  }

  async execute(
    method: string,
    path: string,
    data?: Record<string, any>,
    headers?: Record<string, any>,
  ) {
    console.log(`${method}, ${path}`);

    if (data) console.log(data);

    const response = await axios({
      method: method,
      baseURL: this.baseUrl,
      url: path,
      auth: this.auth,
      data: data,
      headers,
    });

    console.log(response.status);

    if (response.status >= 400) throw Error(response.data);

    return response.data;
  }
}

export default ApiClient;
