export async function loadService(name: string): Promise<Function> {
  const [directoryName, serviceName] = name.trim().split(/\s*:\s*/);

  try {
    const serviceModule = await import(`@/services/${directoryName}/${serviceName}`);

    const service = serviceModule.default;

    return service;
  } catch (error: any) {
    throw new Error(`Error loading service at ${directoryName}/${serviceName}`);
  }
}
