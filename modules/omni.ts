import axios from 'axios';
import config from 'config';
import {
  embedSsoDashboard,
  embedSsoContentDiscovery,
  EmbedSsoContentDiscoveryProps,
  EmbedSsoWorkbookProps,
  EmbedSessionMode,
  EmbedConnectionRoles,
  EmbedEntityFolderContentRoles,
} from '@omni-co/embed';

type Auth = {
  token: string;
};

type EmbedProps = {
  orgName: string;
  userId: string;
  userName: string;
  omniOrgName?: string;
  omniSecretPath?: string;
};

interface DashboardEmbedProps extends EmbedProps {
  contentId: string;
}

interface ApplicationEmbedProps extends EmbedProps {
  omniConnectionId: string;
  connectionRole?: 'viewer' | 'restricted_querier' | 'querier';
  contentRole?: 'viewer' | 'editor' | 'manager';
}

const omniPermissions = {
  connection: {
    viewer: EmbedConnectionRoles.VIEWER,
    restricted_querier: EmbedConnectionRoles.RESTRICTED_QUERIER,
    querier: EmbedConnectionRoles.QUERIER,
  },
  content: {
    no_access: EmbedEntityFolderContentRoles.NO_ACCESS,
    viewer: EmbedEntityFolderContentRoles.VIEWER,
    editor: EmbedEntityFolderContentRoles.EDITOR,
    manager: EmbedEntityFolderContentRoles.MANAGER,
  },
};

class OmniClient {
  auth: Auth;
  baseUrl: string;

  constructor() {
    this.auth = {
      token: config.omni.apiKey!,
    };

    this.baseUrl = config.omni.baseUrl;
  }

  async execute(method: string, path: string, data?: Record<string, any>) {
    const response = await axios({
      method,
      baseURL: this.baseUrl,
      url: path,
      headers: {
        Authorization: `Bearer ${this.auth.token}`,
      },
      data,
    });

    if (response.status >= 400) {
      throw Error(response.data);
    }

    return response.data;
  }

  async refreshModel(modelId: string) {
    const res = await this.execute('post', `/model/${modelId}/refresh`);

    return res.data;
  }

  async genDashboardEmbed(props: DashboardEmbedProps): Promise<string> {
    const {
      orgName,
      userId,
      userName,
      contentId,
      omniOrgName,
      omniSecretPath = 'OMNI_SECRET',
    } = props;

    const omniSecret = process.env[omniSecretPath] as string;

    const embedProps: EmbedSsoWorkbookProps = {
      contentId,
      externalId: userId,
      name: userName,
      secret: omniSecret,
      // entity defines the organization this embed user belongs to – it determines which shared folder they can access.
      entity: orgName,
      // each omni instance requires an individual org name. Anything in our gestalt instance needs to be 'gestalt'
      organizationName: omniOrgName || 'gestalt',
      prefersDark: config.omni.prefersDark.toString(),
      theme: config.omni.theme,
    };

    return await embedSsoDashboard(embedProps);
  }

  // returns embed url for session
  async genApplicationEmbed(props: ApplicationEmbedProps): Promise<string> {
    const {
      orgName,
      userId,
      userName,
      omniOrgName,
      omniSecretPath = 'OMNI_SECRET',
      omniConnectionId,
      connectionRole = 'viewer',
      contentRole = 'viewer',
    } = props;

    const omniSecret = process.env[omniSecretPath] as string;

    const embedProps: EmbedSsoContentDiscoveryProps = {
      path: 'root',
      externalId: userId,
      name: userName,
      secret: omniSecret,
      entity: orgName,
      organizationName: omniOrgName || 'gestalt',
      prefersDark: config.omni.prefersDark.toString(),
      theme: config.omni.theme,
      // this mode setting enables full Omni embed – you can edit workbooks and view any dashboards.
      mode: EmbedSessionMode.Application,
      // the connection id is found in the settings -> connections page of Omni. Each customer will have their own connection. This id is found in gestalt_client_db.config.customers config column.
      connectionRoles: { [omniConnectionId]: omniPermissions.connection[connectionRole] },
      // this allows customers to edit workbooks in their folder and save new analyses to that folder.
      entityFolderContentRole: omniPermissions.content.no_access,
      entityFolderGroupContentRole: omniPermissions.content.no_access,
    };

    return await embedSsoContentDiscovery(embedProps);
  }
}

export default OmniClient;
