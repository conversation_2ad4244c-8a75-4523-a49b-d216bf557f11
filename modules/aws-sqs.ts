import { Consumer } from 'sqs-consumer';
import { Producer } from 'sqs-producer';
import config from 'config';
import { QueueMessageHandler, Queue, ServicePayload } from '@/types';
import { createMessageHandler, encodeMessage } from '@/utils/queue';

function createQueue<P>(queueName: string): Queue {
  const queueConfig = config.queue.queues[queueName];

  try {
    if (!queueConfig) {
      throw new Error(`Queue ${queueName} not found`);
    }

    return {
      createConsumer(handleMessage: QueueMessageHandler) {
        return Consumer.create({
          region: queueConfig.region,
          queueUrl: queueConfig.url,
          handleMessage: createMessageHandler(queueName, handleMessage),
        });
      },

      createProducer() {
        const producer = Producer.create({
          region: queueConfig.region,
          queueUrl: queueConfig.url,
        });
        return {
          async send(payload: P) {
            // @ts-ignore
            return await producer.send(encodeMessage(payload));
          },
        };
      },
    };
  } catch (error: any) {
    console.error('Error creating queue', { error, stack: error.stack });

    throw error;
  }
}

export default class QueueClient {
  queues: Record<string, Queue>;

  constructor() {
    this.queues = {
      client: createQueue<ServicePayload>('client'),
    };
  }
}
