import axios, { AxiosRequestConfig } from 'axios';
import config from 'config';
import { MappingMatch, ExecuteProps } from '@/types';

type GetResultsProps = {
  dataSourceKey: string;
};

type MapperResults = {
  package_name: string;
  metadata: Record<string, any>;
  mappings: MappingMatch[];
};

type BuildProps = {
  dataSourceKey: string;
};

class MapperClient {
  apiKey: string;
  baseUrl: string;

  constructor() {
    this.apiKey = config.mapper.apiKey;
    this.baseUrl = config.mapper.baseUrl;
  }

  async execute(props: ExecuteProps) {
    const { method, path, queryParams, data } = props;

    const axiosConfig: AxiosRequestConfig = {
      method,
      baseURL: this.baseUrl,
      url: path,
      data: data || '',
      params: queryParams,
      headers: {
        'X-API-Key': this.apiKey,
      },
    };

    const response = await axios(axiosConfig);

    if (response.status >= 400) {
      throw Error(response.data);
    }

    return response.data;
  }

  async getResults(props: GetResultsProps): Promise<MapperResults> {
    const { dataSourceKey } = props;

    const { body } = await this.execute({
      method: 'get',
      path: `build/${dataSourceKey.toUpperCase()}`,
    });

    return body;
  }

  async build(props: BuildProps) {
    const { dataSourceKey } = props;

    return await this.execute({ method: 'post', path: `build/${dataSourceKey}` });
  }

  // returns health status of the api, useful for internal debugging
  async health() {
    return await this.execute({ method: 'get', path: 'health' });
  }
}

export default MapperClient;
