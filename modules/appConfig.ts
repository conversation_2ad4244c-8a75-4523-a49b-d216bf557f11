import {
  AppConfigDataClient,
  GetLatestConfigurationCommand,
  StartConfigurationSessionCommand,
} from '@aws-sdk/client-appconfigdata';
import config from 'config';
import axios from 'axios';

export default class AppConfig {
  async getAppConfigToken(client) {
    const { appId, profileId, envId } = config.appConfig;

    const getSession = new StartConfigurationSessionCommand({
      //get these values from the provisioned appconfig instance
      ApplicationIdentifier: appId,
      ConfigurationProfileIdentifier: profileId,
      EnvironmentIdentifier: envId,
    });

    const { InitialConfigurationToken } = await client.send(getSession);

    return InitialConfigurationToken;
  }

  async getFromAwsSdk(): Promise<JSON> {
    console.log('get AppConfig from sdk...');

    const { region } = config.appConfig;

    const client = new AppConfigDataClient({ region });

    const token = await this.getAppConfigToken(client);

    const command = new GetLatestConfigurationCommand({ ConfigurationToken: token });

    const response = await client.send(command);

    const configStr: string = new TextDecoder().decode(response.Configuration);

    return JSON.parse(configStr);
  }

  async getFromAppConfigAgent(): Promise<JSON> {
    console.log('get AppConfig from local agent...');

    const { appId, envId, profileId } = config.appConfig;

    const url = `http://localhost:2772/applications/${appId}/environments/${envId}/configurations/${profileId}`;

    const { data } = await axios.get(url);

    return data;
  }

  async getAppConfig(): Promise<JSON> {
    const { useSdk } = config.appConfig;

    if (useSdk) {
      return await this.getFromAwsSdk();
    }

    return await this.getFromAppConfigAgent();
  }
}
