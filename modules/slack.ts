import { WebClient } from '@slack/web-api';
import { Status } from '@/types';
import config from '../config';

type Field = {
  title: string;
  value: string;
};

type MessageOptions = {
  title?: string;
  level?: Status;
  fields?: Field[];
  actions?: any[];
};

export default class SlackClient {
  client: any;

  constructor() {
    this.client = new WebClient(config.slack.token);
  }

  async postMessage(channel: string, text: string, options?: MessageOptions) {
    try {
      const attachment = {
        text,
        title: options?.title,
        color: config.slack.theme[options?.level || 'neutral'],
        fields: options?.fields,
        actions: options?.actions,
      };

      if (config.slack.enabled) {
        await this.client.chat.postMessage({ channel, attachments: [attachment] });
      } else {
        console.log(attachment);
      }
    } catch (error) {
      console.error('Failed to send Slack message', { error });
    }
  }

  get retro() {
    return {
      post: async (text: string, options?: MessageOptions) =>
        this.postMessage(config.slack.channels.retros, text, options),
    };
  }

  get customerRequests() {
    return {
      post: async (text: string, options?: MessageOptions) =>
        this.postMessage(config.slack.channels.customerRequests, text, options),
    };
  }
}
