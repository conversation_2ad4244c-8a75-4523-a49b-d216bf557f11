import { SSMClient, GetParametersCommand } from '@aws-sdk/client-ssm';
import { chunk } from 'lodash';

async function fetchSsmParameters(ssmPaths: string[]) {
  const ssm = new SSMClient({ region: process.env.AWS_DEFAULT_REGION });
  const chunks = chunk(ssmPaths, 10);

  const responses = await Promise.all(
    chunks.map((ssmPathsChunk) =>
      ssm.send(
        new GetParametersCommand({
          Names: ssmPathsChunk,
          WithDecryption: true,
        }),
      ),
    ),
  );

  // make sure all ssm parameters from aws-secrets are found in ssm
  const parameters = responses.flatMap((r) => r.Parameters || []);
  const foundPaths = new Set(parameters.map((p) => p.Name));
  const missingPaths = ssmPaths.filter((path) => !foundPaths.has(path));

  if (missingPaths.length > 0) {
    throw new Error(`These parameters are missing from SSM: ${missingPaths.join(', ')}`);
  }

  return parameters;
}

function getSsmKeysFromEnv() {
  return Object.entries(process.env)
    .filter(([key]) => key.startsWith('SSMKEY_'))
    .map(([key, ssmPath]) => ({
      envVar: key.replace('SSMKEY_', ''),
      ssmPath: ssmPath!,
    }));
}

export async function loadSsmKeysToEnv() {
  console.info('Loading SSM parameters...');

  const ssmVars = getSsmKeysFromEnv();

  if (ssmVars.length === 0) {
    console.info('No SSMKEY_ environment variables found');
    return;
  }

  const parameters = await fetchSsmParameters(ssmVars.map((v) => v.ssmPath));

  parameters.forEach((param) => {
    if (!param.Name || !param.Value) {
      console.error(`SSM parameter missing Name or Value:`, param);
      return;
    }
    
    const ssmVar = ssmVars.find((v) => v.ssmPath === param.Name);
    if (ssmVar) process.env[ssmVar.envVar] = param.Value;
  });

  console.info(`Loaded ${ssmVars.length} SSM parameters.`);
}
