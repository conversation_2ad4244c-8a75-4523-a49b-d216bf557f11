import { find } from 'lodash';

const defaultImgStyle = {
  width: '40px',
  filter: 'brightness(0) invert(100%)',
};

const defaultDropdownStyle = {
  width: 20,
  height: 20,
};

type ImgSrc = {
  imgPath: string;
  bgColor: string;
  direction: string;
  imgStyle?: Record<string, any>;
  dropdownStyle?: Record<string, any>;
};

export const imgSrc: Record<string, ImgSrc> & any = {
  'defi los': {
    imgPath: '/img/dataSourceOptions/defi.png',
    bgColor: 'rgb(0, 180, 229)',
    direction: 'inbound',
    connectorTypes: ['defilos'],
  },
  lendisoft: {
    imgPath: '/img/dataSourceOptions/lendisoft.png',
    bgColor: 'rgb(59, 148, 255)',
    direction: 'inbound',
  },
  dataoceans: {
    imgPath: '/img/dataSourceOptions/dataoceans.png',
    bgColor: 'rgb(12, 79, 148)',
    direction: 'outbound',
  },
  filemaker: {
    imgPath: '/img/dataSourceOptions/filemaker.png',
    bgColor: 'rgb(40, 119, 247)',
    direction: 'inbound',
    connectorTypes: ['filemaker_olh'],
  },
  gestalt: {
    imgPath: '/img/Gestalt_Cube.svg',
    alt: 'Gestalt Logo Cube',
  },
  megasys: {
    imgPath: '/img/dataSourceOptions/megasys.png',
    bgColor: 'rgb(5, 82, 161)',
    direction: 'inbound',
    alt: 'Megasys',
  },
  allegro: {
    imgPath: '/img/dataSourceOptions/allegro.png',
    bgColor: 'rgb(124, 178, 206)',
    direction: 'inbound',
    alt: 'Allegro',
  },
  highline: {
    imgPath: '/img/dataSourceOptions/highline.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'Highline',
  },
  trudecision: {
    imgPath: '/img/dataSourceOptions/tru-decision.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'TruDecision',
  },
  ensemblex: {
    imgPath: '/img/dataSourceOptions/ensemblex.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'Ensemblex',
  },
  fingoal: {
    imgPath: '/img/dataSourceOptions/fingoal.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'FinGoal',
  },
  salesforce: {
    imgPath: '/img/dataSourceOptions/salesforce.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'Salesforce',
    imgStyle: { width: '80px' },
    connectorTypes: [
      'salesforce_source_one',
      'salesforce_neighborhood_trust_pathways',
      'salesforce_neighborhood_trust_ntfp',
    ],
  },
  rent_manager: {
    imgPath: '/img/dataSourceOptions/rent-manager.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'Rent Manager',
    imgStyle: { width: '48px' },
    connectorTypes: ['rent_manager', 'rent_manager_metra', 'rent_manager_crow'],
  },
  custom: {
    imgPath: '/img/dataSourceOptions/custom.png',
    bgColor: 'rgb(255, 255, 255)',
    direction: 'inbound',
    alt: 'Custom',
    imgStyle: { width: '80px' },
  },
  loan_pro: {
    imgPath: '/img/dataSourceOptions/loanpro.png',
    bgColor: 'rgb(247, 144, 30)',
    direction: 'inbound',
    alt: 'Loan Pro',
    connectorTypes: ['loanpro', 'loan_pro'],
  },
  ssi: {
    imgPath: '/img/dataSourceOptions/ssi.png',
    bgColor: 'rgb(22, 44, 128)',
    direction: 'inbound',
    alt: 'SSI',
  },
  paramount: {
    imgPath: '/img/dataSourceOptions/paramount.png',
    bgColor: 'rgb(241, 113, 70)',
    direction: 'inbound',
    alt: 'Paramount',
    dropdownStyle: {
      width: 30,
      height: 30,
    },
  },
  autopal: {
    imgPath: '/img/dataSourceOptions/autopal.png',
    bgColor: 'rgb(30, 115, 190)',
    direction: 'inbound',
    alt: 'Autopal',
  },
  launcher: {
    imgPath: '/img/dataSourceOptions/launcher.png',
    imgStyle: { width: '80px' },
    direction: 'inbound',
    alt: 'Launcher',
  },
  sql_server: {
    imgPath: '/img/dataSourceOptions/sql-server.svg',
    imgStyle: { width: '40px' },
    direction: 'inbound',
    alt: 'SQL Server',
  },
  nls: {
    imgPath: '/img/dataSourceOptions/nls.png',
    bgColor: 'rgb(111, 7, 34)',
    direction: 'inbound',
    alt: 'NLS',
  },
  linkmoney: {
    imgPath: '/img/dataSourceOptions/fingoal.png',
    direction: 'inbound',
    alt: 'Link Money',
    imgStyle: {},
  },
  akuvo: {
    imgPath: '/img/dataSourceOptions/akuvo.png',
    direction: 'inbound',
    alt: 'Akuvo',
    imgStyle: {
      width: '50px',
    },
  },
  corelation: {
    imgPath: '/img/dataSourceOptions/corelation.png',
    direction: 'inbound',
    alt: 'Corelation',
    imgStyle: {
      width: '50px',
    },
  },
  origence: {
    imgPath: '/img/dataSourceOptions/origence.png',
    bgColor: 'rgb(0, 0, 0)',
    direction: 'inbound',
    alt: 'Corelation',
    dropdownStyle: {
      width: 20,
      height: 20,
      style: { filter: 'brightness(0)', color: 'rgb(0, 0, 0)' },
    },
  },
  allied: {
    imgPath: '/img/dataSourceOptions/allied.svg',
    direction: 'inbound',
    alt: 'Corelation',
    imgStyle: {},
  },
  _default: {
    imgPath: '/img/dataSourceOptions/data-cloud-icon.png',
    bgColor: 'rgb(0, 180, 229)',
    direction: 'inbound',
    imgStyle: defaultImgStyle,
    dropdownStyle: defaultDropdownStyle,
  },
  getOrDefault(connector_type) {
    const src = find(
      this,
      (val, key) =>
        key === connector_type?.toLowerCase() ||
        key === connector_type?.replace(/\s/g, '').toLowerCase() ||
        val.connectorTypes?.includes(connector_type?.toLowerCase()),
    );

    if (src) {
      return {
        ...src,
        imgStyle: src.imgStyle || defaultImgStyle,
        dropdownStyle: src.dropdownStyle || defaultDropdownStyle,
      };
    }

    return imgSrc._default;
  },
};

export const retroImages = {
  dataOceans: {
    howItWorks: {
      imgPath: '/img/retros/dataOceans/how_it_works.png',
      alt: 'How the DataOceans retro works.',
      style: {
        image: { width: 400, height: 400 },
        container: { backgroundColor: '#eff6ff' },
      },
    },
    solution: {
      imgPath: '/img/retros/dataOceans/solution.png',
      alt: 'DataOceans solution.',
      style: {
        width: 400,
        height: 400,
      },
    },
    solution2: {
      imgPath: '/img/retros/dataOceans/solution2.png',
      alt: 'DataOceans solution.',
      style: {
        width: 400,
        height: 400,
      },
    },
    howItWorksDetails: {
      imgPath: '/img/retros/dataOceans/how_it_works_details.png',
      alt: 'DataOceans info.',
      style: {
        width: 400,
        height: 400,
      },
    },
  },
  fingoal: {
    howItWorks: {
      imgPath: '/img/retros/fingoal/how_it_works.svg',
      alt: 'FinGoal info.',
      style: {
        image: { width: 450, height: 450 },
      },
    },
    solution: {
      imgPath: '/img/retros/fingoal/solution.png',
      alt: 'FinGoal solution.',
      style: {
        width: 400,
        height: 400,
      },
    },
  },
  default: {
    howItWorks: {
      imgPath: '/img/freeretros-info.svg',
      alt: 'How the retro works.',
      style: {
        image: { width: 200, height: 200 },
        container: { width: '400px', backgroundColor: '#eff6ff' },
      },
    },
    solution: {
      imgPath: '/img/freeretros-info-2.png',
      alt: 'Retro solution',
      style: {
        width: 400,
        height: 400,
      },
    },
    howItWorksDetails: {
      imgPath: '/img/freeretros-info-3.png',
      alt: 'Free retros info.',
      style: {
        width: 500,
        height: 500,
      },
    },
  },
  getOrDefault(retroKind: string) {
    const retro = this[retroKind];

    return retro || this.default;
  },
};
