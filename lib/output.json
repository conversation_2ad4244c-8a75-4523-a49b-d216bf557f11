{"LoanApplications": {"LoanApplication": [{"Dealer": {"dealer_type": "Independent", "document_delivery": "Both", "dealer_id": 242205, "client_dealer_id": 12345, "dealer_state": "TX", "name": "DT Test Dealer", "source_dealer_id": 269472, "enrollment_date": "2022-05-26T00:00:00.0000000", "phone_number": 4444444444, "address": "1234 defi Way", "city": "Somewhere", "postal_code": 76087, "email": "<EMAIL>", "active": "True", "create_date": "2022-05-26T15:59:49.5470000", "update_date": "2022-11-21T11:12:39.6630000", "updated_by": "jmcclintick"}, "LoanApplicationStates": {"LoanApplicationState": {"CreditPolicy": {"min_income": 0, "max_pti": 0, "max_dti": 0, "max_af": 0, "min_af": 0, "max_term": 0, "min_payment": 0, "max_vehicle_age": 0, "max_miles": 0, "max_line3_percent": 0, "max_line5_percent": 0, "create_date": "2022-06-07T13:46:35.9530000", "update_date": "2022-06-07T13:46:35.9530000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, "DealDetails": {"Structure": {"scenario_id": 208, "deal_detail_id": 208, "cash_down": 1000, "net_trade": 7666, "unpaid_balance": 27943, "estimated_amount_financed": 30436, "term_months": 72, "warranty": 799, "ltv_ratio": 79.83, "calculated_discount": 0, "max_amount_financed": 0, "item_code": "RECOMMENDED", "pti": 0, "total_down": 8666, "vehicle_max_term": 0, "amount_financed_max_term": 0, "total_income": 12000, "payment": 0, "estimated_payment": 0, "dti": 0, "participation": 0, "discount_percent": 0, "back_end": 699, "max_pti": 0, "max_dti": 0, "sale_price": 34900, "min_income": 0, "min_payment": 0, "max_vehicle_age": 0, "max_miles": 0, "max_line3_percent": 0, "max_line5_percent": 0, "min_af": 0, "ttl_estimate": 99, "rebate": 0, "accidental_health_insurance": 0, "credit_life_insurance": 0, "gap": 995, "sales_tax": 1610, "front_end_fees": 0, "create_date": "2022-06-07T13:46:36.0130000", "update_date": "2022-08-18T09:17:00.9470000", "updated_by": "jmcclintick", "bundle_id": 1082}}, "Customers": {"Customer": {"Addresses": {"Address": [{"address_id": 426, "city": "FANTASY ISLAND", "state_code_id": "IL", "country_code": "USA", "postal_code": 60750, "item_code": "CURRENT", "street_type": "LN", "street_type_description": "LANE", "years_at_address": 14, "months_at_address": 0, "mortgage_rent": 500, "address_type": "CURRENT", "street_number": 525, "street": "COUNTRY", "house_type": "CURRENT", "create_date": "2022-06-07T13:46:35.9900000", "update_date": "2022-06-07T13:46:35.9900000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, {"address_id": 427, "country_code": "USA", "item_code": "PREVIOUS", "mortgage_rent": 500, "address_type": "PREVIOUS", "create_date": "2022-06-07T13:46:35.9930000", "update_date": "2022-06-07T13:46:35.9930000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}]}, "Communications": {"Communication": [{"com_link_id": 1032, "item_code": "HOME", "com": "304-987-4521", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0070000", "update_date": "2022-06-07T13:46:36.0070000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, {"com_link_id": 1033, "item_code": "WORK", "com": "304-987-4111", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0100000", "update_date": "2022-06-07T13:46:36.0100000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, {"com_link_id": 1034, "item_code": "PWORK", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0100000", "update_date": "2022-06-07T13:46:36.0100000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}]}, "Employers": {"Employer": [{"employer_id": 459, "type": "EMPLOYED", "type_code": "CURRENT", "years_employed": 15, "months_employed": 0, "employer_name": "TEST CO", "occupation": "MANAGER", "employment_status": "Employed", "create_date": "2022-06-07T13:46:35.9830000", "update_date": "2022-08-18T09:17:00.9470000", "updated_by": "jmcclintick"}, {"employer_id": 460, "type_code": "PREVIOUS", "employment_status": "None", "create_date": "2022-06-07T13:46:35.9870000", "update_date": "2022-08-18T09:17:00.9470000", "updated_by": "jmcclintick"}]}, "Incomes": {"Income": [{"income_id": 301, "frequency": "MONTHLY", "gross": 12000, "net_source": "MAN", "monthly_income": 12000, "employer_id": 459, "income_type": "PAYCHECK", "create_date": "2022-06-07T13:46:35.9670000", "update_date": "2022-06-07T13:46:35.9770000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, {"income_id": 302, "frequency": "MONTHLY", "gross": 0, "net_source": "MAN", "monthly_income": 0, "employer_id": 459, "income_type": "PAYCHECK", "create_date": "2022-06-07T13:46:35.9730000", "update_date": "2022-06-07T13:46:36.2730000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}], "total_primary_income": 12000, "total_secondary_income": 0, "deal_detail_id": 208, "bundle_id": 1082}, "Expenses": {"Expense": {"entity_type": "PB", "creditor": "HOUSING PAYMENT", "balance": 0, "payment": 500, "expense_id": 1461, "frequency": "MONTHLY", "source": "APP", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "RENT", "item_code": "RENT", "create_date": "2022-06-07T13:46:35.9470000", "update_date": "2022-06-07T13:46:35.9470000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, "total_debt": 500}, "customer_id": 230, "social_security_number": 666393933, "type": "PB", "date_of_birth": "1989-05-25T00:00:00.0000000", "first_name": "LINE", "last_name": "DANCER", "income_monthly": 12000, "total_exp": 0, "is_in_military": "No", "full_name": "LINE DANCER", "age": 34, "create_date": "2022-06-07T13:46:35.0000000", "update_date": "2022-08-18T09:17:01.0000000", "updated_by": "jmcclintick", "deal_detail_id": 208, "bundle_id": 1082}}, "CustomScorecard": {"deal_detail_id": 208, "bundle_id": 1082}, "Vehicles": {"Vehicle": [{"vehicle_id": 364, "source_system_code": "DT", "trade_in": "True", "make": "KIA", "model": "OPTIMA", "year": 2014, "trim": "SEDAN 4D LX I4", "vehicle_age": 9, "active": "True", "create_date": "2022-06-07T13:46:35.9300000", "update_date": "2022-06-07T13:46:36.2530000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, {"Eval": {"collateral_evaluation_id": 343, "collateral_id": 365, "wholesale_value": 38125, "retail_value": 0, "msrp": 0, "used_car_value": 0, "stated_value": 38125, "invoice": 0, "vehicle_value": 38125, "create_date": "2022-06-07T13:46:35.9430000", "update_date": "2022-06-07T13:46:35.9430000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}, "vehicle_id": 365, "source_system_code": "DT", "type": "Used", "trade_in": "False", "make": "MERCEDES-BENZ", "model": "C-CLASS", "year": 2019, "condition": "Used", "odometer": 39930, "trim": "COUPE 2D C300", "certified_used": "False", "vehicle_age": 4, "active": "True", "create_date": "2022-06-07T13:46:35.9300000", "update_date": "2022-06-07T13:46:36.2600000", "updated_by": "System [No User Available]", "deal_detail_id": 208, "bundle_id": 1082}]}, "payment_call_flag": "False", "credit_type": "Individual", "id": 208, "source_system_code": "DT", "received_date": "2022-06-07T13:46:00.0000000", "initial_entry_date": "2022-06-07T13:46:00.0000000", "source_system_dealer_id": 269472, "decision_date": "2022-06-07T13:45:00.0000000", "decision_code": "I", "source_system_id": "BBF3700439", "total_primary_income": 12000, "total_secondary_income": 0, "total_debt": 500, "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T09:17:01.6930000", "updated_by": "jmcclintick", "bundle_id": 1082}}, "deal_type": "REQUESTED", "lender_id": "SA5", "request_date": "2022-06-07T13:46:00.0000000", "payment_call": "False", "source_system_id": "BBF3700439", "source_system": "DT", "version": 1, "deal_detail_id": 208, "source_dealer_id": 269472, "app_status": "I", "perform_dupe_check": 1, "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T09:17:01.6930000", "updated_by": "jmcclintick", "decisioned_by": "jmcclintick", "bundle_id": 1082, "status_last_changed_on": "2022-06-07T13:46:35.9000000"}, {"Dealer": {"dealer_type": "Independent", "document_delivery": "Both", "dealer_id": 242205, "client_dealer_id": 12345, "dealer_state": "TX", "name": "DT Test Dealer", "source_dealer_id": 269472, "enrollment_date": "2022-05-26T00:00:00.0000000", "phone_number": 4444444444, "address": "1234 defi Way", "city": "Somewhere", "postal_code": 76087, "email": "<EMAIL>", "active": "True", "create_date": "2022-05-26T15:59:49.5470000", "update_date": "2022-11-21T11:12:39.6630000", "updated_by": "jmcclintick"}, "LoanApplicationStates": {"LoanApplicationState": {"CreditPolicy": {"min_income": 0, "min_income_result": "Incomplete", "max_pti": 0, "max_pti_result": "Incomplete", "max_dti": 0, "max_dti_result": "Incomplete", "max_af": 0, "max_af_result": "Incomplete", "min_af": 0, "min_af_result": "Incomplete", "max_term": 0, "max_term_result": "Incomplete", "min_payment": 0, "min_payment_result": "Incomplete", "max_vehicle_age": 15, "max_vehicle_age_result": "Passed", "max_miles": 110000, "max_miles_result": "Passed", "max_line3_percent": 0, "max_line3_percent_result": "Incomplete", "max_line5_percent": 150, "max_line5_percent_result": "Passed", "usury": 0, "usury_result": "Incomplete", "create_date": "2022-06-07T13:46:35.9530000", "update_date": "2022-06-07T13:46:47.1700000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, "Stipulations": {"Stipulation": [{"id": 2, "name": "Retail Installment Contract", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 4, "name": "Signed Credit App", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 6, "name": "Signed Buyers Order", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 8, "name": "Title Application", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 10, "name": "Odometer Statement", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 12, "name": "Invoice or Bookout", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 14, "name": "ATPI", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 16, "name": "Ancillary Product Certificates", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 18, "name": "Valid DL or Government Issued ID", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 20, "name": "4 Personal References", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 24, "name": "Proof of Income", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 22, "name": "Proof of Residence", "type": "UW", "status": "Incomplete", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:48:06.9770000", "update_date": "2022-06-07T13:48:06.9770000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}]}, "DealDetails": {"Structure": {"scenario_id": 207, "deal_detail_id": 207, "cash_down": 1000, "net_trade": 7666, "unpaid_balance": 27943, "estimated_amount_financed": 30436, "term_months": 72, "initial_max_apr": "Incomplete", "warranty": 799, "contract_rate": 11.01, "tier_level": 1, "ltv_ratio": 79.83, "calculated_discount": 195, "max_amount_financed": 0, "item_code": "RECOMMENDED", "pti": 5.09, "total_down": 8666, "vehicle_max_term": 0, "amount_financed_max_term": 0, "total_income": 12000, "payment": 611.14, "estimated_payment": 611.14, "dti": 15.58, "participation": 0, "discount_percent": 0.64, "back_end": 699, "max_pti": 0, "max_dti": 0, "max_payment": 0, "sale_price": 34900, "min_income": 0, "min_payment": 0, "max_vehicle_age": 15, "max_miles": 110000, "max_line3_percent": 0, "max_line5_percent": 150, "min_af": 0, "ttl_estimate": 99, "rebate": 0, "accidental_health_insurance": 0, "credit_life_insurance": 0, "gap": 995, "sales_tax": 1610, "front_end_fees": 0, "usury": 0, "create_date": "2022-06-07T13:46:36.0130000", "update_date": "2022-08-18T09:17:00.9530000", "updated_by": "jmcclintick", "bundle_id": 1082}}, "Customers": {"Customer": {"Reports": {"Report": {"active": "True", "valid": "True", "bureau": "TU", "report_date": "2022-06-02T15:20:28.5870000", "score": 740, "raw_xml": "<Response Score=\"740\"><DMSXML><DMSCommon XMLindent=\"true\" Tracking=\"e3704323-36a0-4d28-958e-ad205eb16185\" DMSAppId=\"COM18872893\" DMSAccountNumber=\"TST0418221\" DMSAccountName=\"625293-Solera Af\" DMSAccountId=\"129\" Repull=\"1\" ReturnFormat=\"DMSCommon\" ReturnInputs=\"false\" ReturnAllProducts=\"false\" ReturnInquiries=\"false\" ApplicationDate=\"2022-06-02T15:20:28\" DateGenerated=\"2022-06-02T15:20:28\" Status=\"Ok\"><Products><Product Id=\"Product1\" ProductId=\"3004\" Vendor=\"TransUnion\" ProductName=\"Credit Report\" ProductVersion=\"4.0\" DLLVersion=\"*********\" ReturnResult=\"true\" ReturnFormat=\"TTY\" DateGenerated=\"2022-06-02T15:20:28\" Entities=\"E1\" Status=\"Ok\"><ProductResult Format=\"TTY\">                                                                            &#xD;&#xA;********-36A0-4D28-958E-  TRANSUNION CREDIT REPORT                          &#xD;&#xA;                                                                            &#xD;&#xA;&lt;FOR&gt;          &lt;SUB NAME&gt;          &lt;MKT SUB&gt;  &lt;INFILE&gt;   &lt;DATE&gt;      &lt;TIME&gt; &#xD;&#xA;(I) F ********* SOLERA AUTO         03 PE      9/97      06/02/22    15:20CT&#xD;&#xA;                                                                            &#xD;&#xA;&lt;SUBJECT&gt;                                          &lt;SSN&gt;        &lt;BIRTH DATE&gt;&#xD;&#xA;DANCER, LINE                                       ***********  10/76       &#xD;&#xA;&lt;CURRENT ADDRESS&gt;                                               &lt;DATE RPTD&gt; &#xD;&#xA;525 S. COUNTRY RD., FANTASY ISLAND IL. 60750                     9/21       &#xD;&#xA;&lt;FORMER ADDRESS&gt;                                                            &#xD;&#xA;11 RIDGE WY., FANTASY ISLAND IL. 60750                           1/18       &#xD;&#xA;                                         &lt;POSITION&gt;                         &#xD;&#xA;&lt;CURRENT EMPLOYER AND ADDRESS&gt;                     &lt;VERF&gt; &lt;RPTD&gt;            &#xD;&#xA;DEFI                                     MANAGER                            &#xD;&#xA;                                                    4/22V  4/22             &#xD;&#xA;&lt;FORMER EMPLOYER AND ADDRESS&gt;                                               &#xD;&#xA;USGS                                                                        &#xD;&#xA;                                                    4/22V  4/22             &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;S P E C I A L   M E S S A G E S                                             &#xD;&#xA;****TRUVALIDATE FRAUD ALERTS: CLEAR FOR ALL SEARCHES PERFORMED***           &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;M O D E L   P R O F I L E          * * * A L E R T * * *                    &#xD;&#xA;***VANTAGESCORE 4 SCORE +740  : 38, 49, 44, 50 SCORECARD :07*** IN ADDITION &#xD;&#xA;***TO THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S   &#xD;&#xA;***CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                     &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;P U B L I C   R E C O R D S                                                 &#xD;&#xA;SOURCE    DATE     LIAB     ECOA COURT ASSETS   PAID  DOCKET#               &#xD;&#xA;TYPE                                                  PLAINTIFF/ATTORNEY    &#xD;&#xA;Z 5048079  4/20R   $0       I    FE             7/21  *********             &#xD;&#xA;CH 7 BANKRUPTCY DISMISSED/CLOSED                      JOSEPH B GREEN        &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;T R A D E S                                                                 &#xD;&#xA;SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP &#xD;&#xA;ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24     &#xD;&#xA;ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90  &#xD;&#xA;FNCC         B 41PF037 10/15   $1002                       **********11 R01 &#xD;&#xA;4939394                 4/22V           $0                 1X1111111X11     &#xD;&#xA;I                              $666                           24   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;CITIFINANCIA F 7212205  5/21   $3929    015M262            **********   I01 &#xD;&#xA;800858                  4/22A           $0                                  &#xD;&#xA;I                              $2736                          10   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;WILM TRUST   B 848R015  4/93   $36.5K   300M163            **********11 M01 &#xD;&#xA;698007                  4/22V           $0                 **********11     &#xD;&#xA;I    REAL ESTATE               $3327                          24   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;AVCO FINANCE F 321E430 11/19   $3591    036M145            X**********1 I01 &#xD;&#xA;32443                   4/22V           $0                 111X11111111     &#xD;&#xA;I    SECRD;HSHLD GDS           $2577                          28   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;CHASE        B 701N090 10/18            MIN67              **********11 R01 &#xD;&#xA;3                       4/22V  $2500    $0                 **********11     &#xD;&#xA;I                              $255                           34   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;BK OF AMER   B 1597029  6/19   $1005                       **********11 R01 &#xD;&#xA;4676656                 3/22V  $2500    $0                 **********11     &#xD;&#xA;I                              $104                           24   0/ 0/ 0  &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;I N Q U I R I E S                                                           &#xD;&#xA;DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                      &#xD;&#xA; 6/02/22 F*********(CHI) SOLERA AUTO                                        &#xD;&#xA; 4/18/22 BLO2382255(IND) HSBC                                               &#xD;&#xA; 4/18/22 YPE2462756(IND) MIDLAND CRED                                       &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;C R E D I T  R E P O R T  S E R V I C E D  B Y :                            &#xD;&#xA;TRANSUNION TEST FACILITY                                      ************  &#xD;&#xA;555 W ADAMS CHICAGO, IL 60661                                               &#xD;&#xA;CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:          &#xD;&#xA;     HTTP://WWW.TRANSUNION.COM                                              &#xD;&#xA;                                                                            &#xD;&#xA;                            END OF TRANSUNION REPORT                        &#xD;&#xA;</ProductResult></Product><Product Id=\"Merge1\" ProductId=\"1003\" Vendor=\"DMS\" ProductName=\"MERGE\" ProductVersion=\"2.0\" DLLVersion=\"*********\" ReturnFormat=\"XML,0,ND,PD\" DateGenerated=\"2022-06-02T15:20:28\" Products=\"Product1\" Entities=\"E1\"><ProductResult Format=\"XML,TU\"><INsegments><INsegment SourceSegID=\"DMS5\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"22\" BureauHitLevelID=\"0A1@22\" DedupFlag=\"0\" tracking=\"********-36A0-4D28-958E-AD205EB16185\" name=\"LINE DANCER\" ssn=\"666393933\" street=\"525 COUNTRY LN\" citystatezip=\"FANTASY ISLAND,IL 60750\" appid=\"COM18872893\" mergereportdate=\"2022-06-02\" mergereportdateType=\"YMD\" bdate=\"1985-06-12\" bdateType=\"YMD\" version=\"919043000\" XMLversion=\"V_1.0.0.10\" XMLcrc=\"-1204256204\" reptypes=\"I\" bureaus=\"4\" scripts=\"DMS_SUPP_ATTR_0000\"></INsegment></INsegments><IDsegments><IDsegment SourceSegID=\"SH01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"0\" BureauHitLevelID=\"4A1@0\" DedupFlag=\"0\" subjectid=\"1\" first=\"LINE\" last=\"DANCER\" ssn=\"666393933\" datebirth=\"1976-10-01\" datebirthType=\"YMD\" dateonfile=\"1997-09-26\" dateonfileType=\"YMD\" daterep=\"2022-06-02\" daterepType=\"YMD\" namesource=\"F\" nameind=\"1\" productCode=\"07000\" productText=\"TU Credit Report\" personalsource=\"F\" fileno=\"1\" filehit=\"Y\" ssnmatch=\"05\" indicator=\"N\" indicator2=\"N\" marketfile=\"03\" submarketfile=\"PE\" version=\"0\" country=\"1\" language=\"1\" userref=\"********-36A0-4D28-958E-\" market=\"0622\" member=\"F03562356\" datetrans=\"2022-06-02\" datetransType=\"YMD\" timetrans=\"15:20:28\" cpuversion=\"98\" name=\"LINE DANCER\"></IDsegment></IDsegments><CAsegments><CAsegment SourceSegID=\"AD01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"1\" BureauHitLevelID=\"4A1@1\" DedupFlag=\"0\" qualifier=\"1\" street=\"525 S COUNTRY RD\" houseno=\"525\" predirect=\"S\" streetname=\"COUNTRY\" streettype=\"RD\" cityonly=\"FANTASY ISLAND\" state=\"IL\" zip=\"60750\" daterep=\"2021-09-21\" daterepType=\"YMD\" sourceind=\"F\" former=\"0\" parsedhouse=\"525\" parsedstreet=\"S COUNTRY\" parsedstreettype=\"RD\" addressind=\"1\"></CAsegment><CAsegment SourceSegID=\"AD01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"2\" BureauHitLevelID=\"4A1@2\" DedupFlag=\"0\" qualifier=\"1\" street=\"11 RIDGE WY\" houseno=\"11\" streetname=\"RIDGE\" streettype=\"WY\" cityonly=\"FANTASY ISLAND\" state=\"IL\" zip=\"60750\" daterep=\"2018-01-24\" daterepType=\"YMD\" sourceind=\"F\" former=\"1\" parsedhouse=\"11\" parsedstreet=\"RIDGE\" parsedstreettype=\"WY\" addressind=\"1\"></CAsegment></CAsegments><EMsegments><EMsegment SourceSegID=\"EM01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"3\" BureauHitLevelID=\"4A1@3\" DedupFlag=\"0\" name=\"DEFI\" occupation=\"MANAGER\" dateveri=\"2022-04-10\" dateveriType=\"YMD\" vericode=\"V\" sourceind=\"F\" former=\"0\"></EMsegment><EMsegment SourceSegID=\"EM01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"4\" BureauHitLevelID=\"4A1@4\" DedupFlag=\"0\" name=\"USGS\" dateveri=\"2022-04-10\" dateveriType=\"YMD\" vericode=\"V\" sourceind=\"F\" former=\"1\"></EMsegment></EMsegments><DLsegments><DLsegment SourceSegID=\"SC01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"19\" BureauHitLevelID=\"4A1@19\" DedupFlag=\"0\" productCode=\"001NN\" productText=\"VantageScore 4.0\" score=\"740\" sign=\"+\" reason1Code=\"38\" reason1Text=\"Lack of bankcard account information.\" reason2Code=\"49\" reason2Text=\"Not enough balance paid down over time on revolving accounts.\" reason3Code=\"44\" reason3Text=\"Balances on revolving accounts are too high compared with credit limits.\" reason4Code=\"50\" reason4Text=\"Balances on personal installment accounts too high compared to loan amounts.\"></DLsegment></DLsegments><MIsegments><MIsegment SourceSegID=\"SC01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"20\" BureauHitLevelID=\"4A1@20\" DedupFlag=\"0\" msg1=\"FACTA: 5th Reason Code\" msg2=\"Inquiries impacted the credit score\" msg3=\"001NN=VantageScore 4.0\"></MIsegment></MIsegments><PUsegments><PUsegment SourceSegID=\"PR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"5\" BureauHitLevelID=\"4A1@5\" DedupFlag=\"0\" court=\"*********\" courtnameCode=\"FE\" courtnameText=\"Federal District\" docket=\"*********\" attorney=\"JOSEPH B GREEN\" typeCode=\"7D\" typeText=\"Chapter 7 Bankruptcy dismissed\" daterep=\"2020-04-09\" daterepType=\"YMD\" datepaid=\"2021-07-16\" datepaidType=\"YMD\" ecoa=\"I\" dateorigfile=\"2020-04-09\" dateorigfileType=\"YMD\" kob=\"Z\"></PUsegment></PUsegments><TRsegments><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"6\" BureauHitLevelID=\"4A1@6\" DedupFlag=\"0\" member=\"B041PF037\" kob=\"B\" membername=\"FNCC\" account=\"4939394\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amount=\"1002\" amounttype=\"H\" amount2type=\"L\" highcredit=\"1002\" balance=\"666\" pastdueno=\"0\" dateopen=\"2015-10-01\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"**********111X1111111X11\" commonprofile=\"+++++++++++++-+++++++-++------------------------\" monsrev=\"24\" rawterms=\"             \" monthpayamt=\"19\" monthpayamtInfo=\"ESTIMATED\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"R\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"R\" orgecoa=\"I\" fullrating=\"RB11   \" surating=\"BRRR1OAB\" suprofile=\"..++++++++++++++-+++++++-++------------------------..............................\" mopayamtind=\"E\" termlength=\"0\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"7\" BureauHitLevelID=\"4A1@7\" DedupFlag=\"0\" member=\"F07212205\" kob=\"F\" membername=\"CITIFINANCIA\" account=\"800858\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amount=\"3929\" amounttype=\"H\" amount2type=\"L\" highcredit=\"3929\" balance=\"2736\" pastdueno=\"0\" dateopen=\"2021-05-14\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"**********\" commonprofile=\"++++++++++-\" monsrev=\"10\" rawterms=\"015M000000262\" monthpayamt=\"262\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"A\" veriindText=\"Automated account\" accounttype=\"I\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"I\" orgecoa=\"I\" fullrating=\"IF11   \" surating=\"FNII1OAB\" suprofile=\"..+++++++++++-\" mopayamtind=\"P\" termlength=\"15\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"8\" BureauHitLevelID=\"4A1@8\" DedupFlag=\"0\" member=\"B0848R015\" kob=\"B\" membername=\"WILM TRUST\" account=\"698007\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" loantypeCode=\"RE\" loantypeText=\"Real Estate\" ecoa=\"I\" amount=\"36500\" amounttype=\"H\" amount2type=\"L\" highcredit=\"36500\" balance=\"3327\" pastdueno=\"0\" dateopen=\"1993-04-24\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"********************1111\" commonprofile=\"++++++++++++++++++++++++------------------------\" monsrev=\"24\" rawterms=\"300M000000163\" monthpayamt=\"163\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"M\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"M\" orgecoa=\"I\" fullrating=\"ML11   \" surating=\"REIM1OAB\" suprofile=\"..+++++++++++++++++++++++++------------------------..................................\" mopayamtind=\"P\" termlength=\"300\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"9\" BureauHitLevelID=\"4A1@9\" DedupFlag=\"0\" member=\"F0321E430\" kob=\"F\" membername=\"AVCO FINANCE\" account=\"32443\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" loantypeCode=\"SH\" loantypeText=\"Secured by Household Goods\" ecoa=\"I\" amount=\"3591\" amounttype=\"H\" amount2type=\"L\" highcredit=\"3591\" balance=\"2577\" pastdueno=\"0\" dateopen=\"2019-11-10\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"X**********1111X**********X1\" commonprofile=\"-++++++++++++++-++++++++++-+-\" monsrev=\"28\" rawterms=\"036M000000145\" monthpayamt=\"145\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"I\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"I\" orgecoa=\"I\" fullrating=\"IF11   \" surating=\"FNII1OAB\" suprofile=\"..+-++++++++++++++-++++++++++-+-\" mopayamtind=\"P\" termlength=\"36\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"10\" BureauHitLevelID=\"4A1@10\" DedupFlag=\"0\" member=\"B0701N090\" kob=\"B\" membername=\"CHASE\" account=\"3\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amounttype=\"H\" amount2=\"2500\" amount2type=\"L\" creditlimit=\"2500\" balance=\"255\" pastdueno=\"0\" dateopen=\"2018-10-31\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"********************11111XXXX111X1\" commonprofile=\"+++++++++++++++++++++++++----+++-+--------\" monsrev=\"34\" rawterms=\"MIN *********\" monthpayamt=\"67\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"R\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"R\" orgecoa=\"I\" fullrating=\"RB11   \" surating=\"BRRR1OAB\" suprofile=\"..++++++++++++++++++++++++++----+++-+--------\" mopayamtind=\"P\" termlength=\"0\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"11\" BureauHitLevelID=\"4A1@11\" DedupFlag=\"0\" member=\"B01597029\" kob=\"B\" membername=\"BK OF AMER\" account=\"4676656\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amount=\"1005\" amounttype=\"H\" amount2=\"2500\" amount2type=\"L\" highcredit=\"1005\" creditlimit=\"2500\" balance=\"104\" pastdueno=\"0\" dateopen=\"2019-06-19\" dateopenType=\"YMD\" daterep=\"2022-03-01\" daterepType=\"YMD\" payprofile=\"********************1111\" commonprofile=\"++++++++++++++++++++++++---------\" monsrev=\"24\" rawterms=\"             \" monthpayamt=\"3\" monthpayamtInfo=\"ESTIMATED\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"R\" profilestartdate=\"2022-02-01\" profilestartdateType=\"YMD\" dateveri=\"2022-03-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"R\" orgecoa=\"I\" fullrating=\"RB11   \" surating=\"BRRR1OAB\" suprofile=\"...+++++++++++++++++++++++++---------\" mopayamtind=\"E\" termlength=\"0\"></TRsegment></TRsegments><IQsegments><IQsegment SourceSegID=\"IN01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"12\" BureauHitLevelID=\"4A1@12\" DedupFlag=\"0\" daterep=\"2022-06-02\" daterepType=\"YMD\" member=\"F03562356\" membername=\"SOLERA AUTO\" ecoa=\"I\" kob=\"F\" market=\"06TR\" SUcat=\"BR\"></IQsegment><IQsegment SourceSegID=\"IN01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"13\" BureauHitLevelID=\"4A1@13\" DedupFlag=\"0\" daterep=\"2022-04-18\" daterepType=\"YMD\" member=\"*********\" membername=\"HSBC\" ecoa=\"I\" kob=\"B\" market=\"40LO\" SUcat=\"BR\"></IQsegment><IQsegment SourceSegID=\"IN01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"14\" BureauHitLevelID=\"4A1@14\" DedupFlag=\"0\" daterep=\"2022-04-18\" daterepType=\"YMD\" member=\"*********\" membername=\"MIDLAND CRED\" ecoa=\"I\" kob=\"Y\" market=\"40PE\" SUcat=\"MC\"></IQsegment></IQsegments><BUsegments><BUsegment SourceSegID=\"OB01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"15\" BureauHitLevelID=\"4A1@15\" DedupFlag=\"0\" name=\"TRANSUNION TEST FACILITY\" street=\"555 W ADAMS\" cityonly=\"CHICAGO\" state=\"IL\" zip=\"60661\" phone=\"************\"></BUsegment></BUsegments><RWsegments><RWsegment SourceSegID=\"AO01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"16\" BureauHitLevelID=\"4A1@16\" DedupFlag=\"0\" rawsegment=\"AO010170650001H01\"></RWsegment><RWsegment SourceSegID=\"AO01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"17\" BureauHitLevelID=\"4A1@17\" DedupFlag=\"0\" rawsegment=\"AO01017001NN04   \"></RWsegment><RWsegment SourceSegID=\"SC01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"18\" BureauHitLevelID=\"4A1@18\" DedupFlag=\"0\" rawsegment=\"SC01034001NN+740   I38 49 44 50 07\"></RWsegment><RWsegment SourceSegID=\"ENDS\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"21\" BureauHitLevelID=\"4A1@21\" DedupFlag=\"0\" rawsegment=\"ENDS010024\"></RWsegment></RWsegments><GPsegments><GPsegment SourceSegID=\"DMS_SUPP_ATTR_0000,4/1/2022 8:36 AM,mriley\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"33\" BureauHitLevelID=\"0A1@33\" DedupFlag=\"0\" GP001=\"T40_A_SUPP_AUTO\" GP002=\"0\" GP003=\"0\" GP004=\"0\" GP006=\"0\" GP007=\"0\" GP008=\"0\" GP009=\"0\" GP010=\"0\" GP011=\"0\" GP012=\"0\" GP013=\"0\" GP014=\"0\" GP015=\"0\" GP016=\"0\" GP017=\"0\" GP018=\"0\" GP019=\"0\" GP020=\"0\" GP021=\"0\" GP023=\"0\" GP024=\"0\" GP025=\"0\" GP026=\"0\" GP027=\"0\" GP028=\"0\" GP032=\"0\" GP033=\"0\" GP034=\"0\" GP035=\"0\" GP036=\"0\" GP037=\"0\" GP038=\"0\" GP039=\"0\" GP040=\"0\" GP041=\"0\" GP042=\"0\" GP043=\"0\" GP044=\"0\" GP045=\"0\" GP046=\"0\" GP047=\"0\" GP048=\"0\" GP049=\"0\" GP051=\"0\" GP052=\"0\" GP053=\"0\" GP056=\"0\" GP058=\"0\" GP059=\"0\" GP060=\"0\" GP066=\"0\" GP069=\"0\" GP070=\"0\" GP071=\"0\" GP080=\"0\" GP081=\"0\" GP082=\"1\" GP083=\"11\" GP085=\"11\" GP087=\"0\" GP088=\"0\" GP089=\"0\" GP090=\"0\" GP091=\"0\" GP092=\"0\" GP093=\"0\" GP094=\"0\" GP095=\"0\" GP096=\"0\" GP097=\"0\" GP098=\"0\" GP099=\"0\" GP100=\"0\" GP101=\"0\" GP102=\"0\" GP103=\"0\" GP104=\"0\" GP105=\"0\" GP106=\"0\" GP107=\"0\" GP108=\"0\" GP109=\"0\" GP110=\"0\" GP111=\"0\" GP112=\"0\" GP113=\"0\" GP123=\"0\" GP124=\"6\" GP125=\"0\" GP126=\"0\" GP128=\"349\" GP129=\"0\" GP131=\"262\" GP132=\"36500\" GP135=\"6002\" GP136=\"6\" GP137=\"0\" GP138=\"0\" GP142=\"0\" GP143=\"0\" GP144=\"0\" GP145=\"0\" GP146=\"1\" GP147=\"0\" GP148=\"0\" GP149=\"0\" GP150=\"0\" GP151=\"0\" GP152=\"0\" GP153=\"0\" GP154=\"0\" GP156=\"6002\" GP158=\"0\" GP160=\"0\" GP161=\"0\" GP162=\"0\" GP163=\"0\"></GPsegment></GPsegments><SUsegments><SUCIsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"23\" BureauHitLevelID=\"0A1@23\" DedupFlag=\"0\" SUCIcat=\"CI\" SUCI001=\"92\" SUCI002=\"1\" SUCI003=\"659\" SUCI007=\"350\" SUCI008=\"2\" SUCI009=\"2\" SUCI010=\"2\" SUCI011=\"2\" SUCI012=\"3\" SUCI013=\"0\" SUCI014=\"3\" SUCI015=\"0\" SUCI016=\"0\" SUCI017=\"0\" SUCI018=\"0\" SUCI019=\"2\" SUCI020=\"0\" SUCI021=\"0\" SUCI022=\"0\" SUCI023=\"0\" SUCI024=\"6\" SUCI025=\"100\" SUCI026=\"17\" SUCI027=\"407\" SUCI028=\"89\" SUCI029=\"350\" SUCI030=\"2\" SUCI031=\"13\" SUCI032=\"0\" SUCI033=\"5313\" SUCI034=\"1025\" SUCI035=\"919430000\" SUCI036=\"6002\" SUCI037=\"0\" SUCI038=\"0\" SUCI039=\"0\" SUCI040=\"0\" SUCI041=\"0\" SUCI042=\"0\" SUCI043=\"0\" SUCI044=\"0\" SUCI045=\"0\" SUCI046=\"0\" SUCI047=\"0\" SUCI048=\"0\" SUCI049=\"0\" SUCI050=\"0\" SUCI051=\"0\" SUCI052=\"0\" SUCI053=\"0\" SUCI054=\"0\" SUCI055=\"0\" SUCI056=\"0\" SUCI057=\"0\" SUCI058=\"0\" SUCI059=\"0\" SUCI060=\"0\" SUCI061=\"0\" SUCI062=\"26\" SUCI065=\"0\" SUCI070=\"0\" SUCI071=\"0\" SUCI072=\"0\" SUCI073=\"0\" SUCI074=\"0\" SUCI075=\"0\" SUCI076=\"0\" SUCI077=\"0\" SUCI078=\"36500\" SUCI079=\"350\" SUCI080=\"3327\" SUCI082=\"0\" SUCI083=\"3327\" SUCI084=\"1\" SUCI085=\"1\" SUCI087=\"0\" SUCI089=\"26\" SUCI090=\"13\" SUCI091=\"6\" SUCI092=\"0\" SUCI093=\"6\" SUCI094=\"0\" SUCI095=\"0\" SUCI096=\"0\" SUCI097=\"0\" SUCI098=\"0\" SUCI099=\"0\" SUCI100=\"0\" SUCI101=\"0\" SUCI102=\"0\" SUCI103=\"0\" SUCI104=\"3\" SUCI105=\"2\" SUCI106=\"6\" SUCI107=\"0\" SUCI108=\"2\" SUCI109=\"36500\" SUCI110=\"163\" SUCI111=\"1\" SUCI112=\"0\" SUCI113=\"0\" SUCI114=\"0\" SUCI115=\"6\" SUCI116=\"9665\" SUCI117=\"7520\" SUCI121=\"3\" SUCI122=\"0\" SUCI123=\"1025\" SUCI124=\"297\" SUCI136=\"6\" SUCI137=\"31\" SUCI138=\"407\" SUCI139=\"0\" SUCI140=\"0\" SUCI141=\"0\" SUCI142=\"0\" SUCI143=\"0\" SUCI144=\"0\" SUCI145=\"0\" SUCI146=\"0\" SUCI147=\"0\" SUCI148=\"0\" SUCI149=\"0\" SUCI150=\"0\" SUCI151=\"0\" SUCI152=\"0\" SUCI153=\"80\" SUCI154=\"89\" SUCI155=\"0\" SUCI156=\"0\" SUCI157=\"0\" SUCI158=\"0\" SUCI159=\"0\" SUCI160=\"0\" SUCI161=\"0\" SUCI162=\"0\" SUCI163=\"0\" SUCI164=\"0\" SUCI165=\"0\" SUCI166=\"0\" SUCI167=\"0\" SUCI168=\"0\" SUCI169=\"3327\" SUCI170=\"350\" SUCI171=\"0\" SUCI172=\"0\" SUCI173=\"0\" SUCI174=\"0\" SUCI175=\"0\" SUCI176=\"0\" SUCI177=\"0\" SUCI178=\"0\" SUCI179=\"0\" SUCI180=\"0\" SUCI181=\"0\" SUCI182=\"0\" SUCI183=\"0\" SUCI184=\"0\" SUCI185=\"26\" SUCI186=\"2\" SUCI189=\"0\" SUCI193=\"3327\" SUCI194=\"2\" SUCI196=\"6\" SUCI197=\"9665\" SUCI198=\"659\" SUCI199=\"5313\" SUCI200=\"1025\" SUCI201=\"163\" SUCI202=\"2500\" SUCI203=\"36500\" SUCI204=\"0\"></SUCIsegment><SUBRsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"26\" BureauHitLevelID=\"0A1@26\" DedupFlag=\"0\" SUBRcat=\"BR\" SUBR001=\"3\" SUBR002=\"3\" SUBR003=\"50\" SUBR004=\"3\" SUBR005=\"0\" SUBR006=\"0\" SUBR007=\"0\" SUBR008=\"0\" SUBR009=\"3\" SUBR011=\"36\" SUBR012=\"0\" SUBR013=\"0\" SUBR014=\"0\" SUBR015=\"0\" SUBR016=\"0\" SUBR017=\"0\" SUBR018=\"0\" SUBR019=\"0\" SUBR020=\"0\" SUBR021=\"0\" SUBR022=\"0\" SUBR023=\"0\" SUBR024=\"0\" SUBR025=\"0\" SUBR026=\"0\" SUBR027=\"0\" SUBR028=\"0\" SUBR029=\"0\" SUBR030=\"0\" SUBR031=\"0\" SUBR032=\"0\" SUBR033=\"0\" SUBR034=\"0\" SUBR035=\"0\" SUBR036=\"0\" SUBR037=\"0\" SUBR038=\"0\" SUBR039=\"89\" SUBR040=\"1025\" SUBR041=\"6002\" SUBR042=\"100\" SUBR043=\"17\" SUBR044=\"100\" SUBR045=\"1\" SUBR046=\"0\" SUBR047=\"0\" SUBR048=\"160\" SUBR049=\"53\" SUBR050=\"7\" SUBR051=\"2\" SUBR052=\"1\" SUBR053=\"0\" SUBR054=\"0\" SUBR055=\"0\" SUBR056=\"1\" SUBR057=\"1\" SUBR058=\"1\" SUBR059=\"1\" SUBR060=\"2\" SUBR061=\"666\" SUBR065=\"0\" SUBR066=\"0\" SUBR067=\"0\" SUBR068=\"0\" SUBR070=\"0\" SUBR071=\"0\" SUBR072=\"0\" SUBR073=\"0\" SUBR074=\"2500\"></SUBRsegment><SUC2segment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"25\" BureauHitLevelID=\"0A1@25\" DedupFlag=\"0\" SUC2cat=\"C2\" SUC2002=\"0\" SUC2003=\"0\" SUC2004=\"0\" SUC2005=\"0\" SUC2006=\"0\" SUC2007=\"0\" SUC2008=\"0\" SUC2009=\"0\" SUC2010=\"0\" SUC2011=\"0\" SUC2012=\"0\" SUC2013=\"0\" SUC2014=\"0\" SUC2015=\"0\" SUC2016=\"0\" SUC2018=\"26\" SUC2020=\"0\" SUC2021=\"0\" SUC2024=\"0\" SUC2025=\"2\" SUC2026=\"2\" SUC2027=\"6\" SUC2028=\"6\" SUC2029=\"0\" SUC2030=\"0\" SUC2031=\"0\" SUC2033=\"0\" SUC2034=\"0\" SUC2035=\"0\" SUC2036=\"0\" SUC2037=\"0\" SUC2038=\"2\" SUC2039=\"2\" SUC2040=\"2\" SUC2041=\"2\" SUC2042=\"2\" SUC2043=\"2\" SUC2044=\"2\" SUC2045=\"2\" SUC2046=\"2\" SUC2047=\"2\" SUC2048=\"2\" SUC2049=\"2\" SUC2050=\"2\" SUC2051=\"2\" SUC2052=\"2\" SUC2053=\"2\" SUC2054=\"2\" SUC2055=\"2\" SUC2056=\"2\" SUC2057=\"2\" SUC2058=\"2\" SUC2059=\"0\" SUC2060=\"0\" SUC2061=\"0\" SUC2062=\"0\" SUC2063=\"2500\" SUC2064=\"36500\" SUC2069=\"0\" SUC2070=\"0\" SUC2071=\"0\" SUC2072=\"0\" SUC2073=\"0\"></SUC2segment><SUFNsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"27\" BureauHitLevelID=\"0A1@27\" DedupFlag=\"0\" SUFNcat=\"FN\" SUFN001=\"2\" SUFN002=\"2\" SUFN003=\"33\" SUFN004=\"2\" SUFN005=\"0\" SUFN006=\"0\" SUFN007=\"0\" SUFN008=\"0\" SUFN009=\"2\" SUFN011=\"13\" SUFN012=\"0\" SUFN013=\"0\" SUFN014=\"0\" SUFN015=\"0\" SUFN016=\"0\" SUFN017=\"0\" SUFN018=\"0\" SUFN019=\"0\" SUFN020=\"0\" SUFN021=\"0\" SUFN022=\"0\" SUFN023=\"0\" SUFN024=\"0\" SUFN025=\"0\" SUFN026=\"0\" SUFN027=\"0\" SUFN028=\"0\" SUFN029=\"0\" SUFN030=\"0\" SUFN031=\"0\" SUFN032=\"0\" SUFN033=\"0\" SUFN034=\"0\" SUFN035=\"0\" SUFN036=\"0\" SUFN037=\"0\" SUFN038=\"0\" SUFN039=\"407\" SUFN040=\"5313\" SUFN041=\"7520\" SUFN042=\"100\" SUFN043=\"71\" SUFN044=\"100\" SUFN045=\"2\" SUFN046=\"0\" SUFN047=\"0\" SUFN048=\"44\" SUFN049=\"22\" SUFN050=\"4\" SUFN051=\"2\" SUFN052=\"0\" SUFN053=\"0\" SUFN054=\"0\" SUFN055=\"0\" SUFN056=\"0\" SUFN057=\"0\" SUFN058=\"0\" SUFN059=\"0\" SUFN061=\"2736\" SUFN065=\"0\" SUFN066=\"0\" SUFN067=\"0\" SUFN068=\"0\" SUFN070=\"0\" SUFN071=\"0\" SUFN072=\"0\" SUFN073=\"0\" SUFN074=\"3929\" SUFN075=\"3929\" SUFN076=\"262\" SUFN077=\"15\" SUFN078=\"2\" SUFN079=\"3591\" SUFN080=\"145\" SUFN081=\"36\" SUFN082=\"5\"></SUFNsegment><SUILsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"30\" BureauHitLevelID=\"0A1@30\" DedupFlag=\"0\" SUILcat=\"IL\" SUIL001=\"3\" SUIL002=\"3\" SUIL003=\"50\" SUIL004=\"3\" SUIL005=\"0\" SUIL006=\"0\" SUIL007=\"0\" SUIL008=\"0\" SUIL009=\"3\" SUIL011=\"13\" SUIL012=\"0\" SUIL013=\"0\" SUIL014=\"0\" SUIL015=\"0\" SUIL016=\"0\" SUIL017=\"0\" SUIL018=\"0\" SUIL019=\"0\" SUIL020=\"0\" SUIL021=\"0\" SUIL022=\"0\" SUIL023=\"0\" SUIL024=\"0\" SUIL025=\"0\" SUIL026=\"0\" SUIL027=\"0\" SUIL028=\"0\" SUIL029=\"0\" SUIL030=\"0\" SUIL031=\"0\" SUIL032=\"0\" SUIL033=\"0\" SUIL034=\"0\" SUIL035=\"0\" SUIL036=\"0\" SUIL037=\"0\" SUIL038=\"0\" SUIL039=\"570\" SUIL040=\"8640\" SUIL041=\"44020\" SUIL042=\"100\" SUIL043=\"20\" SUIL044=\"100\" SUIL045=\"2\" SUIL046=\"0\" SUIL047=\"0\" SUIL048=\"394\" SUIL049=\"131\" SUIL050=\"6\" SUIL051=\"3\" SUIL052=\"0\" SUIL053=\"0\" SUIL054=\"0\" SUIL055=\"0\" SUIL056=\"0\" SUIL057=\"0\" SUIL058=\"0\" SUIL059=\"0\" SUIL061=\"3327\" SUIL065=\"0\" SUIL066=\"0\" SUIL067=\"0\" SUIL068=\"0\" SUIL070=\"0\" SUIL071=\"0\" SUIL072=\"0\" SUIL073=\"0\" SUIL074=\"36500\" SUIL075=\"3929\" SUIL076=\"262\" SUIL077=\"15\" SUIL078=\"2\" SUIL079=\"3591\" SUIL080=\"145\" SUIL081=\"36\" SUIL082=\"5\"></SUILsegment><SUMCsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"28\" BureauHitLevelID=\"0A1@28\" DedupFlag=\"0\" SUMCcat=\"MC\" SUMC001=\"0\" SUMC002=\"0\" SUMC003=\"0\" SUMC004=\"0\" SUMC005=\"0\" SUMC006=\"0\" SUMC007=\"0\" SUMC008=\"0\" SUMC009=\"0\" SUMC012=\"0\" SUMC013=\"0\" SUMC014=\"0\" SUMC015=\"0\" SUMC016=\"0\" SUMC017=\"0\" SUMC018=\"0\" SUMC019=\"0\" SUMC020=\"0\" SUMC021=\"0\" SUMC022=\"0\" SUMC023=\"0\" SUMC024=\"0\" SUMC025=\"0\" SUMC026=\"0\" SUMC027=\"0\" SUMC028=\"0\" SUMC029=\"0\" SUMC030=\"0\" SUMC031=\"0\" SUMC032=\"0\" SUMC033=\"0\" SUMC034=\"0\" SUMC035=\"0\" SUMC036=\"0\" SUMC037=\"0\" SUMC038=\"0\" SUMC044=\"0\" SUMC045=\"0\" SUMC046=\"0\" SUMC047=\"0\" SUMC050=\"0\" SUMC051=\"0\" SUMC052=\"0\" SUMC053=\"0\" SUMC054=\"0\" SUMC055=\"0\" SUMC056=\"1\" SUMC057=\"1\" SUMC058=\"1\" SUMC059=\"1\" SUMC060=\"2\" SUMC065=\"0\" SUMC066=\"0\" SUMC067=\"0\" SUMC068=\"0\" SUMC070=\"0\" SUMC071=\"0\" SUMC072=\"0\" SUMC073=\"0\"></SUMCsegment><SUREsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"29\" BureauHitLevelID=\"0A1@29\" DedupFlag=\"0\" SUREcat=\"RE\" SURE001=\"1\" SURE002=\"1\" SURE003=\"17\" SURE004=\"1\" SURE005=\"0\" SURE006=\"0\" SURE007=\"0\" SURE008=\"0\" SURE009=\"1\" SURE011=\"350\" SURE012=\"0\" SURE013=\"0\" SURE014=\"0\" SURE015=\"0\" SURE016=\"0\" SURE017=\"0\" SURE018=\"0\" SURE019=\"0\" SURE020=\"0\" SURE021=\"0\" SURE022=\"0\" SURE023=\"0\" SURE024=\"0\" SURE025=\"0\" SURE026=\"0\" SURE027=\"0\" SURE028=\"0\" SURE029=\"0\" SURE030=\"0\" SURE031=\"0\" SURE032=\"0\" SURE033=\"0\" SURE034=\"0\" SURE035=\"0\" SURE036=\"0\" SURE037=\"0\" SURE038=\"0\" SURE039=\"163\" SURE040=\"3327\" SURE041=\"36500\" SURE042=\"100\" SURE043=\"9\" SURE044=\"100\" SURE045=\"0\" SURE046=\"0\" SURE047=\"0\" SURE048=\"350\" SURE049=\"350\" SURE050=\"2\" SURE051=\"1\" SURE052=\"0\" SURE053=\"0\" SURE054=\"0\" SURE055=\"0\" SURE056=\"0\" SURE057=\"0\" SURE058=\"0\" SURE059=\"0\" SURE061=\"3327\" SURE065=\"0\" SURE066=\"0\" SURE067=\"0\" SURE068=\"0\" SURE070=\"0\" SURE071=\"0\" SURE072=\"0\" SURE073=\"0\" SURE074=\"36500\" SURE075=\"36500\" SURE076=\"163\" SURE077=\"300\" SURE078=\"0\"></SUREsegment><SURLsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"31\" BureauHitLevelID=\"0A1@31\" DedupFlag=\"0\" SURLcat=\"RL\" SURL001=\"3\" SURL002=\"3\" SURL003=\"50\" SURL004=\"3\" SURL005=\"0\" SURL006=\"0\" SURL007=\"0\" SURL008=\"0\" SURL009=\"3\" SURL011=\"36\" SURL012=\"0\" SURL013=\"0\" SURL014=\"0\" SURL015=\"0\" SURL016=\"0\" SURL017=\"0\" SURL018=\"0\" SURL019=\"0\" SURL020=\"0\" SURL021=\"0\" SURL022=\"0\" SURL023=\"0\" SURL024=\"0\" SURL025=\"0\" SURL026=\"0\" SURL027=\"0\" SURL028=\"0\" SURL029=\"0\" SURL030=\"0\" SURL031=\"0\" SURL032=\"0\" SURL033=\"0\" SURL034=\"0\" SURL035=\"0\" SURL036=\"0\" SURL037=\"0\" SURL038=\"0\" SURL039=\"89\" SURL040=\"1025\" SURL041=\"6002\" SURL042=\"100\" SURL043=\"17\" SURL044=\"100\" SURL045=\"1\" SURL046=\"0\" SURL047=\"0\" SURL048=\"160\" SURL049=\"53\" SURL050=\"7\" SURL051=\"2\" SURL052=\"1\" SURL053=\"0\" SURL054=\"0\" SURL055=\"0\" SURL056=\"2\" SURL057=\"2\" SURL058=\"2\" SURL059=\"2\" SURL060=\"2\" SURL061=\"666\" SURL065=\"0\" SURL066=\"0\" SURL067=\"0\" SURL068=\"0\" SURL070=\"0\" SURL071=\"0\" SURL072=\"0\" SURL073=\"0\" SURL074=\"2500\"></SURLsegment><SUSCsegment SourceSegID=\"SC01\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"32\" BureauHitLevelID=\"0A1@32\" DedupFlag=\"0\" SUSCcat=\"SC\" SUSC001=\"740\" SUSC002=\"0\" SUSC003=\"0\" SUSC004=\"4\" SUSC005=\"308\" SUSC006=\"409\" SUSC007=\"404\" SUSC008=\"500\"></SUSCsegment></SUsegments><CKsegment nosegments=\"0\" nocharacters=\"0\" checksum=\"1\"></CKsegment></ProductResult></Product></Products></DMSCommon></DMSXML></Response>", "create_date": "2022-06-07T13:46:45.0170000", "update_date": "2022-06-07T13:46:45.0170000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}}, "Addresses": {"Address": [{"address_id": 424, "city": "FANTASY ISLAND", "state_code_id": "IL", "country_code": "USA", "postal_code": 60750, "item_code": "CURRENT", "street_type": "LN", "street_type_description": "LANE", "years_at_address": 14, "months_at_address": 0, "mortgage_rent": 500, "address_type": "CURRENT", "street_number": 525, "street": "COUNTRY", "house_type": "CURRENT", "create_date": "2022-06-07T13:46:35.9900000", "update_date": "2022-06-07T13:46:35.9900000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, {"address_id": 425, "country_code": "USA", "item_code": "PREVIOUS", "mortgage_rent": 500, "address_type": "PREVIOUS", "create_date": "2022-06-07T13:46:35.9930000", "update_date": "2022-06-07T13:46:35.9930000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}]}, "Communications": {"Communication": [{"com_link_id": 1029, "item_code": "HOME", "com": "304-987-4521", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0070000", "update_date": "2022-06-07T13:46:36.0070000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, {"com_link_id": 1030, "item_code": "WORK", "com": "304-987-4111", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0100000", "update_date": "2022-06-07T13:46:36.0100000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, {"com_link_id": 1031, "item_code": "PWORK", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0100000", "update_date": "2022-06-07T13:46:36.0100000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}]}, "Employers": {"Employer": [{"employer_id": 457, "type": "EMPLOYED", "type_code": "CURRENT", "years_employed": 15, "months_employed": 0, "employer_name": "TEST CO", "occupation": "MANAGER", "employment_status": "Employed", "create_date": "2022-06-07T13:46:35.9830000", "update_date": "2022-08-18T09:17:00.9530000", "updated_by": "jmcclintick"}, {"employer_id": 458, "type_code": "PREVIOUS", "employment_status": "None", "create_date": "2022-06-07T13:46:35.9870000", "update_date": "2022-06-07T13:46:35.9870000", "updated_by": "System [No User Available]"}]}, "Incomes": {"Income": [{"income_id": 299, "frequency": "MONTHLY", "gross": 12000, "net_source": "MAN", "monthly_income": 12000, "employer_id": 457, "income_type": "PAYCHECK", "create_date": "2022-06-07T13:46:35.9670000", "update_date": "2022-06-07T13:46:35.9770000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, {"income_id": 300, "frequency": "MONTHLY", "gross": 0, "net_source": "MAN", "monthly_income": 0, "employer_id": 457, "income_type": "PAYCHECK", "create_date": "2022-06-07T13:46:35.9730000", "update_date": "2022-06-07T13:46:36.2730000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}], "total_primary_income": 12000, "total_secondary_income": 0, "deal_detail_id": 207, "bundle_id": 1082}, "Expenses": {"Expense": [{"entity_type": "PB", "creditor": "HOUSING PAYMENT", "balance": 0, "payment": 500, "expense_id": 1460, "frequency": "MONTHLY", "source": "APP", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "RENT", "item_code": "RENT", "create_date": "2022-06-07T13:46:35.9470000", "update_date": "2022-06-07T13:46:35.9470000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "FNCC", "balance": 666, "open_date": "2015-10-01T00:00:00.0000000", "rating": 1, "payment": 19, "expense_id": 1462, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "CITIFINANCIA", "balance": 2736, "open_date": "2021-05-14T00:00:00.0000000", "rating": 1, "payment": 262, "expense_id": 1463, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "WILM TRUST", "balance": 3327, "open_date": "1993-04-24T00:00:00.0000000", "rating": 1, "payment": 163, "expense_id": 1464, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "AVCO FINANCE", "balance": 2577, "open_date": "2019-11-10T00:00:00.0000000", "rating": 1, "payment": 145, "expense_id": 1465, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "CHASE", "balance": 255, "open_date": "2018-10-31T00:00:00.0000000", "rating": 1, "payment": 67, "expense_id": 1466, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "BK OF AMER", "balance": 104, "open_date": "2019-06-19T00:00:00.0000000", "rating": 1, "payment": 3, "expense_id": 1467, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "balance": 0, "open_date": "2020-04-09T00:00:00.0000000", "payment": 0, "expense_id": 1468, "source": "TU", "active": "True", "exclude_flag": "True", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "Insurance", "balance": 0, "payment": 100, "expense_id": 1469, "frequency": "MONTHLY", "source": "SYSTEM", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "INSURANCE", "item_code": "INSURANCE", "create_date": "2022-06-07T13:46:47.0730000", "update_date": "2022-06-07T13:46:47.0730000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "Est. Payment", "balance": 0, "payment": 611.14, "expense_id": 1470, "frequency": "MONTHLY", "source": "SYSTEM", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "PAYMENT", "item_code": "PAYMENT", "create_date": "2022-06-07T13:46:47.0730000", "update_date": "2022-06-07T13:47:42.0000000", "updated_by": "rmescha", "deal_detail_id": 207, "bundle_id": 1082}], "total_debt": 1870}, "customer_id": 229, "social_security_number": 666393933, "type": "PB", "date_of_birth": "1989-05-25T00:00:00.0000000", "first_name": "LINE", "last_name": "DANCER", "income_monthly": 12000, "fico": 740, "total_exp": 0, "is_in_military": "No", "full_name": "LINE DANCER", "age": 34, "create_date": "2022-06-07T13:46:35.0000000", "update_date": "2022-08-18T09:17:01.0000000", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}}, "CustomScorecard": {"deal_detail_id": 207, "bundle_id": 1082}, "Vehicles": {"Vehicle": [{"vehicle_id": 362, "source_system_code": "DT", "trade_in": "True", "make": "KIA", "model": "OPTIMA", "year": 2014, "trim": "SEDAN 4D LX I4", "vehicle_age": 9, "active": "True", "create_date": "2022-06-07T13:46:35.9300000", "update_date": "2022-06-07T13:46:36.2530000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, {"Eval": {"collateral_evaluation_id": 342, "collateral_id": 363, "wholesale_value": 38125, "retail_value": 0, "msrp": 0, "used_car_value": 0, "stated_value": 38125, "invoice": 0, "vehicle_value": 38125, "create_date": "2022-06-07T13:46:35.9430000", "update_date": "2022-06-07T13:46:35.9430000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}, "vehicle_id": 363, "source_system_code": "DT", "type": "Used", "trade_in": "False", "make": "MERCEDES-BENZ", "model": "C-CLASS", "year": 2019, "condition": "Used", "odometer": 39930, "trim": "COUPE 2D C300", "certified_used": "False", "vehicle_age": 4, "active": "True", "create_date": "2022-06-07T13:46:35.9300000", "update_date": "2022-06-07T13:46:36.2600000", "updated_by": "System [No User Available]", "deal_detail_id": 207, "bundle_id": 1082}]}, "payment_call_flag": "False", "credit_type": "Individual", "id": 207, "source_system_code": "DT", "received_date": "2022-06-07T13:46:00.0000000", "initial_entry_date": "2022-06-07T13:46:00.0000000", "source_system_dealer_id": 269472, "decision_date": "2022-06-07T13:48:06.0000000", "decision_code": "A", "source_system_id": "BBF3700439", "total_primary_income": 12000, "total_secondary_income": 0, "total_debt": 1870.14, "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T09:17:01.6970000", "updated_by": "jmcclintick", "bundle_id": 1082}, "RuleSummary": {"Rule": [{"id": 155, "name": "15% Tolerance", "type": "Conditional Approval Rules", "eval_value": 25870.6, "actual_value": 30436, "result": "PASS", "waive": "False", "definition": "Recommended.EstAmtFinanced < Custom Fields.Adjusted Requested Amount Financed", "evaluatedDefinition": "30436.00 < 25870.6", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 146, "name": "AA AutoDecline CB Print", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "CoApplicant.IsActive == 1 and Deal.App Status == AD", "evaluatedDefinition": "False == True and APPROVED == AD", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 145, "name": "AA Decline CB Print", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "Deal.App Status == D and CoApplicant.IsActive == 1", "evaluatedDefinition": "APPROVED == D and False == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 142, "name": "AA Decline PB Print", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "Deal.App Status == D or Deal.App Status == AD", "evaluatedDefinition": "APPROVED == D or APPROVED == AD", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 147, "name": "Applicant Age less than 18", "type": "Pre-Bureau Rules", "eval_value": 18, "actual_value": 33, "result": "PASS", "waive": "False", "definition": "Applicant.Age < 18", "evaluatedDefinition": "33.00 < 18.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 143, "name": "Approval", "type": "Notification Template Rules", "result": "FAIL", "waive": "False", "definition": "Deal.App Status == AA or Deal.App Status == Approved or", "evaluatedDefinition": "APPROVED == AA or APPROVED == APPROVED or", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 144, "name": "Conditional Approval", "type": "Notification Template Rules", "eval_value": "CONDITIONAL APPROVAL", "actual_value": "APPROVED", "result": "PASS", "waive": "False", "definition": "Deal.App Status == Conditional Approval", "evaluatedDefinition": "APPROVED == CONDITIONAL APPROVAL", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - AF < MinAF", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 30436, "result": "NA", "waive": "False", "definition": "AF < MinAF", "evaluatedDefinition": "30436.00 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - AF > MaxAF", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 30436, "result": "NA", "waive": "False", "definition": "AF > MaxAF", "evaluatedDefinition": "30436.00 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - APR > Usury", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 11.01, "result": "NA", "waive": "False", "definition": "APR > Usury", "evaluatedDefinition": "11.01 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Buy Rate < Custom Min Rate", "type": "Credit Policy Rules", "eval_value": 8.99, "actual_value": 11.01, "result": "PASS", "waive": "False", "definition": "Buy Rate < Custom Min Rate", "evaluatedDefinition": "11.01 < 8.99", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Buy Rate > Custom Max Rate", "type": "Credit Policy Rules", "eval_value": 11.5, "actual_value": 11.01, "result": "PASS", "waive": "False", "definition": "Buy Rate > Custom Max Rate", "evaluatedDefinition": "11.01 > 11.50", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - CalculatedDiscount < Custom Min Fee", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 195, "result": "PASS", "waive": "False", "definition": "CalculatedDiscount < Custom Min Fee", "evaluatedDefinition": "195.00 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - CalculatedDiscount > Custom Max Fee", "type": "Credit Policy Rules", "eval_value": 195, "actual_value": 195, "result": "PASS", "waive": "False", "definition": "CalculatedDiscount > Custom Max Fee", "evaluatedDefinition": "195.00 > 195.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - CashDown < Custom Min Down", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 1000, "result": "PASS", "waive": "False", "definition": "CashDown < Custom Min Down", "evaluatedDefinition": "1000.00 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - DTI > Custom Max DTI", "type": "Credit Policy Rules", "eval_value": 999, "actual_value": 15.58, "result": "PASS", "waive": "False", "definition": "DTI > Custom Max DTI", "evaluatedDefinition": "15.58 > 999.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - DTI > MaxDTI", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 15.58, "result": "NA", "waive": "False", "definition": "DTI > MaxDTI", "evaluatedDefinition": "15.58 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - EstAmtFinanced > Custom Max Amt Fin", "type": "Credit Policy Rules", "eval_value": 99999, "actual_value": 30436, "result": "PASS", "waive": "False", "definition": "EstAmtFinanced > Custom Max Amt Fin", "evaluatedDefinition": "30436.00 > 99999.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - FLTV > Custom Max FLTV", "type": "Credit Policy Rules", "eval_value": 130, "actual_value": 68.81, "result": "PASS", "waive": "False", "definition": "FLTV > Custom Max FLTV", "evaluatedDefinition": "68.81 > 130.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Line3Pct > MaxLine3Pct", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 73.29, "result": "NA", "waive": "False", "definition": "Line3Pct > MaxLine3Pct", "evaluatedDefinition": "73.29 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Line5Pct > MaxLine5Pct", "type": "Credit Policy Rules", "eval_value": 150, "actual_value": 79.83, "result": "PASS", "waive": "False", "definition": "Line5Pct > MaxLine5Pct", "evaluatedDefinition": "79.83 > 150.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Miles > MaxMiles", "type": "Credit Policy Rules", "eval_value": 110000, "actual_value": 39930, "result": "PASS", "waive": "False", "definition": "Miles > MaxMiles", "evaluatedDefinition": "39930 > 110000", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - MinIncome < Custom Min Income", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 0, "result": "NA", "waive": "False", "definition": "MinIncome < Custom Min Income", "evaluatedDefinition": "0.00 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Monthly Income < Min Income", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 12000, "result": "NA", "waive": "False", "definition": "Monthly Income < Min Income", "evaluatedDefinition": "12000.00 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Participation Percentage < Custom Min Participation", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 2, "result": "PASS", "waive": "False", "definition": "Participation Percentage < Custom Min Participation", "evaluatedDefinition": "2.00 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Participation Percentage > Custom Max Participation", "type": "Credit Policy Rules", "eval_value": 2.5, "actual_value": 2, "result": "PASS", "waive": "False", "definition": "Participation Percentage > Custom Max Participation", "evaluatedDefinition": "2.00 > 2.50", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Payment < MinPayment", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 611.14, "result": "NA", "waive": "False", "definition": "Payment < MinPayment", "evaluatedDefinition": "611.14 < 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Payment > MaxPayment", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 611.14, "result": "NA", "waive": "False", "definition": "Payment > MaxPayment", "evaluatedDefinition": "611.14 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - PTI > Custom Max PTI", "type": "Credit Policy Rules", "eval_value": 22, "actual_value": 5.09, "result": "PASS", "waive": "False", "definition": "PTI > Custom Max PTI", "evaluatedDefinition": "5.09 > 22.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - PTI > MaxPTI", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 5.09, "result": "NA", "waive": "False", "definition": "PTI > MaxPTI", "evaluatedDefinition": "5.09 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - Term > MaxTerm", "type": "Credit Policy Rules", "eval_value": 0, "actual_value": 72, "result": "NA", "waive": "False", "definition": "Term > MaxTerm", "evaluatedDefinition": "72 > 0", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - TermMonths > Custom Max Term", "type": "Credit Policy Rules", "eval_value": 84, "actual_value": 72, "result": "PASS", "waive": "False", "definition": "TermMonths > Custom Max Term", "evaluatedDefinition": "72.00 > 84.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"name": "CP Rule - VehicleAge > MaxVehicleAge", "type": "Credit Policy Rules", "eval_value": 15, "actual_value": 3, "result": "PASS", "waive": "False", "definition": "VehicleAge > MaxVehicleAge", "evaluatedDefinition": "3 > 15", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 156, "name": "Extended <PERSON><PERSON>", "type": "Post-Bureau Rules", "eval_value": "FRAUD ALERT", "actual_value": "NO FRAUD", "result": "PASS", "waive": "False", "definition": "Custom Fields.Extended Fraud <PERSON>ert == <PERSON><PERSON>", "evaluatedDefinition": "NO FRAUD == FRAUD ALERT", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 130, "name": "Housing Adjustment", "type": "Housing Status Adjustment", "result": "PASS", "waive": "False", "definition": "Applicant.Mortgage/Rent < $400.00 and Applicant.Address HouseType != RENT and Applicant.Address HouseType != OWN", "evaluatedDefinition": "500.00 < 400.00 and RENT != RENT and RENT != OWN", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == INSTALLMENT WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "FAIL", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == INSTALLMENT WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "MORTGAGE == INSTALLMENT WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "FAIL", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == INSTALLMENT WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == INSTALLMENT WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == INSTALLMENT WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 56, "name": "Installment with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "NA == Installment with Payment and NA == 1 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == INSTALLMENT WITHOUT PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == INSTALLMENT WITHOUT PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "MORTGAGE == INSTALLMENT WITHOUT PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == INSTALLMENT WITHOUT PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == INSTALLMENT WITHOUT PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == INSTALLMENT WITHOUT PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 55, "name": "Installment without Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Installment without Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "NA == Installment without Payment and NA == 1 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 71, "name": "Max Mileage GT 72", "type": "Credit Policy Rules", "result": "PASS", "waive": "False", "definition": "Recommended.Tier != 7 and Recommended.Tier != 6 and Recommended.Tier != 5 and Recommended.Tier != 4 and Recommended.TermMonths > 72", "evaluatedDefinition": "1 != 7 and 1 != 6 and 1 != 5 and 1 != 4 and 72.00 > 72.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 2, "name": "Max Mileage LT 72", "type": "Credit Policy Rules", "eval_value": 72, "actual_value": 72, "result": "FAIL", "waive": "False", "definition": "Recommended.TermMonths <= 72", "evaluatedDefinition": "72.00 <= 72.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == MORTGAGE and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == MORTGAGE and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "FAIL", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "MORTGAGE == MORTGAGE and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == MORTGAGE and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == MORTGAGE and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == MORTGAGE and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 57, "name": "Mortgage", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Mortgage and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "NA == Mortgage and NA == 1 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 29, "name": "Must Trade Open Auto", "type": "Stipulations Rules", "eval_value": 1, "actual_value": 0, "result": "PASS", "waive": "False", "definition": "DMS.GP052 Nbr of Open Autos PB >= 1", "evaluatedDefinition": "0.00 >= 1.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 148, "name": "Open BK", "type": "Post-Bureau Rules", "eval_value": 0, "actual_value": 0, "result": "PASS", "waive": "False", "definition": "Custom Fields.Open BK Count > 0", "evaluatedDefinition": "0.00 > 0.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == OPEN-ENDED and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == OPEN-ENDED and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "MORTGAGE == OPEN-ENDED and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == OPEN-ENDED and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == OPEN-ENDED and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == OPEN-ENDED and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 58, "name": "Open-Ended", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Open-Ended and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "NA == Open-Ended and NA == 1 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "REV WITH PAYMENT == OTHER and True == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == OTHER and True == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "MORTGAGE == OTHER and True == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == OTHER and True == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "REV WITH PAYMENT == OTHER and True == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "REV WITH PAYMENT == OTHER and True == True", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 59, "name": "Other", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Other and Tradeline.IsOpen == 1", "evaluatedDefinition": "NA == Other and NA == 1", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 31, "name": "Proof of Income", "type": "Stipulations Rules", "eval_value": 1, "actual_value": 1082, "result": "FAIL", "waive": "False", "definition": "Deal.Application Number > 1", "evaluatedDefinition": "1082.00 > 1.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 30, "name": "Proof of Residence", "type": "Stipulations Rules", "eval_value": 1, "actual_value": 1082, "result": "FAIL", "waive": "False", "definition": "Deal.Application Number > 1", "evaluatedDefinition": "1082.00 > 1.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == REV WIHTOUT PAYMENT and True == True and 666.00 > 0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == REV WIHTOUT PAYMENT and True == True and 2736.00 > 0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "MORTGAGE == REV WIHTOUT PAYMENT and True == True and 3327.00 > 0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == REV WIHTOUT PAYMENT and True == True and 2577.00 > 0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == REV WIHTOUT PAYMENT and True == True and 255.00 > 0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == REV WIHTOUT PAYMENT and True == True and 104.00 > 0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 44, "name": "Rev 4% without payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev wihtout Payment and Tradeline.IsOpen == 1 and Tradeline.Balance > $0.00 and Tradeline.ECOA == I", "evaluatedDefinition": "NA == Rev wihtout Payment and NA == 1 and NA > $0.00 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "FAIL", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == REV WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == REV WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "MORTGAGE == REV WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == REV WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "FAIL", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == REV WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "FAIL", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "REV WITH PAYMENT == REV WITH PAYMENT and True == True and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 54, "name": "Rev with Payment", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Rev with Payment and Tradeline.IsOpen == 1 and Tradeline.ECOA == I", "evaluatedDefinition": "NA == Rev with Payment and NA == 1 and I == I", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 157, "name": "SSN Discrepancy Flag CB", "type": "Post-Bureau Rules", "eval_value": 1, "result": "NA", "waive": "False", "definition": "DMS.GP151 SSN Discrepancy CB == 1", "evaluatedDefinition": "NA == 1", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 158, "name": "SSN Discrepancy Flag PB", "type": "Post-Bureau Rules", "eval_value": 1, "actual_value": 0, "result": "PASS", "waive": "False", "definition": "DMS.GP151 SSN Discrepancy PB == 1", "evaluatedDefinition": "0.00 == 1.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "REV WITH PAYMENT == STUDENT LOANS and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == STUDENT LOANS and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "MORTGAGE == STUDENT LOANS and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "INSTALLMENT WITH PAYMENT == STUDENT LOANS and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "REV WITH PAYMENT == STUDENT LOANS and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "REV WITH PAYMENT == STUDENT LOANS and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 61, "name": "Student Loans", "type": "Tradeline Rules", "result": "PASS", "waive": "False", "definition": "Tradeline.Category == Student Loans and Tradeline.Special Comment Code != PDE", "evaluatedDefinition": "NA == Student Loans and NA != PDE", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 10, "name": "Tier 1", "type": "Tier Rules", "eval_value": 641, "actual_value": 740, "result": "FAIL", "waive": "False", "definition": "Custom Fields.FICO for Application >= 641", "evaluatedDefinition": "740.00 >= 641.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 9, "name": "Tier 2", "type": "Tier Rules", "result": "PASS", "waive": "False", "definition": "Custom Fields.FICO for Application >= 611 and Custom Fields.FICO for Application <= 640", "evaluatedDefinition": "740.00 >= 611.00 and 740.00 <= 640.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 8, "name": "Tier 3", "type": "Tier Rules", "result": "PASS", "waive": "False", "definition": "Custom Fields.FICO for Application >= 581 and Custom Fields.FICO for Application <= 610", "evaluatedDefinition": "740.00 >= 581.00 and 740.00 <= 610.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 7, "name": "Tier 4", "type": "Tier Rules", "result": "PASS", "waive": "False", "definition": "Custom Fields.FICO for Application >= 561 and Custom Fields.FICO for Application <= 580", "evaluatedDefinition": "740.00 >= 561.00 and 740.00 <= 580.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 6, "name": "Tier 5", "type": "Tier Rules", "result": "PASS", "waive": "False", "definition": "Custom Fields.FICO for Application >= 541 and Custom Fields.FICO for Application <= 560", "evaluatedDefinition": "740.00 >= 541.00 and 740.00 <= 560.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 5, "name": "Tier 6", "type": "Tier Rules", "result": "PASS", "waive": "False", "definition": "Custom Fields.FICO for Application >= 520 and Custom Fields.FICO for Application <= 540", "evaluatedDefinition": "740.00 >= 520.00 and 740.00 <= 540.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 4, "name": "Tier 7", "type": "Tier Rules", "eval_value": 520, "actual_value": 740, "result": "PASS", "waive": "False", "definition": "Custom Fields.FICO for Application < 520", "evaluatedDefinition": "740.00 < 520.00", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 159, "name": "Trans Union OFAC Alert Applicant", "type": "Post-Bureau Rules", "eval_value": "Yes", "result": "NA", "waive": "False", "definition": "DMS.TU OFAC PB == Yes", "evaluatedDefinition": "NA == Yes", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 160, "name": "Trans Union OFAC Alert Coapplicant", "type": "Post-Bureau Rules", "result": "PASS", "waive": "False", "definition": "CoApplicant.IsActive == 1 and DMS.TU OFAC CB == Yes", "evaluatedDefinition": "False == True and NA == Yes", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}, {"id": 67, "name": "Welcome Call", "type": "Stipulations Rules", "result": "PASS", "waive": "False", "definition": "Recommended.Tier != 1 and Recommended.Tier != 2 and Recommended.Tier != 3", "evaluatedDefinition": "1 != 1 and 1 != 2 and 1 != 3", "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T08:54:09.3698186", "updated_by": "jmcclintick", "deal_detail_id": 207, "bundle_id": 1082}]}}, "CustomFields": {"CustomField": [{"group": "Custom Fields", "value": 740, "text": "FICO for Application", "id": 216260}, {"group": "Custom Fields", "value": "Decisioned", "text": "UW Process Status", "id": 216265}, {"group": "Custom Fields", "value": "14 Years 0 Months", "text": "App Time at Residence", "id": 216278}, {"group": "Custom Fields", "value": "0 Years 0 Months", "text": "CoApp Time at Residence", "id": 216279}, {"group": "Custom Fields", "value": "15 Years 0 Months", "text": "App Time on Job", "id": 216280}, {"group": "Custom Fields", "value": "0 Years 0 Months", "text": "CoApp Time on Job", "id": 216281}, {"group": "Custom Fields", "value": "No", "text": "TU OFAC PB", "id": 216284}, {"group": "Custom Fields", "value": "No", "text": "TU OFAC CB", "id": 216285}, {"group": "Tradeline", "text": "ECOA", "id": 216313}, {"group": "Recommended", "value": 2, "text": "Participation Percentage", "id": 216339}, {"group": "Custom Fields", "value": 2.5, "text": "Participation Prepop", "id": 216348}, {"group": "Custom Fields", "value": 13.01, "text": "Customer Rate", "id": 216349}, {"group": "Custom Fields", "value": 14, "text": "Recommended Customer Rate", "id": 216350}, {"group": "Custom Fields", "value": 26234, "text": "Front End Advance UW", "id": 216351}, {"group": "Recommended", "value": 68.81, "text": "FLTV", "id": 216352}, {"group": "Custom Fields", "value": "Pass", "text": "Auth Trades GT 25 Percent", "id": 216393}, {"group": "Custom Fields", "value": 0, "text": "SmartCalcs Total Sales Price", "id": 216394}, {"group": "Custom Fields", "text": "Empty", "id": 216395}, {"group": "Custom Fields", "value": 579.48, "text": "Retail Payment Buy Rate UW", "id": 216396}, {"group": "Custom Fields", "value": 11286.56, "text": "Interest Amount Buy Rate UW", "id": 216397}, {"group": "Custom Fields", "value": 13566.08, "text": "Interest Amount Customer Rate UW", "id": 216398}, {"group": "Custom Fields", "value": 2270, "text": "Participation Amount UW", "id": 216399}, {"group": "Custom Fields", "value": 0, "text": "Short Fund Amount", "id": 216400}, {"group": "Custom Fields", "value": 31408, "text": "Disbursement UW", "id": 216401}, {"group": "Custom Fields", "value": 0, "text": "Front End Advance VF", "id": 216402}, {"group": "Custom Fields", "text": "FLTV VF", "id": 216403}, {"group": "Custom Fields", "value": 195, "text": "Assigment Fee", "id": 216404}, {"group": "Custom Fields", "value": 0, "text": "Interest Buy Rate VF", "id": 216409}, {"group": "Custom Fields", "value": 0, "text": "Retail Payment Buy Rate VF", "id": 216410}, {"group": "Custom Fields", "value": 0, "text": "Participation Amount VF", "id": 216411}, {"group": "Custom Fields", "value": 195, "text": "Discount Amount", "id": 216412}, {"group": "Custom Fields", "value": -390, "text": "Disbursement VF", "id": 216413}, {"group": "Custom Fields", "text": "Type of Verification", "id": 216418}, {"group": "Custom Fields", "text": "Likelihood of Continues Employment", "id": 216419}, {"group": "Custom Fields", "text": "Short Fund Amount VF", "id": 216420}, {"group": "Custom Fields", "value": 195, "text": "Assigment Fee VF", "id": 216421}, {"group": "Custom Fields", "text": "Employment Start Date", "id": 216422}, {"group": "Custom Fields", "value": 12000, "text": "Employment Income", "id": 216423}, {"group": "Custom Fields", "text": "Employment Bonus Tips Com", "id": 216424}, {"group": "Custom Fields", "value": "No Fraud", "text": "Extended <PERSON><PERSON>", "id": 216425}, {"group": "Custom Fields", "value": "<PERSON><PERSON> Present Must Be Cleared", "text": "<PERSON><PERSON>", "id": 216426}, {"group": "Custom Fields", "value": "NaN/NaN/NaN", "text": "Booked Date for Queue", "id": 216427}, {"group": "Custom Fields", "value": "No", "text": "Employment Verification Completed", "id": 216460}, {"group": "Custom Fields", "text": "Days to First Payment", "id": 216461}, {"group": "Custom Fields", "text": "Odd Days RIC", "id": 216462}, {"group": "Custom Fields", "value": "05/25/1989", "text": "SSI Applicant DOB", "id": 216463}, {"group": "Custom Fields", "text": "SSI CoApplicant DOB", "id": 216464}, {"group": "Custom Fields", "text": "SSI Contract Date", "id": 216465}, {"group": "Custom Fields", "value": "FALSE", "text": "SSI Applicant SelfEmployed", "id": 216466}, {"group": "Custom Fields", "text": "SSI CoApplicant SelfEmployed", "id": 216467}, {"group": "Custom Fields", "value": 0, "text": "Participation Sixty Split VF", "id": 216468}, {"group": "Custom Fields", "value": 1362, "text": "Participation Sixty Split UW", "id": 216469}, {"group": "Custom Fields", "text": "SSI CoApplicant CIF Number", "id": 216470}, {"group": "Custom Fields", "value": "1082-SA", "text": "SSI Applicant CIF Number", "id": 216471}, {"group": "Custom Fields", "value": "1082-SA", "text": "SSI Applicant Loan Number", "id": 216472}, {"group": "Custom Fields", "text": "SSI CIFPortfolioName CB", "id": 216473}, {"group": "Custom Fields", "text": "SSI Entity CB", "id": 216474}, {"group": "Custom Fields", "text": "SSI Phone Desc CB", "id": 216475}, {"group": "Custom Fields", "text": "SSI Operation CB", "id": 216476}, {"group": "Custom Fields", "text": "SSI RelationshipCode CB", "id": 216477}, {"group": "Custom Fields", "text": "SSI NumOfYearsOnJob CB", "id": 216478}, {"group": "Custom Fields", "text": "SSI Direct Deposit CB", "id": 216479}, {"group": "Custom Fields", "text": "SSI PrimaryEmployer CB", "id": 216480}, {"group": "Custom Fields", "text": "SSI PhoneDesc Employer CB", "id": 216481}, {"group": "Custom Fields", "text": "SSI PhoneDesc Mobile CB", "id": 216482}, {"group": "Custom Fields", "text": "SSI PhoneNumberDoNotParse CB", "id": 216483}, {"group": "Custom Fields", "text": "SSI MobileNumberFlag CB", "id": 216484}, {"group": "Custom Fields", "text": "SSI RelationshipCode CoBorrower  CB", "id": 216485}, {"group": "Bureau", "value": "[{\"Bureau\": \"TransUnion\"\"Address1\":\"2 Baldwin Place\"\"Address2\":\"P.O. Box 10000\"\"CityStateZip\":\"Chester PA 19022\"\"Phone\":\"************\"}]", "text": "Active Bureau Contact Info JSON", "id": 216486}, {"group": "Bureau", "value": "TransUnion", "text": "Active Bureau Name", "id": 216487}, {"group": "Bureau", "value": "2 Baldwin Place", "text": "Active Bureau Address1", "id": 216488}, {"group": "Bureau", "text": "Active Bureau Address1 CB", "id": 216489}, {"group": "Bureau", "value": "P.O. Box 10000", "text": "Active Bureau Address2", "id": 216490}, {"group": "Bureau", "text": "Active Bureau Address2 CB", "id": 216491}, {"group": "Bureau", "value": "Chester PA 19022", "text": "Active Bureau CityStateZip", "id": 216492}, {"group": "Bureau", "text": "Active Bureau CityStateZip CB", "id": 216493}, {"group": "Bureau", "value": "************", "text": "Active Bureau Phone Number", "id": 216494}, {"group": "Bureau", "text": "Active Bureau Phone Number CB", "id": 216495}, {"group": "Bureau", "value": "[&#xD;&#xA;  undefined&#xD;&#xA;]", "text": "Active Bureau Contact Info JSON CB", "id": 216496}, {"group": "Bureau", "text": "Active Bureau Name CB", "id": 216497}, {"group": "Custom Fields", "value": "<br>TransUnion <br>2 Baldwin Place <br>P.O. Box 10000 <br>Chester PA 19022 <br>************", "text": "Concatenate PB Bureau Date for DO", "id": 216498}, {"group": "Custom Fields", "text": "Concatenate CB Bureau Date for DO", "id": 216499}, {"group": "Custom Fields", "text": "Adverse Action Reasons List PB", "id": 216500}, {"group": "Custom Fields", "text": "Adverse Action Reasons List CB", "id": 216501}, {"group": "Custom Fields", "text": "SSI CoApplicant CIF-Relationship", "id": 216508}, {"group": "Custom Fields", "value": "RICH MESCHA", "text": "Dealer User", "id": 216509}, {"group": "Custom Fields", "value": 0, "text": "Open BK Count", "id": 216515}, {"group": "Custom Fields", "value": 25870.6, "text": "Adjusted Requested Amount Financed", "id": 216528}, {"group": "Custom Fields", "text": "SSI First Principal and Interest Payment Date", "id": 216535}, {"group": "Custom Fields", "text": "Other 1", "id": 216536}, {"group": "Custom Fields", "text": "Other 2", "id": 216537}, {"group": "Custom Fields", "text": "Other 1 Description", "id": 216538}, {"group": "Custom Fields", "text": "Other 2 Description", "id": 216539}, {"group": "Custom Fields", "value": 390, "text": "Fee + Assignment Fee", "id": 216540}, {"group": "Custom Fields", "value": "[]", "text": "LUT Rate Chart JSON", "id": 216541}, {"group": "Custom Fields", "text": "Base Rate", "id": 216542}, {"group": "Custom Fields", "text": "LTVAdder", "id": 216543}, {"group": "Custom Fields", "text": "PTIAdder", "id": 216544}, {"group": "Custom Fields", "value": "N/A", "text": "Suggested Rate", "id": 216545}, {"group": "Custom Fields", "text": "SSI Maturity Date", "id": 216549}, {"group": "Custom Fields", "value": 4444444444, "text": "SSI Dealer Phone", "id": 216550}]}, "deal_type": "RECOMMENDED", "lender_id": "SA5", "request_date": "2022-06-07T13:46:00.0000000", "payment_call": "False", "source_system_id": "BBF3700439", "source_system": "DT", "version": 1, "deal_detail_id": 207, "source_dealer_id": 269472, "app_status": "A", "perform_dupe_check": 1, "create_date": "2022-06-07T13:46:35.9130000", "update_date": "2022-08-18T09:17:01.6970000", "updated_by": "jmcclintick", "decisioned_by": "jmcclintick", "bundle_id": 1082, "status_last_changed_on": "2022-06-07T13:48:06.6300000"}, {"Dealer": {"dealer_type": "Independent", "document_delivery": "Both", "dealer_id": 242205, "client_dealer_id": 12345, "dealer_state": "TX", "name": "DT Test Dealer", "source_dealer_id": 269472, "enrollment_date": "2022-05-26T00:00:00.0000000", "phone_number": 4444444444, "address": "1234 defi Way", "city": "Somewhere", "postal_code": 76087, "email": "<EMAIL>", "active": "True", "create_date": "2022-05-26T15:59:49.5470000", "update_date": "2022-11-21T11:12:39.6630000", "updated_by": "jmcclintick"}, "LoanApplicationStates": {"LoanApplicationState": {"CreditPolicy": {"min_income": 0, "max_pti": 0, "max_dti": 0, "max_af": 0, "min_af": 0, "max_term": 0, "min_payment": 0, "max_vehicle_age": 0, "max_miles": 0, "max_line3_percent": 0, "max_line5_percent": 0, "usury": 0, "create_date": "2022-06-07T13:48:43.3770000", "update_date": "2022-06-07T13:48:43.3770000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, "Stipulations": {"Stipulation": [{"Documents": {"Document": {"DocQuestions": {"DocQuestion": [{"id": 638, "name": "Does contract match approval?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 10, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6300000", "updated_by": "rmescha", "document_id": 491, "deal_detail_id": 209, "bundle_id": 1082}, {"id": 639, "name": "Is the document signed by dealer & customer?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 11, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6300000", "updated_by": "rmescha", "document_id": 491, "deal_detail_id": 209, "bundle_id": 1082}]}, "id": 491, "name": "Retail Installment Contract", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6100000", "updated_by": "rmescha", "stipulation_id": 2, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 2, "name": "Retail Installment Contract", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.5970000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": {"id": 640, "name": "Is the document signed?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 2, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6300000", "updated_by": "rmescha", "document_id": 492, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 492, "name": "Credit Application", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6170000", "updated_by": "rmescha", "stipulation_id": 4, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 4, "name": "Signed Credit App", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.5970000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": {"id": 641, "name": "Is the document signed by dealer & customer?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 11, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6330000", "updated_by": "rmescha", "document_id": 493, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 493, "name": "Buyers Order", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6170000", "updated_by": "rmescha", "stipulation_id": 6, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 6, "name": "Signed Buyers Order", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6000000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": [{"id": 642, "name": "Is the document valid?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 3, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6330000", "updated_by": "rmescha", "document_id": 494, "deal_detail_id": 209, "bundle_id": 1082}, {"id": 643, "name": "Does Unit and mileage match approval and book out?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 12, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6330000", "updated_by": "rmescha", "document_id": 494, "deal_detail_id": 209, "bundle_id": 1082}]}, "id": 494, "name": "Title Application", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6200000", "updated_by": "rmescha", "stipulation_id": 8, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 8, "name": "Title Application", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6000000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": {"id": 644, "name": "Does document mileage match system?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 13, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6370000", "updated_by": "rmescha", "document_id": 495, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 495, "name": "Odometer Statement", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6200000", "updated_by": "rmescha", "stipulation_id": 10, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 10, "name": "Odometer Statement", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6030000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": {"id": 645, "name": "Does value match system?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 14, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6370000", "updated_by": "rmescha", "document_id": 496, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 496, "name": "Invoice or Bookout", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6200000", "updated_by": "rmescha", "stipulation_id": 12, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 12, "name": "Invoice or Bookout", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6030000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": [{"id": 646, "name": "Is the document valid?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 3, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6370000", "updated_by": "rmescha", "document_id": 497, "deal_detail_id": 209, "bundle_id": 1082}, {"id": 647, "name": "Is the max deductible $1500", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 4, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6400000", "updated_by": "rmescha", "document_id": 497, "deal_detail_id": 209, "bundle_id": 1082}]}, "id": 497, "name": "ATPI", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6230000", "updated_by": "rmescha", "stipulation_id": 14, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 14, "name": "ATPI", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6030000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": [{"id": 648, "name": "All product certificates signed by customer?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 16, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6400000", "updated_by": "rmescha", "document_id": 498, "deal_detail_id": 209, "bundle_id": 1082}, {"id": 649, "name": "Are all products listed on the app represented by a document?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 15, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6430000", "updated_by": "rmescha", "document_id": 498, "deal_detail_id": 209, "bundle_id": 1082}]}, "id": 498, "name": "Ancillary Product Certificates", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6230000", "updated_by": "rmescha", "stipulation_id": 16, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 16, "name": "Ancillary Product Certificates", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6070000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": {"id": 650, "name": "Is the document valid?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 3, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6430000", "updated_by": "rmescha", "document_id": 499, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 499, "name": "Valid DL or Government Issued ID", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6230000", "updated_by": "rmescha", "stipulation_id": 18, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 18, "name": "Valid DL or Government Issued ID", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6070000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": {"id": 651, "name": "4 complete references entered in system?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 17, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6430000", "updated_by": "rmescha", "document_id": 500, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 500, "name": "Personal References", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6270000", "updated_by": "rmescha", "stipulation_id": 20, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 20, "name": "4 Personal References", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6070000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": [{"id": 652, "name": "Is the docs from last 30 days?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 5, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6470000", "updated_by": "rmescha", "document_id": 501, "deal_detail_id": 209, "bundle_id": 1082}, {"id": 653, "name": "Does the verified income match the system?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 19, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6470000", "updated_by": "rmescha", "document_id": 501, "deal_detail_id": 209, "bundle_id": 1082}]}, "id": 501, "name": "Proof of Income", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6270000", "updated_by": "rmescha", "stipulation_id": 24, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 24, "name": "Proof of Income", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6100000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Documents": {"Document": {"DocQuestions": {"DocQuestion": [{"id": 654, "name": "Is the docs from last 30 days?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 5, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6470000", "updated_by": "rmescha", "document_id": 502, "deal_detail_id": 209, "bundle_id": 1082}, {"id": 655, "name": "Does the address match the contract?", "type": "Boolean", "status": "Passed", "waive": "False", "eval_value": 1, "actual": 1, "question_id": 18, "create_date": "2022-06-07T13:48:43.4000000", "update_date": "2022-06-07T13:52:29.6500000", "updated_by": "rmescha", "document_id": 502, "deal_detail_id": 209, "bundle_id": 1082}]}, "id": 502, "name": "Proof of Residence", "type": "Received", "status": "Passed", "waive": "False", "create_date": "2022-06-07T13:48:43.3930000", "update_date": "2022-06-07T13:52:29.6270000", "updated_by": "rmescha", "stipulation_id": 22, "deal_detail_id": 209, "bundle_id": 1082}}, "id": 22, "name": "Proof of Residence", "type": "UW", "status": "Passed", "reason": "None", "waive": "False", "create_date": "2022-06-07T13:47:49.6130000", "update_date": "2022-06-07T13:52:29.6100000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}]}, "DealDetails": {"Structure": {"scenario_id": 209, "deal_detail_id": 209, "cash_down": 1000, "net_trade": 7666, "unpaid_balance": 30436.27, "estimated_amount_financed": 30436.27, "term_months": 72, "warranty": 799, "contract_rate": 13.01, "tier_level": 1, "ltv_ratio": 79.83, "calculated_discount": 195, "max_amount_financed": 0, "item_code": "RECOMMENDED", "pti": 5.09, "total_down": 8666, "vehicle_max_term": 0, "amount_financed_max_term": 0, "total_income": 12000, "payment": 611.15, "estimated_payment": 611.15, "dti": 15.58, "participation": 0, "discount_percent": 0.64, "back_end": 699, "max_pti": 0, "max_dti": 0, "max_payment": 0, "sale_price": 34900, "min_income": 0, "min_payment": 0, "max_vehicle_age": 0, "max_miles": 0, "max_line3_percent": 0, "max_line5_percent": 0, "min_af": 0, "ttl_estimate": 99, "accidental_health_insurance": 0, "credit_life_insurance": 0, "gap": 995, "sales_tax": 1610, "front_end_fees": 0, "usury": 0, "create_date": "2022-06-07T13:46:36.0130000", "update_date": "2022-08-18T09:17:00.9930000", "updated_by": "jmcclintick", "bundle_id": 1082}}, "Customers": {"Customer": {"Reports": {"Report": {"active": "True", "valid": "True", "bureau": "TU", "report_date": "2022-06-02T15:20:28.5870000", "score": 740, "raw_xml": "<Response Score=\"740\"><DMSXML><DMSCommon XMLindent=\"true\" Tracking=\"e3704323-36a0-4d28-958e-ad205eb16185\" DMSAppId=\"COM18872893\" DMSAccountNumber=\"TST0418221\" DMSAccountName=\"625293-Solera Af\" DMSAccountId=\"129\" Repull=\"1\" ReturnFormat=\"DMSCommon\" ReturnInputs=\"false\" ReturnAllProducts=\"false\" ReturnInquiries=\"false\" ApplicationDate=\"2022-06-02T15:20:28\" DateGenerated=\"2022-06-02T15:20:28\" Status=\"Ok\"><Products><Product Id=\"Product1\" ProductId=\"3004\" Vendor=\"TransUnion\" ProductName=\"Credit Report\" ProductVersion=\"4.0\" DLLVersion=\"*********\" ReturnResult=\"true\" ReturnFormat=\"TTY\" DateGenerated=\"2022-06-02T15:20:28\" Entities=\"E1\" Status=\"Ok\"><ProductResult Format=\"TTY\">                                                                            &#xD;&#xA;********-36A0-4D28-958E-  TRANSUNION CREDIT REPORT                          &#xD;&#xA;                                                                            &#xD;&#xA;&lt;FOR&gt;          &lt;SUB NAME&gt;          &lt;MKT SUB&gt;  &lt;INFILE&gt;   &lt;DATE&gt;      &lt;TIME&gt; &#xD;&#xA;(I) F ********* SOLERA AUTO         03 PE      9/97      06/02/22    15:20CT&#xD;&#xA;                                                                            &#xD;&#xA;&lt;SUBJECT&gt;                                          &lt;SSN&gt;        &lt;BIRTH DATE&gt;&#xD;&#xA;DANCER, LINE                                       ***********  10/76       &#xD;&#xA;&lt;CURRENT ADDRESS&gt;                                               &lt;DATE RPTD&gt; &#xD;&#xA;525 S. COUNTRY RD., FANTASY ISLAND IL. 60750                     9/21       &#xD;&#xA;&lt;FORMER ADDRESS&gt;                                                            &#xD;&#xA;11 RIDGE WY., FANTASY ISLAND IL. 60750                           1/18       &#xD;&#xA;                                         &lt;POSITION&gt;                         &#xD;&#xA;&lt;CURRENT EMPLOYER AND ADDRESS&gt;                     &lt;VERF&gt; &lt;RPTD&gt;            &#xD;&#xA;DEFI                                     MANAGER                            &#xD;&#xA;                                                    4/22V  4/22             &#xD;&#xA;&lt;FORMER EMPLOYER AND ADDRESS&gt;                                               &#xD;&#xA;USGS                                                                        &#xD;&#xA;                                                    4/22V  4/22             &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;S P E C I A L   M E S S A G E S                                             &#xD;&#xA;****TRUVALIDATE FRAUD ALERTS: CLEAR FOR ALL SEARCHES PERFORMED***           &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;M O D E L   P R O F I L E          * * * A L E R T * * *                    &#xD;&#xA;***VANTAGESCORE 4 SCORE +740  : 38, 49, 44, 50 SCORECARD :07*** IN ADDITION &#xD;&#xA;***TO THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S   &#xD;&#xA;***CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                     &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;P U B L I C   R E C O R D S                                                 &#xD;&#xA;SOURCE    DATE     LIAB     ECOA COURT ASSETS   PAID  DOCKET#               &#xD;&#xA;TYPE                                                  PLAINTIFF/ATTORNEY    &#xD;&#xA;Z 5048079  4/20R   $0       I    FE             7/21  *********             &#xD;&#xA;CH 7 BANKRUPTCY DISMISSED/CLOSED                      JOSEPH B GREEN        &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;T R A D E S                                                                 &#xD;&#xA;SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP &#xD;&#xA;ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24     &#xD;&#xA;ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90  &#xD;&#xA;FNCC         B 41PF037 10/15   $1002                       **********11 R01 &#xD;&#xA;4939394                 4/22V           $0                 1X1111111X11     &#xD;&#xA;I                              $666                           24   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;CITIFINANCIA F 7212205  5/21   $3929    015M262            **********   I01 &#xD;&#xA;800858                  4/22A           $0                                  &#xD;&#xA;I                              $2736                          10   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;WILM TRUST   B 848R015  4/93   $36.5K   300M163            **********11 M01 &#xD;&#xA;698007                  4/22V           $0                 **********11     &#xD;&#xA;I    REAL ESTATE               $3327                          24   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;AVCO FINANCE F 321E430 11/19   $3591    036M145            X**********1 I01 &#xD;&#xA;32443                   4/22V           $0                 111X11111111     &#xD;&#xA;I    SECRD;HSHLD GDS           $2577                          28   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;CHASE        B 701N090 10/18            MIN67              **********11 R01 &#xD;&#xA;3                       4/22V  $2500    $0                 **********11     &#xD;&#xA;I                              $255                           34   0/ 0/ 0  &#xD;&#xA;                                                                            &#xD;&#xA;BK OF AMER   B 1597029  6/19   $1005                       **********11 R01 &#xD;&#xA;4676656                 3/22V  $2500    $0                 **********11     &#xD;&#xA;I                              $104                           24   0/ 0/ 0  &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;I N Q U I R I E S                                                           &#xD;&#xA;DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                      &#xD;&#xA; 6/02/22 F*********(CHI) SOLERA AUTO                                        &#xD;&#xA; 4/18/22 BLO2382255(IND) HSBC                                               &#xD;&#xA; 4/18/22 YPE2462756(IND) MIDLAND CRED                                       &#xD;&#xA;----------------------------------------------------------------------------&#xD;&#xA;C R E D I T  R E P O R T  S E R V I C E D  B Y :                            &#xD;&#xA;TRANSUNION TEST FACILITY                                      ************  &#xD;&#xA;555 W ADAMS CHICAGO, IL 60661                                               &#xD;&#xA;CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:          &#xD;&#xA;     HTTP://WWW.TRANSUNION.COM                                              &#xD;&#xA;                                                                            &#xD;&#xA;                            END OF TRANSUNION REPORT                        &#xD;&#xA;</ProductResult></Product><Product Id=\"Merge1\" ProductId=\"1003\" Vendor=\"DMS\" ProductName=\"MERGE\" ProductVersion=\"2.0\" DLLVersion=\"*********\" ReturnFormat=\"XML,0,ND,PD\" DateGenerated=\"2022-06-02T15:20:28\" Products=\"Product1\" Entities=\"E1\"><ProductResult Format=\"XML,TU\"><INsegments><INsegment SourceSegID=\"DMS5\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"22\" BureauHitLevelID=\"0A1@22\" DedupFlag=\"0\" tracking=\"********-36A0-4D28-958E-AD205EB16185\" name=\"LINE DANCER\" ssn=\"666393933\" street=\"525 COUNTRY LN\" citystatezip=\"FANTASY ISLAND,IL 60750\" appid=\"COM18872893\" mergereportdate=\"2022-06-02\" mergereportdateType=\"YMD\" bdate=\"1985-06-12\" bdateType=\"YMD\" version=\"919043000\" XMLversion=\"V_1.0.0.10\" XMLcrc=\"-1204256204\" reptypes=\"I\" bureaus=\"4\" scripts=\"DMS_SUPP_ATTR_0000\"></INsegment></INsegments><IDsegments><IDsegment SourceSegID=\"SH01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"0\" BureauHitLevelID=\"4A1@0\" DedupFlag=\"0\" subjectid=\"1\" first=\"LINE\" last=\"DANCER\" ssn=\"666393933\" datebirth=\"1976-10-01\" datebirthType=\"YMD\" dateonfile=\"1997-09-26\" dateonfileType=\"YMD\" daterep=\"2022-06-02\" daterepType=\"YMD\" namesource=\"F\" nameind=\"1\" productCode=\"07000\" productText=\"TU Credit Report\" personalsource=\"F\" fileno=\"1\" filehit=\"Y\" ssnmatch=\"05\" indicator=\"N\" indicator2=\"N\" marketfile=\"03\" submarketfile=\"PE\" version=\"0\" country=\"1\" language=\"1\" userref=\"********-36A0-4D28-958E-\" market=\"0622\" member=\"F03562356\" datetrans=\"2022-06-02\" datetransType=\"YMD\" timetrans=\"15:20:28\" cpuversion=\"98\" name=\"LINE DANCER\"></IDsegment></IDsegments><CAsegments><CAsegment SourceSegID=\"AD01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"1\" BureauHitLevelID=\"4A1@1\" DedupFlag=\"0\" qualifier=\"1\" street=\"525 S COUNTRY RD\" houseno=\"525\" predirect=\"S\" streetname=\"COUNTRY\" streettype=\"RD\" cityonly=\"FANTASY ISLAND\" state=\"IL\" zip=\"60750\" daterep=\"2021-09-21\" daterepType=\"YMD\" sourceind=\"F\" former=\"0\" parsedhouse=\"525\" parsedstreet=\"S COUNTRY\" parsedstreettype=\"RD\" addressind=\"1\"></CAsegment><CAsegment SourceSegID=\"AD01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"2\" BureauHitLevelID=\"4A1@2\" DedupFlag=\"0\" qualifier=\"1\" street=\"11 RIDGE WY\" houseno=\"11\" streetname=\"RIDGE\" streettype=\"WY\" cityonly=\"FANTASY ISLAND\" state=\"IL\" zip=\"60750\" daterep=\"2018-01-24\" daterepType=\"YMD\" sourceind=\"F\" former=\"1\" parsedhouse=\"11\" parsedstreet=\"RIDGE\" parsedstreettype=\"WY\" addressind=\"1\"></CAsegment></CAsegments><EMsegments><EMsegment SourceSegID=\"EM01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"3\" BureauHitLevelID=\"4A1@3\" DedupFlag=\"0\" name=\"DEFI\" occupation=\"MANAGER\" dateveri=\"2022-04-10\" dateveriType=\"YMD\" vericode=\"V\" sourceind=\"F\" former=\"0\"></EMsegment><EMsegment SourceSegID=\"EM01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"4\" BureauHitLevelID=\"4A1@4\" DedupFlag=\"0\" name=\"USGS\" dateveri=\"2022-04-10\" dateveriType=\"YMD\" vericode=\"V\" sourceind=\"F\" former=\"1\"></EMsegment></EMsegments><DLsegments><DLsegment SourceSegID=\"SC01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"19\" BureauHitLevelID=\"4A1@19\" DedupFlag=\"0\" productCode=\"001NN\" productText=\"VantageScore 4.0\" score=\"740\" sign=\"+\" reason1Code=\"38\" reason1Text=\"Lack of bankcard account information.\" reason2Code=\"49\" reason2Text=\"Not enough balance paid down over time on revolving accounts.\" reason3Code=\"44\" reason3Text=\"Balances on revolving accounts are too high compared with credit limits.\" reason4Code=\"50\" reason4Text=\"Balances on personal installment accounts too high compared to loan amounts.\"></DLsegment></DLsegments><MIsegments><MIsegment SourceSegID=\"SC01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"20\" BureauHitLevelID=\"4A1@20\" DedupFlag=\"0\" msg1=\"FACTA: 5th Reason Code\" msg2=\"Inquiries impacted the credit score\" msg3=\"001NN=VantageScore 4.0\"></MIsegment></MIsegments><PUsegments><PUsegment SourceSegID=\"PR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"5\" BureauHitLevelID=\"4A1@5\" DedupFlag=\"0\" court=\"*********\" courtnameCode=\"FE\" courtnameText=\"Federal District\" docket=\"*********\" attorney=\"JOSEPH B GREEN\" typeCode=\"7D\" typeText=\"Chapter 7 Bankruptcy dismissed\" daterep=\"2020-04-09\" daterepType=\"YMD\" datepaid=\"2021-07-16\" datepaidType=\"YMD\" ecoa=\"I\" dateorigfile=\"2020-04-09\" dateorigfileType=\"YMD\" kob=\"Z\"></PUsegment></PUsegments><TRsegments><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"6\" BureauHitLevelID=\"4A1@6\" DedupFlag=\"0\" member=\"B041PF037\" kob=\"B\" membername=\"FNCC\" account=\"4939394\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amount=\"1002\" amounttype=\"H\" amount2type=\"L\" highcredit=\"1002\" balance=\"666\" pastdueno=\"0\" dateopen=\"2015-10-01\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"**********111X1111111X11\" commonprofile=\"+++++++++++++-+++++++-++------------------------\" monsrev=\"24\" rawterms=\"             \" monthpayamt=\"19\" monthpayamtInfo=\"ESTIMATED\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"R\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"R\" orgecoa=\"I\" fullrating=\"RB11   \" surating=\"BRRR1OAB\" suprofile=\"..++++++++++++++-+++++++-++------------------------..............................\" mopayamtind=\"E\" termlength=\"0\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"7\" BureauHitLevelID=\"4A1@7\" DedupFlag=\"0\" member=\"F07212205\" kob=\"F\" membername=\"CITIFINANCIA\" account=\"800858\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amount=\"3929\" amounttype=\"H\" amount2type=\"L\" highcredit=\"3929\" balance=\"2736\" pastdueno=\"0\" dateopen=\"2021-05-14\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"**********\" commonprofile=\"++++++++++-\" monsrev=\"10\" rawterms=\"015M000000262\" monthpayamt=\"262\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"A\" veriindText=\"Automated account\" accounttype=\"I\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"I\" orgecoa=\"I\" fullrating=\"IF11   \" surating=\"FNII1OAB\" suprofile=\"..+++++++++++-\" mopayamtind=\"P\" termlength=\"15\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"8\" BureauHitLevelID=\"4A1@8\" DedupFlag=\"0\" member=\"B0848R015\" kob=\"B\" membername=\"WILM TRUST\" account=\"698007\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" loantypeCode=\"RE\" loantypeText=\"Real Estate\" ecoa=\"I\" amount=\"36500\" amounttype=\"H\" amount2type=\"L\" highcredit=\"36500\" balance=\"3327\" pastdueno=\"0\" dateopen=\"1993-04-24\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"********************1111\" commonprofile=\"++++++++++++++++++++++++------------------------\" monsrev=\"24\" rawterms=\"300M000000163\" monthpayamt=\"163\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"M\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"M\" orgecoa=\"I\" fullrating=\"ML11   \" surating=\"REIM1OAB\" suprofile=\"..+++++++++++++++++++++++++------------------------..................................\" mopayamtind=\"P\" termlength=\"300\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"9\" BureauHitLevelID=\"4A1@9\" DedupFlag=\"0\" member=\"F0321E430\" kob=\"F\" membername=\"AVCO FINANCE\" account=\"32443\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" loantypeCode=\"SH\" loantypeText=\"Secured by Household Goods\" ecoa=\"I\" amount=\"3591\" amounttype=\"H\" amount2type=\"L\" highcredit=\"3591\" balance=\"2577\" pastdueno=\"0\" dateopen=\"2019-11-10\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"X**********1111X**********X1\" commonprofile=\"-++++++++++++++-++++++++++-+-\" monsrev=\"28\" rawterms=\"036M000000145\" monthpayamt=\"145\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"I\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"I\" orgecoa=\"I\" fullrating=\"IF11   \" surating=\"FNII1OAB\" suprofile=\"..+-++++++++++++++-++++++++++-+-\" mopayamtind=\"P\" termlength=\"36\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"10\" BureauHitLevelID=\"4A1@10\" DedupFlag=\"0\" member=\"B0701N090\" kob=\"B\" membername=\"CHASE\" account=\"3\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amounttype=\"H\" amount2=\"2500\" amount2type=\"L\" creditlimit=\"2500\" balance=\"255\" pastdueno=\"0\" dateopen=\"2018-10-31\" dateopenType=\"YMD\" daterep=\"2022-04-01\" daterepType=\"YMD\" payprofile=\"********************11111XXXX111X1\" commonprofile=\"+++++++++++++++++++++++++----+++-+--------\" monsrev=\"34\" rawterms=\"MIN *********\" monthpayamt=\"67\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"R\" profilestartdate=\"2022-03-01\" profilestartdateType=\"YMD\" dateveri=\"2022-04-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"R\" orgecoa=\"I\" fullrating=\"RB11   \" surating=\"BRRR1OAB\" suprofile=\"..++++++++++++++++++++++++++----+++-+--------\" mopayamtind=\"P\" termlength=\"0\"></TRsegment><TRsegment SourceSegID=\"TR01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"11\" BureauHitLevelID=\"4A1@11\" DedupFlag=\"0\" member=\"B01597029\" kob=\"B\" membername=\"BK OF AMER\" account=\"4676656\" statusCode=\"01\" statusText=\"Paid or paying as agreed\" ecoa=\"I\" amount=\"1005\" amounttype=\"H\" amount2=\"2500\" amount2type=\"L\" highcredit=\"1005\" creditlimit=\"2500\" balance=\"104\" pastdueno=\"0\" dateopen=\"2019-06-19\" dateopenType=\"YMD\" daterep=\"2022-03-01\" daterepType=\"YMD\" payprofile=\"********************1111\" commonprofile=\"++++++++++++++++++++++++---------\" monsrev=\"24\" rawterms=\"             \" monthpayamt=\"3\" monthpayamtInfo=\"ESTIMATED\" late30=\"0\" late60=\"0\" late90=\"0\" veriindCode=\"V\" veriindText=\"Manual account\" accounttype=\"R\" profilestartdate=\"2022-02-01\" profilestartdateType=\"YMD\" dateveri=\"2022-03-01\" dateveriType=\"YMD\" currency=\"1\" histverif=\"2\" accttype=\"R\" orgecoa=\"I\" fullrating=\"RB11   \" surating=\"BRRR1OAB\" suprofile=\"...+++++++++++++++++++++++++---------\" mopayamtind=\"E\" termlength=\"0\"></TRsegment></TRsegments><IQsegments><IQsegment SourceSegID=\"IN01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"12\" BureauHitLevelID=\"4A1@12\" DedupFlag=\"0\" daterep=\"2022-06-02\" daterepType=\"YMD\" member=\"F03562356\" membername=\"SOLERA AUTO\" ecoa=\"I\" kob=\"F\" market=\"06TR\" SUcat=\"BR\"></IQsegment><IQsegment SourceSegID=\"IN01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"13\" BureauHitLevelID=\"4A1@13\" DedupFlag=\"0\" daterep=\"2022-04-18\" daterepType=\"YMD\" member=\"*********\" membername=\"HSBC\" ecoa=\"I\" kob=\"B\" market=\"40LO\" SUcat=\"BR\"></IQsegment><IQsegment SourceSegID=\"IN01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"14\" BureauHitLevelID=\"4A1@14\" DedupFlag=\"0\" daterep=\"2022-04-18\" daterepType=\"YMD\" member=\"*********\" membername=\"MIDLAND CRED\" ecoa=\"I\" kob=\"Y\" market=\"40PE\" SUcat=\"MC\"></IQsegment></IQsegments><BUsegments><BUsegment SourceSegID=\"OB01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"15\" BureauHitLevelID=\"4A1@15\" DedupFlag=\"0\" name=\"TRANSUNION TEST FACILITY\" street=\"555 W ADAMS\" cityonly=\"CHICAGO\" state=\"IL\" zip=\"60661\" phone=\"************\"></BUsegment></BUsegments><RWsegments><RWsegment SourceSegID=\"AO01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"16\" BureauHitLevelID=\"4A1@16\" DedupFlag=\"0\" rawsegment=\"AO010170650001H01\"></RWsegment><RWsegment SourceSegID=\"AO01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"17\" BureauHitLevelID=\"4A1@17\" DedupFlag=\"0\" rawsegment=\"AO01017001NN04   \"></RWsegment><RWsegment SourceSegID=\"SC01\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"18\" BureauHitLevelID=\"4A1@18\" DedupFlag=\"0\" rawsegment=\"SC01034001NN+740   I38 49 44 50 07\"></RWsegment><RWsegment SourceSegID=\"ENDS\" BureauName=\"TU\" BureauNumber=\"4\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"21\" BureauHitLevelID=\"4A1@21\" DedupFlag=\"0\" rawsegment=\"ENDS010024\"></RWsegment></RWsegments><GPsegments><GPsegment SourceSegID=\"DMS_SUPP_ATTR_0000,4/1/2022 8:36 AM,mriley\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"33\" BureauHitLevelID=\"0A1@33\" DedupFlag=\"0\" GP001=\"T40_A_SUPP_AUTO\" GP002=\"0\" GP003=\"0\" GP004=\"0\" GP006=\"0\" GP007=\"0\" GP008=\"0\" GP009=\"0\" GP010=\"0\" GP011=\"0\" GP012=\"0\" GP013=\"0\" GP014=\"0\" GP015=\"0\" GP016=\"0\" GP017=\"0\" GP018=\"0\" GP019=\"0\" GP020=\"0\" GP021=\"0\" GP023=\"0\" GP024=\"0\" GP025=\"0\" GP026=\"0\" GP027=\"0\" GP028=\"0\" GP032=\"0\" GP033=\"0\" GP034=\"0\" GP035=\"0\" GP036=\"0\" GP037=\"0\" GP038=\"0\" GP039=\"0\" GP040=\"0\" GP041=\"0\" GP042=\"0\" GP043=\"0\" GP044=\"0\" GP045=\"0\" GP046=\"0\" GP047=\"0\" GP048=\"0\" GP049=\"0\" GP051=\"0\" GP052=\"0\" GP053=\"0\" GP056=\"0\" GP058=\"0\" GP059=\"0\" GP060=\"0\" GP066=\"0\" GP069=\"0\" GP070=\"0\" GP071=\"0\" GP080=\"0\" GP081=\"0\" GP082=\"1\" GP083=\"11\" GP085=\"11\" GP087=\"0\" GP088=\"0\" GP089=\"0\" GP090=\"0\" GP091=\"0\" GP092=\"0\" GP093=\"0\" GP094=\"0\" GP095=\"0\" GP096=\"0\" GP097=\"0\" GP098=\"0\" GP099=\"0\" GP100=\"0\" GP101=\"0\" GP102=\"0\" GP103=\"0\" GP104=\"0\" GP105=\"0\" GP106=\"0\" GP107=\"0\" GP108=\"0\" GP109=\"0\" GP110=\"0\" GP111=\"0\" GP112=\"0\" GP113=\"0\" GP123=\"0\" GP124=\"6\" GP125=\"0\" GP126=\"0\" GP128=\"349\" GP129=\"0\" GP131=\"262\" GP132=\"36500\" GP135=\"6002\" GP136=\"6\" GP137=\"0\" GP138=\"0\" GP142=\"0\" GP143=\"0\" GP144=\"0\" GP145=\"0\" GP146=\"1\" GP147=\"0\" GP148=\"0\" GP149=\"0\" GP150=\"0\" GP151=\"0\" GP152=\"0\" GP153=\"0\" GP154=\"0\" GP156=\"6002\" GP158=\"0\" GP160=\"0\" GP161=\"0\" GP162=\"0\" GP163=\"0\"></GPsegment></GPsegments><SUsegments><SUCIsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"23\" BureauHitLevelID=\"0A1@23\" DedupFlag=\"0\" SUCIcat=\"CI\" SUCI001=\"92\" SUCI002=\"1\" SUCI003=\"659\" SUCI007=\"350\" SUCI008=\"2\" SUCI009=\"2\" SUCI010=\"2\" SUCI011=\"2\" SUCI012=\"3\" SUCI013=\"0\" SUCI014=\"3\" SUCI015=\"0\" SUCI016=\"0\" SUCI017=\"0\" SUCI018=\"0\" SUCI019=\"2\" SUCI020=\"0\" SUCI021=\"0\" SUCI022=\"0\" SUCI023=\"0\" SUCI024=\"6\" SUCI025=\"100\" SUCI026=\"17\" SUCI027=\"407\" SUCI028=\"89\" SUCI029=\"350\" SUCI030=\"2\" SUCI031=\"13\" SUCI032=\"0\" SUCI033=\"5313\" SUCI034=\"1025\" SUCI035=\"919430000\" SUCI036=\"6002\" SUCI037=\"0\" SUCI038=\"0\" SUCI039=\"0\" SUCI040=\"0\" SUCI041=\"0\" SUCI042=\"0\" SUCI043=\"0\" SUCI044=\"0\" SUCI045=\"0\" SUCI046=\"0\" SUCI047=\"0\" SUCI048=\"0\" SUCI049=\"0\" SUCI050=\"0\" SUCI051=\"0\" SUCI052=\"0\" SUCI053=\"0\" SUCI054=\"0\" SUCI055=\"0\" SUCI056=\"0\" SUCI057=\"0\" SUCI058=\"0\" SUCI059=\"0\" SUCI060=\"0\" SUCI061=\"0\" SUCI062=\"26\" SUCI065=\"0\" SUCI070=\"0\" SUCI071=\"0\" SUCI072=\"0\" SUCI073=\"0\" SUCI074=\"0\" SUCI075=\"0\" SUCI076=\"0\" SUCI077=\"0\" SUCI078=\"36500\" SUCI079=\"350\" SUCI080=\"3327\" SUCI082=\"0\" SUCI083=\"3327\" SUCI084=\"1\" SUCI085=\"1\" SUCI087=\"0\" SUCI089=\"26\" SUCI090=\"13\" SUCI091=\"6\" SUCI092=\"0\" SUCI093=\"6\" SUCI094=\"0\" SUCI095=\"0\" SUCI096=\"0\" SUCI097=\"0\" SUCI098=\"0\" SUCI099=\"0\" SUCI100=\"0\" SUCI101=\"0\" SUCI102=\"0\" SUCI103=\"0\" SUCI104=\"3\" SUCI105=\"2\" SUCI106=\"6\" SUCI107=\"0\" SUCI108=\"2\" SUCI109=\"36500\" SUCI110=\"163\" SUCI111=\"1\" SUCI112=\"0\" SUCI113=\"0\" SUCI114=\"0\" SUCI115=\"6\" SUCI116=\"9665\" SUCI117=\"7520\" SUCI121=\"3\" SUCI122=\"0\" SUCI123=\"1025\" SUCI124=\"297\" SUCI136=\"6\" SUCI137=\"31\" SUCI138=\"407\" SUCI139=\"0\" SUCI140=\"0\" SUCI141=\"0\" SUCI142=\"0\" SUCI143=\"0\" SUCI144=\"0\" SUCI145=\"0\" SUCI146=\"0\" SUCI147=\"0\" SUCI148=\"0\" SUCI149=\"0\" SUCI150=\"0\" SUCI151=\"0\" SUCI152=\"0\" SUCI153=\"80\" SUCI154=\"89\" SUCI155=\"0\" SUCI156=\"0\" SUCI157=\"0\" SUCI158=\"0\" SUCI159=\"0\" SUCI160=\"0\" SUCI161=\"0\" SUCI162=\"0\" SUCI163=\"0\" SUCI164=\"0\" SUCI165=\"0\" SUCI166=\"0\" SUCI167=\"0\" SUCI168=\"0\" SUCI169=\"3327\" SUCI170=\"350\" SUCI171=\"0\" SUCI172=\"0\" SUCI173=\"0\" SUCI174=\"0\" SUCI175=\"0\" SUCI176=\"0\" SUCI177=\"0\" SUCI178=\"0\" SUCI179=\"0\" SUCI180=\"0\" SUCI181=\"0\" SUCI182=\"0\" SUCI183=\"0\" SUCI184=\"0\" SUCI185=\"26\" SUCI186=\"2\" SUCI189=\"0\" SUCI193=\"3327\" SUCI194=\"2\" SUCI196=\"6\" SUCI197=\"9665\" SUCI198=\"659\" SUCI199=\"5313\" SUCI200=\"1025\" SUCI201=\"163\" SUCI202=\"2500\" SUCI203=\"36500\" SUCI204=\"0\"></SUCIsegment><SUBRsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"26\" BureauHitLevelID=\"0A1@26\" DedupFlag=\"0\" SUBRcat=\"BR\" SUBR001=\"3\" SUBR002=\"3\" SUBR003=\"50\" SUBR004=\"3\" SUBR005=\"0\" SUBR006=\"0\" SUBR007=\"0\" SUBR008=\"0\" SUBR009=\"3\" SUBR011=\"36\" SUBR012=\"0\" SUBR013=\"0\" SUBR014=\"0\" SUBR015=\"0\" SUBR016=\"0\" SUBR017=\"0\" SUBR018=\"0\" SUBR019=\"0\" SUBR020=\"0\" SUBR021=\"0\" SUBR022=\"0\" SUBR023=\"0\" SUBR024=\"0\" SUBR025=\"0\" SUBR026=\"0\" SUBR027=\"0\" SUBR028=\"0\" SUBR029=\"0\" SUBR030=\"0\" SUBR031=\"0\" SUBR032=\"0\" SUBR033=\"0\" SUBR034=\"0\" SUBR035=\"0\" SUBR036=\"0\" SUBR037=\"0\" SUBR038=\"0\" SUBR039=\"89\" SUBR040=\"1025\" SUBR041=\"6002\" SUBR042=\"100\" SUBR043=\"17\" SUBR044=\"100\" SUBR045=\"1\" SUBR046=\"0\" SUBR047=\"0\" SUBR048=\"160\" SUBR049=\"53\" SUBR050=\"7\" SUBR051=\"2\" SUBR052=\"1\" SUBR053=\"0\" SUBR054=\"0\" SUBR055=\"0\" SUBR056=\"1\" SUBR057=\"1\" SUBR058=\"1\" SUBR059=\"1\" SUBR060=\"2\" SUBR061=\"666\" SUBR065=\"0\" SUBR066=\"0\" SUBR067=\"0\" SUBR068=\"0\" SUBR070=\"0\" SUBR071=\"0\" SUBR072=\"0\" SUBR073=\"0\" SUBR074=\"2500\"></SUBRsegment><SUC2segment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"25\" BureauHitLevelID=\"0A1@25\" DedupFlag=\"0\" SUC2cat=\"C2\" SUC2002=\"0\" SUC2003=\"0\" SUC2004=\"0\" SUC2005=\"0\" SUC2006=\"0\" SUC2007=\"0\" SUC2008=\"0\" SUC2009=\"0\" SUC2010=\"0\" SUC2011=\"0\" SUC2012=\"0\" SUC2013=\"0\" SUC2014=\"0\" SUC2015=\"0\" SUC2016=\"0\" SUC2018=\"26\" SUC2020=\"0\" SUC2021=\"0\" SUC2024=\"0\" SUC2025=\"2\" SUC2026=\"2\" SUC2027=\"6\" SUC2028=\"6\" SUC2029=\"0\" SUC2030=\"0\" SUC2031=\"0\" SUC2033=\"0\" SUC2034=\"0\" SUC2035=\"0\" SUC2036=\"0\" SUC2037=\"0\" SUC2038=\"2\" SUC2039=\"2\" SUC2040=\"2\" SUC2041=\"2\" SUC2042=\"2\" SUC2043=\"2\" SUC2044=\"2\" SUC2045=\"2\" SUC2046=\"2\" SUC2047=\"2\" SUC2048=\"2\" SUC2049=\"2\" SUC2050=\"2\" SUC2051=\"2\" SUC2052=\"2\" SUC2053=\"2\" SUC2054=\"2\" SUC2055=\"2\" SUC2056=\"2\" SUC2057=\"2\" SUC2058=\"2\" SUC2059=\"0\" SUC2060=\"0\" SUC2061=\"0\" SUC2062=\"0\" SUC2063=\"2500\" SUC2064=\"36500\" SUC2069=\"0\" SUC2070=\"0\" SUC2071=\"0\" SUC2072=\"0\" SUC2073=\"0\"></SUC2segment><SUFNsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"27\" BureauHitLevelID=\"0A1@27\" DedupFlag=\"0\" SUFNcat=\"FN\" SUFN001=\"2\" SUFN002=\"2\" SUFN003=\"33\" SUFN004=\"2\" SUFN005=\"0\" SUFN006=\"0\" SUFN007=\"0\" SUFN008=\"0\" SUFN009=\"2\" SUFN011=\"13\" SUFN012=\"0\" SUFN013=\"0\" SUFN014=\"0\" SUFN015=\"0\" SUFN016=\"0\" SUFN017=\"0\" SUFN018=\"0\" SUFN019=\"0\" SUFN020=\"0\" SUFN021=\"0\" SUFN022=\"0\" SUFN023=\"0\" SUFN024=\"0\" SUFN025=\"0\" SUFN026=\"0\" SUFN027=\"0\" SUFN028=\"0\" SUFN029=\"0\" SUFN030=\"0\" SUFN031=\"0\" SUFN032=\"0\" SUFN033=\"0\" SUFN034=\"0\" SUFN035=\"0\" SUFN036=\"0\" SUFN037=\"0\" SUFN038=\"0\" SUFN039=\"407\" SUFN040=\"5313\" SUFN041=\"7520\" SUFN042=\"100\" SUFN043=\"71\" SUFN044=\"100\" SUFN045=\"2\" SUFN046=\"0\" SUFN047=\"0\" SUFN048=\"44\" SUFN049=\"22\" SUFN050=\"4\" SUFN051=\"2\" SUFN052=\"0\" SUFN053=\"0\" SUFN054=\"0\" SUFN055=\"0\" SUFN056=\"0\" SUFN057=\"0\" SUFN058=\"0\" SUFN059=\"0\" SUFN061=\"2736\" SUFN065=\"0\" SUFN066=\"0\" SUFN067=\"0\" SUFN068=\"0\" SUFN070=\"0\" SUFN071=\"0\" SUFN072=\"0\" SUFN073=\"0\" SUFN074=\"3929\" SUFN075=\"3929\" SUFN076=\"262\" SUFN077=\"15\" SUFN078=\"2\" SUFN079=\"3591\" SUFN080=\"145\" SUFN081=\"36\" SUFN082=\"5\"></SUFNsegment><SUILsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"30\" BureauHitLevelID=\"0A1@30\" DedupFlag=\"0\" SUILcat=\"IL\" SUIL001=\"3\" SUIL002=\"3\" SUIL003=\"50\" SUIL004=\"3\" SUIL005=\"0\" SUIL006=\"0\" SUIL007=\"0\" SUIL008=\"0\" SUIL009=\"3\" SUIL011=\"13\" SUIL012=\"0\" SUIL013=\"0\" SUIL014=\"0\" SUIL015=\"0\" SUIL016=\"0\" SUIL017=\"0\" SUIL018=\"0\" SUIL019=\"0\" SUIL020=\"0\" SUIL021=\"0\" SUIL022=\"0\" SUIL023=\"0\" SUIL024=\"0\" SUIL025=\"0\" SUIL026=\"0\" SUIL027=\"0\" SUIL028=\"0\" SUIL029=\"0\" SUIL030=\"0\" SUIL031=\"0\" SUIL032=\"0\" SUIL033=\"0\" SUIL034=\"0\" SUIL035=\"0\" SUIL036=\"0\" SUIL037=\"0\" SUIL038=\"0\" SUIL039=\"570\" SUIL040=\"8640\" SUIL041=\"44020\" SUIL042=\"100\" SUIL043=\"20\" SUIL044=\"100\" SUIL045=\"2\" SUIL046=\"0\" SUIL047=\"0\" SUIL048=\"394\" SUIL049=\"131\" SUIL050=\"6\" SUIL051=\"3\" SUIL052=\"0\" SUIL053=\"0\" SUIL054=\"0\" SUIL055=\"0\" SUIL056=\"0\" SUIL057=\"0\" SUIL058=\"0\" SUIL059=\"0\" SUIL061=\"3327\" SUIL065=\"0\" SUIL066=\"0\" SUIL067=\"0\" SUIL068=\"0\" SUIL070=\"0\" SUIL071=\"0\" SUIL072=\"0\" SUIL073=\"0\" SUIL074=\"36500\" SUIL075=\"3929\" SUIL076=\"262\" SUIL077=\"15\" SUIL078=\"2\" SUIL079=\"3591\" SUIL080=\"145\" SUIL081=\"36\" SUIL082=\"5\"></SUILsegment><SUMCsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"28\" BureauHitLevelID=\"0A1@28\" DedupFlag=\"0\" SUMCcat=\"MC\" SUMC001=\"0\" SUMC002=\"0\" SUMC003=\"0\" SUMC004=\"0\" SUMC005=\"0\" SUMC006=\"0\" SUMC007=\"0\" SUMC008=\"0\" SUMC009=\"0\" SUMC012=\"0\" SUMC013=\"0\" SUMC014=\"0\" SUMC015=\"0\" SUMC016=\"0\" SUMC017=\"0\" SUMC018=\"0\" SUMC019=\"0\" SUMC020=\"0\" SUMC021=\"0\" SUMC022=\"0\" SUMC023=\"0\" SUMC024=\"0\" SUMC025=\"0\" SUMC026=\"0\" SUMC027=\"0\" SUMC028=\"0\" SUMC029=\"0\" SUMC030=\"0\" SUMC031=\"0\" SUMC032=\"0\" SUMC033=\"0\" SUMC034=\"0\" SUMC035=\"0\" SUMC036=\"0\" SUMC037=\"0\" SUMC038=\"0\" SUMC044=\"0\" SUMC045=\"0\" SUMC046=\"0\" SUMC047=\"0\" SUMC050=\"0\" SUMC051=\"0\" SUMC052=\"0\" SUMC053=\"0\" SUMC054=\"0\" SUMC055=\"0\" SUMC056=\"1\" SUMC057=\"1\" SUMC058=\"1\" SUMC059=\"1\" SUMC060=\"2\" SUMC065=\"0\" SUMC066=\"0\" SUMC067=\"0\" SUMC068=\"0\" SUMC070=\"0\" SUMC071=\"0\" SUMC072=\"0\" SUMC073=\"0\"></SUMCsegment><SUREsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"29\" BureauHitLevelID=\"0A1@29\" DedupFlag=\"0\" SUREcat=\"RE\" SURE001=\"1\" SURE002=\"1\" SURE003=\"17\" SURE004=\"1\" SURE005=\"0\" SURE006=\"0\" SURE007=\"0\" SURE008=\"0\" SURE009=\"1\" SURE011=\"350\" SURE012=\"0\" SURE013=\"0\" SURE014=\"0\" SURE015=\"0\" SURE016=\"0\" SURE017=\"0\" SURE018=\"0\" SURE019=\"0\" SURE020=\"0\" SURE021=\"0\" SURE022=\"0\" SURE023=\"0\" SURE024=\"0\" SURE025=\"0\" SURE026=\"0\" SURE027=\"0\" SURE028=\"0\" SURE029=\"0\" SURE030=\"0\" SURE031=\"0\" SURE032=\"0\" SURE033=\"0\" SURE034=\"0\" SURE035=\"0\" SURE036=\"0\" SURE037=\"0\" SURE038=\"0\" SURE039=\"163\" SURE040=\"3327\" SURE041=\"36500\" SURE042=\"100\" SURE043=\"9\" SURE044=\"100\" SURE045=\"0\" SURE046=\"0\" SURE047=\"0\" SURE048=\"350\" SURE049=\"350\" SURE050=\"2\" SURE051=\"1\" SURE052=\"0\" SURE053=\"0\" SURE054=\"0\" SURE055=\"0\" SURE056=\"0\" SURE057=\"0\" SURE058=\"0\" SURE059=\"0\" SURE061=\"3327\" SURE065=\"0\" SURE066=\"0\" SURE067=\"0\" SURE068=\"0\" SURE070=\"0\" SURE071=\"0\" SURE072=\"0\" SURE073=\"0\" SURE074=\"36500\" SURE075=\"36500\" SURE076=\"163\" SURE077=\"300\" SURE078=\"0\"></SUREsegment><SURLsegment SourceSegID=\"DMS\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"31\" BureauHitLevelID=\"0A1@31\" DedupFlag=\"0\" SURLcat=\"RL\" SURL001=\"3\" SURL002=\"3\" SURL003=\"50\" SURL004=\"3\" SURL005=\"0\" SURL006=\"0\" SURL007=\"0\" SURL008=\"0\" SURL009=\"3\" SURL011=\"36\" SURL012=\"0\" SURL013=\"0\" SURL014=\"0\" SURL015=\"0\" SURL016=\"0\" SURL017=\"0\" SURL018=\"0\" SURL019=\"0\" SURL020=\"0\" SURL021=\"0\" SURL022=\"0\" SURL023=\"0\" SURL024=\"0\" SURL025=\"0\" SURL026=\"0\" SURL027=\"0\" SURL028=\"0\" SURL029=\"0\" SURL030=\"0\" SURL031=\"0\" SURL032=\"0\" SURL033=\"0\" SURL034=\"0\" SURL035=\"0\" SURL036=\"0\" SURL037=\"0\" SURL038=\"0\" SURL039=\"89\" SURL040=\"1025\" SURL041=\"6002\" SURL042=\"100\" SURL043=\"17\" SURL044=\"100\" SURL045=\"1\" SURL046=\"0\" SURL047=\"0\" SURL048=\"160\" SURL049=\"53\" SURL050=\"7\" SURL051=\"2\" SURL052=\"1\" SURL053=\"0\" SURL054=\"0\" SURL055=\"0\" SURL056=\"2\" SURL057=\"2\" SURL058=\"2\" SURL059=\"2\" SURL060=\"2\" SURL061=\"666\" SURL065=\"0\" SURL066=\"0\" SURL067=\"0\" SURL068=\"0\" SURL070=\"0\" SURL071=\"0\" SURL072=\"0\" SURL073=\"0\" SURL074=\"2500\"></SURLsegment><SUSCsegment SourceSegID=\"SC01\" BureauName=\"DMS\" BureauNumber=\"0\" AppOrSpouse=\"A\" HitLevel=\"1\" RecordNumber=\"32\" BureauHitLevelID=\"0A1@32\" DedupFlag=\"0\" SUSCcat=\"SC\" SUSC001=\"740\" SUSC002=\"0\" SUSC003=\"0\" SUSC004=\"4\" SUSC005=\"308\" SUSC006=\"409\" SUSC007=\"404\" SUSC008=\"500\"></SUSCsegment></SUsegments><CKsegment nosegments=\"0\" nocharacters=\"0\" checksum=\"1\"></CKsegment></ProductResult></Product></Products></DMSCommon></DMSXML></Response>", "create_date": "2022-06-07T13:46:45.0170000", "update_date": "2022-06-07T13:46:45.0170000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}}, "Addresses": {"Address": [{"address_id": 428, "city": "FANTASY ISLAND", "state_code_id": "IL", "country_code": "USA", "postal_code": 60750, "item_code": "CURRENT", "street_type": "LN", "street_type_description": "LANE", "years_at_address": 14, "months_at_address": 0, "mortgage_rent": 500, "address_type": "CURRENT", "street_number": 525, "street": "COUNTRY", "house_type": "CURRENT", "create_date": "2022-06-07T13:46:35.9900000", "update_date": "2022-06-07T13:51:23.5670000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"address_id": 429, "country_code": "USA", "item_code": "PREVIOUS", "years_at_address": 0, "months_at_address": 0, "mortgage_rent": 500, "address_type": "PREVIOUS", "create_date": "2022-06-07T13:46:35.9930000", "update_date": "2022-06-07T13:51:23.5730000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}]}, "Communications": {"Communication": [{"com_link_id": 1035, "item_code": "HOME", "com": "304-987-4521", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0070000", "update_date": "2022-06-07T13:46:36.0070000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}, {"com_link_id": 1036, "item_code": "WORK", "com": "304-987-4111", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0100000", "update_date": "2022-06-07T13:46:36.0100000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}, {"com_link_id": 1037, "item_code": "PWORK", "contact_type": "CU", "create_date": "2022-06-07T13:46:36.0100000", "update_date": "2022-06-07T13:46:36.0100000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}]}, "Employers": {"Employer": [{"employer_id": 461, "type": "EMPLOYED", "type_code": "CURRENT", "years_employed": 15, "months_employed": 0, "employer_name": "TEST CO", "occupation": "MANAGER", "employment_status": "Employed", "create_date": "2022-06-07T13:46:35.9830000", "update_date": "2022-08-18T09:17:00.9900000", "updated_by": "jmcclintick"}, {"employer_id": 462, "type": "UNEMPLOYED", "type_code": "PREVIOUS", "years_employed": 0, "months_employed": 0, "employment_status": "Unemployed", "create_date": "2022-06-07T13:46:35.9870000", "update_date": "2022-08-18T09:17:00.9900000", "updated_by": "jmcclintick"}]}, "Incomes": {"Income": [{"income_id": 303, "frequency": "MONTHLY", "gross": 12000, "net_source": "MAN", "monthly_income": 12000, "employer_id": 461, "income_type": "PAYCHECK", "create_date": "2022-06-07T13:46:35.9670000", "update_date": "2022-06-07T13:46:35.9770000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}, {"income_id": 304, "frequency": "MONTHLY", "gross": 0, "net_source": "MAN", "monthly_income": 0, "employer_id": 461, "income_type": "PAYCHECK", "create_date": "2022-06-07T13:46:35.9730000", "update_date": "2022-06-07T13:46:36.2730000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}, {"income_id": 305, "frequency": "MONTHLY", "gross": 0, "net_source": "MAN", "monthly_income": 0, "employer_id": 461, "create_date": "2022-06-07T13:51:23.5630000", "update_date": "2022-06-07T13:51:24.1270000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}], "total_primary_income": 12000, "total_secondary_income": 0, "deal_detail_id": 209, "bundle_id": 1082}, "Expenses": {"Expense": [{"entity_type": "PB", "creditor": "HOUSING PAYMENT", "balance": 0, "payment": 500, "expense_id": 1471, "frequency": "MONTHLY", "source": "APP", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "RENT", "item_code": "RENT", "create_date": "2022-06-07T13:46:35.9470000", "update_date": "2022-06-07T13:46:35.9470000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "FNCC", "balance": 666, "open_date": "2015-10-01T00:00:00.0000000", "rating": 1, "payment": 19, "expense_id": 1472, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "CITIFINANCIA", "balance": 2736, "open_date": "2021-05-14T00:00:00.0000000", "rating": 1, "payment": 262, "expense_id": 1473, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "WILM TRUST", "balance": 3327, "open_date": "1993-04-24T00:00:00.0000000", "rating": 1, "payment": 163, "expense_id": 1474, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "AVCO FINANCE", "balance": 2577, "open_date": "2019-11-10T00:00:00.0000000", "rating": 1, "payment": 145, "expense_id": 1475, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "CHASE", "balance": 255, "open_date": "2018-10-31T00:00:00.0000000", "rating": 1, "payment": 67, "expense_id": 1476, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "BK OF AMER", "balance": 104, "open_date": "2019-06-19T00:00:00.0000000", "rating": 1, "payment": 3, "expense_id": 1477, "source": "TU", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "balance": 0, "open_date": "2020-04-09T00:00:00.0000000", "payment": 0, "expense_id": 1478, "source": "TU", "active": "True", "exclude_flag": "True", "joint_flag": "False", "credit_b_flag": "True", "status_code": "BUREAU", "item_code": "BUREAU", "create_date": "2022-06-07T13:46:44.9130000", "update_date": "2022-06-07T13:46:44.9130000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "Insurance", "balance": 0, "payment": 100, "expense_id": 1479, "frequency": "MONTHLY", "source": "SYSTEM", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "INSURANCE", "item_code": "INSURANCE", "create_date": "2022-06-07T13:46:47.0730000", "update_date": "2022-06-07T13:46:47.0730000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"entity_type": "PB", "creditor": "Est. Payment", "balance": 0, "payment": 611.14, "expense_id": 1480, "frequency": "MONTHLY", "source": "SYSTEM", "active": "True", "exclude_flag": "False", "joint_flag": "False", "credit_b_flag": "False", "status_code": "PAYMENT", "item_code": "PAYMENT", "create_date": "2022-06-07T13:46:47.0730000", "update_date": "2022-06-07T13:51:23.4930000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}], "total_debt": 1870}, "customer_id": 231, "social_security_number": 666393933, "type": "PB", "date_of_birth": "1989-05-25T00:00:00.0000000", "first_name": "LINE", "last_name": "DANCER", "income_monthly": 12000, "fico": 740, "total_exp": 0, "is_in_military": "No", "full_name": "LINE DANCER", "age": 34, "create_date": "2022-06-07T13:46:35.0000000", "update_date": "2022-08-18T09:17:01.0000000", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}}, "CustomScorecard": {"deal_detail_id": 209, "bundle_id": 1082}, "Vehicles": {"Vehicle": [{"vehicle_id": 366, "source_system_code": "DT", "trade_in": "True", "trim": "SEDAN 4D LX I4", "vehicle_age": 0, "active": "True", "create_date": "2022-06-07T13:46:35.9300000", "update_date": "2022-06-07T13:51:23.4870000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}, {"Eval": {"collateral_evaluation_id": 344, "collateral_id": 367, "wholesale_value": 38125, "retail_value": 0, "msrp": 0, "used_car_value": 0, "stated_value": 38125, "invoice": 0, "vehicle_value": 38125, "create_date": "2022-06-07T13:46:35.9430000", "update_date": "2022-06-07T13:46:35.9430000", "updated_by": "System [No User Available]", "deal_detail_id": 209, "bundle_id": 1082}, "vehicle_id": 367, "source_system_code": "DT", "type": "Used", "trade_in": "False", "make": "MERCEDES-BENZ", "model": "C-CLASS", "year": 2019, "condition": "Used", "odometer": 39930, "trim": "COUPE 2D C300", "certified_used": "False", "vehicle_age": 4, "active": "True", "create_date": "2022-06-07T13:46:35.9300000", "update_date": "2022-06-07T13:51:23.4930000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}]}, "RetailInstallmentContract": {"RetailInstallmentContractLineItem": [{"id": 215392, "text": "Vehicle Trade Year", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215393, "text": "Vehicle Trade Make", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215394, "text": "Vehicle Trade Model", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215395, "text": "Vehicle Trade VIN", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215396, "text": "License Number", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215403, "value": 72, "text": "Number of Payments", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215404, "value": 611.15, "text": "Amount of Payments", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215405, "value": "06/27/2022", "text": "First Due Date", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215406, "value": "06/27/2028", "text": "Final Due Date", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215360, "value": "05/27/2022", "text": "Contract Date", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216461, "value": 31, "text": "Days to First Payment", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 392, "text": "Buy Rate", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216339, "value": 2.5, "text": "Participation Percentage", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216349, "value": 13.01, "text": "Customer Rate", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215397, "value": 13.01, "text": "Disclosure APR", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 18904, "text": "System CalculatedDiscount", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216412, "value": 195, "text": "Discount Amount", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216469, "value": 1362, "text": "Participation Sixty Split UW", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216468, "value": 1368, "text": "Participation Sixty Split VF", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216400, "text": "Short Fund Amount", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216420, "text": "Short Fund Amount VF", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216404, "value": 195, "text": "Assigment Fee", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216421, "value": 195, "text": "Assigment Fee VF", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216401, "value": 31408, "text": "Disbursement UW", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216413, "value": 31414.27, "text": "Disbursement VF", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 17493, "value": 2019, "text": "Vehicle Year", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 17496, "value": "MERCEDES-BENZ", "text": "Vehicle Make", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 17499, "value": "C-CLASS", "text": "Vehicle Model", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 17672, "text": "Vehicle VIN", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19725, "value": 0, "text": "Vehicle Used Car Value", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19713, "value": "Used", "text": "Vehicle Type", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 17669, "value": 38125, "text": "Vehicle Stated Value", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19709, "value": 39930, "text": "Vehicle Mileage", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215398, "value": 13566.53, "text": "Finance Charge", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215399, "value": 30436.27, "text": "Disclosure Amount Financed", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215400, "value": 44002.8, "text": "Total of Payments", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215401, "value": 8666, "text": "Down Payment", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215402, "value": 52668.8, "text": "Total Sales Price", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215407, "value": 34900, "text": "Sales Price", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215408, "text": "Rebate", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215409, "value": 799, "text": "Service Contracts", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215410, "value": 995, "text": "Gap", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215411, "value": 1000, "text": "Cash Down", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215412, "value": 7666, "text": "Net Trade", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215413, "value": 30436.27, "text": "Unpaid Balance", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215414, "value": 30436.27, "text": "Amount Financed", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215415, "value": 72, "text": "Term", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215416, "value": 13.01, "text": "APR", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 215417, "value": 611.15, "text": "Payment", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216536, "value": 1610.27, "text": "Other 1", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216538, "value": "tax", "text": "Other 1 Description", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216539, "value": "Doc and ELT", "text": "Other 2 Description", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216537, "value": 798, "text": "Other 2", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19471, "text": "APR Compliance", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19478, "text": "Finance Charge Balanced", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19477, "text": "Amount Financed Balanced", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19476, "text": "Total of Payments Balanced", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19474, "text": "Sale Price Balanced", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 19472, "text": "Maximum Charge Compliance", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 216657, "text": "Deferred Down Payment", "deal_detail_id": 209, "bundle_id": 1082}]}, "payment_call_flag": "False", "credit_type": "Individual", "id": 209, "source_system_code": "DT", "received_date": "2022-06-07T13:46:00.0000000", "initial_entry_date": "2022-06-07T13:46:00.0000000", "source_system_dealer_id": 269472, "decision_date": "2022-08-18T09:17:01.0000000", "decision_code": "B", "source_system_id": "BBF3700439", "total_primary_income": 12000, "total_secondary_income": 0, "total_debt": 1870.15, "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:01.7770000", "updated_by": "jmcclintick", "bundle_id": 1082}, "RuleSummary": {"Rule": [{"id": 146, "name": "VF Rule - AA AutoDecline CB Print", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "CoApplicant.IsActive == 1 and Deal.App Status == AD", "evaluatedDefinition": "False == True and BOOKED == AD", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 145, "name": "VF Rule - AA Decline CB Print", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "Deal.App Status == D and CoApplicant.IsActive == 1", "evaluatedDefinition": "BOOKED == D and False == True", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 142, "name": "VF Rule - AA Decline PB Print", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "Deal.App Status == D or Deal.App Status == AD", "evaluatedDefinition": "BOOKED == D or BOOKED == AD", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 166, "name": "VF Rule - Amount Finance Does not Match", "type": "Funding Tolerance Rules", "eval_value": 30436.27, "actual_value": 30436.27, "result": "PASS", "waive": "False", "definition": "Retail Installment Contract.Disclosure Amount Financed != Retail Installment Contract.Amount Financed +0.00", "evaluatedDefinition": "30436.27 != 30436.27 +0.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 161, "name": "VF Rule - Amount Financed Compliant", "type": "Funding Rules", "eval_value": "YES", "actual_value": "YES", "result": "PASS", "waive": "False", "definition": "Funding Compliance Data.Amount Financed Balanced != Yes", "evaluatedDefinition": "YES != YES", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 143, "name": "VF Rule - Approval", "type": "Notification Template Rules", "result": "PASS", "waive": "False", "definition": "Deal.App Status == AA or Deal.App Status == Approved or", "evaluatedDefinition": "BOOKED == AA or BOOKED == APPROVED or", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 162, "name": "VF Rule - APR Compliant", "type": "Funding Rules", "eval_value": "COMPLIANT", "actual_value": "COMPLIANT", "result": "PASS", "waive": "False", "definition": "Funding Compliance Data.APR Compliance != Compliant", "evaluatedDefinition": "COMPLIANT != COMPLIANT", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 167, "name": "VF Rule - Cash Down Out of Tolerance", "type": "Funding Tolerance Rules", "eval_value": 0, "actual_value": 1000, "result": "PASS", "waive": "False", "definition": "Retail Installment Contract.Cash Down < CreditPolicyTypes.Custom Min Down +0.00", "evaluatedDefinition": "1000.00 < 0.000000 +0.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 144, "name": "VF Rule - Conditional Approval", "type": "Notification Template Rules", "eval_value": "CONDITIONAL APPROVAL", "actual_value": "BOOKED", "result": "PASS", "waive": "False", "definition": "Deal.App Status == Conditional Approval", "evaluatedDefinition": "BOOKED == CONDITIONAL APPROVAL", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 138, "name": "VF Rule - Days to First Payment", "type": "Funding Rules", "eval_value": 45, "actual_value": 31, "result": "PASS", "waive": "False", "definition": "Custom Fields.Days to First Payment > 45", "evaluatedDefinition": "31.00 > 45.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 168, "name": "VF Rule - DTI Increased", "type": "Funding Tolerance Rules", "eval_value": 20.58, "actual_value": 15.58, "result": "PASS", "waive": "False", "definition": "Contract.DTI >= Recommended.DTI +5.00", "evaluatedDefinition": "15.58 >= 15.58 +5.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 137, "name": "VF Rule - Employment Verification Completed", "type": "Funding Rules", "eval_value": "NO", "actual_value": "YES", "result": "PASS", "waive": "False", "definition": "Custom Fields.Employment Verification Completed == No", "evaluatedDefinition": "YES == NO", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 163, "name": "VF Rule - Finance Charge Compliant", "type": "Funding Rules", "eval_value": "YES", "actual_value": "YES", "result": "PASS", "waive": "False", "definition": "Funding Compliance Data.Finance Charge Balanced != Yes", "evaluatedDefinition": "YES != YES", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 169, "name": "VF Rule - FLTV Out of Tolerance", "type": "Funding Tolerance Rules", "result": "PASS", "waive": "False", "definition": "Custom Fields.FLTV VF > Recommended.FLTV +2.00% and Custom Fields.FLTV VF < Recommended.FLTV -10.00%", "evaluatedDefinition": "0.00 > 73.29 +2.00% and 0.00 < 73.29 -10.00%", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 170, "name": "VF Rule - LTV Out of Tolerance", "type": "Funding Tolerance Rules", "result": "PASS", "waive": "False", "definition": "Retail Installment Contract.LTV > Recommended.LtvRatio +3.00% and Retail Installment Contract.LTV < Recommended.LtvRatio -10.00%", "evaluatedDefinition": "NA > 79.83 +3.00% and NA < 79.83 -10.00%", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 29, "name": "VF Rule - Must Trade Open Auto", "type": "Stipulations Rules", "eval_value": 1, "actual_value": 0, "result": "PASS", "waive": "False", "definition": "DMS.GP052 Nbr of Open Autos PB >= 1", "evaluatedDefinition": "0.00 >= 1.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 31, "name": "VF Rule - Proof of Income", "type": "Stipulations Rules", "eval_value": 1, "actual_value": 1082, "result": "FAIL", "waive": "False", "definition": "Deal.Application Number > 1", "evaluatedDefinition": "1082.00 > 1.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 30, "name": "VF Rule - Proof of Residence", "type": "Stipulations Rules", "eval_value": 1, "actual_value": 1082, "result": "FAIL", "waive": "False", "definition": "Deal.Application Number > 1", "evaluatedDefinition": "1082.00 > 1.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 175, "name": "VF Rule - PTI Increased", "type": "Funding Tolerance Rules", "eval_value": 5.59, "actual_value": 5.09, "result": "PASS", "waive": "False", "definition": "Contract.PTI > Recommended.PTI +0.50", "evaluatedDefinition": "5.09 > 5.09 +0.50", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 164, "name": "VF Rule - Sale Price Compliant", "type": "Funding Rules", "eval_value": "YES", "actual_value": "YES", "result": "PASS", "waive": "False", "definition": "Funding Compliance Data.Sale Price Balanced != Yes", "evaluatedDefinition": "YES != YES", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 172, "name": "VF Rule - Stated Value Out of Tolerance", "type": "Funding Tolerance Rules", "eval_value": 37625, "actual_value": 38125, "result": "PASS", "waive": "False", "definition": "Contract.Vehicle Stated Value < Recommended.Stated Value -500.00", "evaluatedDefinition": "38125.00 < 38125.00 -500.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 165, "name": "VF Rule - Total Payments Compliant", "type": "Funding Rules", "eval_value": "YES", "actual_value": "YES", "result": "PASS", "waive": "False", "definition": "Funding Compliance Data.Total of Payments Balanced != Yes", "evaluatedDefinition": "YES != YES", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 173, "name": "VF Rule - Vehicle Mileage out of Tolerance", "type": "Funding Tolerance Rules", "result": "PASS", "waive": "False", "definition": "Contract.Vehicle Mileage >= Recommended.Vehicle Mileage +1000.00 and Contract.Vehicle Mileage <= Recommended.Vehicle Mileage -1000.00", "evaluatedDefinition": "39930.00 >= 39930 +1000.00 and 39930.00 <= 39930 -1000.00", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}, {"id": 67, "name": "VF Rule - Welcome Call", "type": "Stipulations Rules", "result": "PASS", "waive": "False", "definition": "Recommended.Tier != 1 and Recommended.Tier != 2 and Recommended.Tier != 3", "evaluatedDefinition": "1 != 1 and 1 != 2 and 1 != 3", "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:02.0362541", "updated_by": "jmcclintick", "deal_detail_id": 209, "bundle_id": 1082}]}}, "Errors": {"Error": {"id": 3967, "message": "CaletonCalcs Funding Compliance Error Description: Negative Payment Computed, Look at the total loan proceeds, rate, and term,  to see if the numbers are valid.", "location": "CaletonCalcs Funding Compliance", "source_system": "System", "create_date": "2022-06-07T13:48:43.1070000", "update_date": "2022-06-07T13:48:43.1070000", "updated_by": "rmescha", "deal_detail_id": 209, "bundle_id": 1082}}, "CustomFields": {"CustomField": [{"group": "Custom Fields", "value": 740, "text": "FICO for Application", "id": 216260}, {"group": "Custom Fields", "value": "Decisioned", "text": "UW Process Status", "id": 216265}, {"group": "Custom Fields", "value": "14 Years 0 Months", "text": "App Time at Residence", "id": 216278}, {"group": "Custom Fields", "value": "0 Years 0 Months", "text": "CoApp Time at Residence", "id": 216279}, {"group": "Custom Fields", "value": "15 Years 0 Months", "text": "App Time on Job", "id": 216280}, {"group": "Custom Fields", "value": "0 Years 0 Months", "text": "CoApp Time on Job", "id": 216281}, {"group": "Custom Fields", "value": "No", "text": "TU OFAC PB", "id": 216284}, {"group": "Custom Fields", "value": "No", "text": "TU OFAC CB", "id": 216285}, {"group": "Tradeline", "text": "ECOA", "id": 216313}, {"group": "Recommended", "value": 2, "text": "Participation Percentage", "id": 216339}, {"group": "Custom Fields", "value": 2.5, "text": "Participation Prepop", "id": 216348}, {"group": "Custom Fields", "value": 13.01, "text": "Customer Rate", "id": 216349}, {"group": "Custom Fields", "value": 14, "text": "Recommended Customer Rate", "id": 216350}, {"group": "Custom Fields", "value": 27943, "text": "Front End Advance UW", "id": 216351}, {"group": "Recommended", "value": 73.29, "text": "FLTV", "id": 216352}, {"group": "Custom Fields", "value": "Pass", "text": "Auth Trades GT 25 Percent", "id": 216393}, {"group": "Custom Fields", "value": 52668.8, "text": "SmartCalcs Total Sales Price", "id": 216394}, {"group": "Custom Fields", "text": "Empty", "id": 216395}, {"group": "Custom Fields", "value": 579.48, "text": "Retail Payment Buy Rate UW", "id": 216396}, {"group": "Custom Fields", "value": 11286.56, "text": "Interest Amount Buy Rate UW", "id": 216397}, {"group": "Custom Fields", "value": 13566.08, "text": "Interest Amount Customer Rate UW", "id": 216398}, {"group": "Custom Fields", "value": 2270, "text": "Participation Amount UW", "id": 216399}, {"group": "Custom Fields", "value": 0, "text": "Short Fund Amount", "id": 216400}, {"group": "Custom Fields", "value": 31408, "text": "Disbursement UW", "id": 216401}, {"group": "Custom Fields", "text": "Front End Advance VF", "id": 216402}, {"group": "Custom Fields", "value": 0, "text": "FLTV VF", "id": 216403}, {"group": "Custom Fields", "value": 195, "text": "Assigment Fee", "id": 216404}, {"group": "Custom Fields", "value": 11286.29, "text": "Interest Buy Rate VF", "id": 216409}, {"group": "Custom Fields", "value": 579.48, "text": "Retail Payment Buy Rate VF", "id": 216410}, {"group": "Custom Fields", "value": 2280, "text": "Participation Amount VF", "id": 216411}, {"group": "Custom Fields", "value": 195, "text": "Discount Amount", "id": 216412}, {"group": "Custom Fields", "value": 31414.27, "text": "Disbursement VF", "id": 216413}, {"group": "Custom Fields", "value": "Phone", "text": "Type of Verification", "id": 216418}, {"group": "Custom Fields", "text": "Likelihood of Continues Employment", "id": 216419}, {"group": "Custom Fields", "text": "Short Fund Amount VF", "id": 216420}, {"group": "Custom Fields", "value": 195, "text": "Assigment Fee VF", "id": 216421}, {"group": "Custom Fields", "text": "Employment Start Date", "id": 216422}, {"group": "Custom Fields", "value": 12000, "text": "Employment Income", "id": 216423}, {"group": "Custom Fields", "text": "Employment Bonus Tips Com", "id": 216424}, {"group": "Custom Fields", "value": "No Fraud", "text": "Extended <PERSON><PERSON>", "id": 216425}, {"group": "Custom Fields", "value": "<PERSON><PERSON> Present Must Be Cleared", "text": "<PERSON><PERSON>", "id": 216426}, {"group": "Custom Fields", "value": "08/18/2022", "text": "Booked Date for Queue", "id": 216427}, {"group": "Custom Fields", "value": "Yes", "text": "Employment Verification Completed", "id": 216460}, {"group": "Custom Fields", "value": 31, "text": "Days to First Payment", "id": 216461}, {"group": "Custom Fields", "value": 0, "text": "Odd Days RIC", "id": 216462}, {"group": "Custom Fields", "value": "05/25/1989", "text": "SSI Applicant DOB", "id": 216463}, {"group": "Custom Fields", "text": "SSI CoApplicant DOB", "id": 216464}, {"group": "Custom Fields", "value": "05/27/2022", "text": "SSI Contract Date", "id": 216465}, {"group": "Custom Fields", "value": "FALSE", "text": "SSI Applicant SelfEmployed", "id": 216466}, {"group": "Custom Fields", "text": "SSI CoApplicant SelfEmployed", "id": 216467}, {"group": "Custom Fields", "value": 1368, "text": "Participation Sixty Split VF", "id": 216468}, {"group": "Custom Fields", "value": 1362, "text": "Participation Sixty Split UW", "id": 216469}, {"group": "Custom Fields", "text": "SSI CoApplicant CIF Number", "id": 216470}, {"group": "Custom Fields", "value": "1082-SA", "text": "SSI Applicant CIF Number", "id": 216471}, {"group": "Custom Fields", "value": "1082-SA", "text": "SSI Applicant Loan Number", "id": 216472}, {"group": "Custom Fields", "text": "SSI CIFPortfolioName CB", "id": 216473}, {"group": "Custom Fields", "text": "SSI Entity CB", "id": 216474}, {"group": "Custom Fields", "text": "SSI Phone Desc CB", "id": 216475}, {"group": "Custom Fields", "text": "SSI Operation CB", "id": 216476}, {"group": "Custom Fields", "text": "SSI RelationshipCode CB", "id": 216477}, {"group": "Custom Fields", "text": "SSI NumOfYearsOnJob CB", "id": 216478}, {"group": "Custom Fields", "text": "SSI Direct Deposit CB", "id": 216479}, {"group": "Custom Fields", "text": "SSI PrimaryEmployer CB", "id": 216480}, {"group": "Custom Fields", "text": "SSI PhoneDesc Employer CB", "id": 216481}, {"group": "Custom Fields", "text": "SSI PhoneDesc Mobile CB", "id": 216482}, {"group": "Custom Fields", "text": "SSI PhoneNumberDoNotParse CB", "id": 216483}, {"group": "Custom Fields", "text": "SSI MobileNumberFlag CB", "id": 216484}, {"group": "Custom Fields", "text": "SSI RelationshipCode CoBorrower  CB", "id": 216485}, {"group": "Bureau", "value": "[{\"Bureau\": \"TransUnion\"\"Address1\":\"2 Baldwin Place\"\"Address2\":\"P.O. Box 10000\"\"CityStateZip\":\"Chester PA 19022\"\"Phone\":\"************\"}]", "text": "Active Bureau Contact Info JSON", "id": 216486}, {"group": "Bureau", "value": "TransUnion", "text": "Active Bureau Name", "id": 216487}, {"group": "Bureau", "value": "2 Baldwin Place", "text": "Active Bureau Address1", "id": 216488}, {"group": "Bureau", "text": "Active Bureau Address1 CB", "id": 216489}, {"group": "Bureau", "value": "P.O. Box 10000", "text": "Active Bureau Address2", "id": 216490}, {"group": "Bureau", "text": "Active Bureau Address2 CB", "id": 216491}, {"group": "Bureau", "value": "Chester PA 19022", "text": "Active Bureau CityStateZip", "id": 216492}, {"group": "Bureau", "text": "Active Bureau CityStateZip CB", "id": 216493}, {"group": "Bureau", "value": "************", "text": "Active Bureau Phone Number", "id": 216494}, {"group": "Bureau", "text": "Active Bureau Phone Number CB", "id": 216495}, {"group": "Bureau", "value": "[&#xD;&#xA;  undefined&#xD;&#xA;]", "text": "Active Bureau Contact Info JSON CB", "id": 216496}, {"group": "Bureau", "text": "Active Bureau Name CB", "id": 216497}, {"group": "Custom Fields", "value": "<br>TransUnion <br>2 Baldwin Place <br>P.O. Box 10000 <br>Chester PA 19022 <br>************", "text": "Concatenate PB Bureau Date for DO", "id": 216498}, {"group": "Custom Fields", "text": "Concatenate CB Bureau Date for DO", "id": 216499}, {"group": "Custom Fields", "text": "Adverse Action Reasons List PB", "id": 216500}, {"group": "Custom Fields", "text": "Adverse Action Reasons List CB", "id": 216501}, {"group": "Custom Fields", "text": "SSI CoApplicant CIF-Relationship", "id": 216508}, {"group": "Custom Fields", "value": "RICH MESCHA", "text": "Dealer User", "id": 216509}, {"group": "Custom Fields", "value": 0, "text": "Open BK Count", "id": 216515}, {"group": "Custom Fields", "value": 25870.6, "text": "Adjusted Requested Amount Financed", "id": 216528}, {"group": "Custom Fields", "value": "06/27/2022", "text": "SSI First Principal and Interest Payment Date", "id": 216535}, {"group": "Custom Fields", "value": 1610.27, "text": "Other 1", "id": 216536}, {"group": "Custom Fields", "value": 798, "text": "Other 2", "id": 216537}, {"group": "Custom Fields", "value": "tax", "text": "Other 1 Description", "id": 216538}, {"group": "Custom Fields", "value": "Doc and ELT", "text": "Other 2 Description", "id": 216539}, {"group": "Custom Fields", "value": 390, "text": "Fee + Assignment Fee", "id": 216540}, {"group": "Custom Fields", "value": "[&#xD;&#xA;  {&#xD;&#xA;    \"Fico\": \"999\"&#xD;&#xA;    \"LTV\": \"0.8\"&#xD;&#xA;    \"PTI\": \"0.0999\"&#xD;&#xA;    \"Base\": \"0.1112\"&#xD;&#xA;    \"LTVAdder\": \"-0.5\"&#xD;&#xA;    \"PTIAdder\": \"0\"&#xD;&#xA;    \"DefiVersionId\": \"[\\\"all\\\"\\\"v3\\\"]\"&#xD;&#xA;  }&#xD;&#xA;]", "text": "LUT Rate Chart JSON", "id": 216541}, {"group": "Custom Fields", "value": 11.12, "text": "Base Rate", "id": 216542}, {"group": "Custom Fields", "value": -0.5, "text": "LTVAdder", "id": 216543}, {"group": "Custom Fields", "value": 0, "text": "PTIAdder", "id": 216544}, {"group": "Custom Fields", "value": 10.62, "text": "Suggested Rate", "id": 216545}, {"group": "Custom Fields", "value": "06/27/2028", "text": "SSI Maturity Date", "id": 216549}, {"group": "Custom Fields", "value": 4444444444, "text": "SSI Dealer Phone", "id": 216550}, {"group": "Custom Fields", "value": 740, "text": "FICO for Printvendor PB", "id": 216552}, {"group": "Custom Fields", "text": "FICO for Printvendor CB", "id": 216553}, {"group": "Custom Fields", "value": 250, "text": "TWN Income Tolerance", "id": 216554}, {"group": "Custom Fields", "value": 0, "text": "Applicant Waive POI", "id": 216555}, {"group": "Custom Fields", "value": 0, "text": "CoApplicant Waive POI", "id": 216556}, {"group": "Custom Fields", "value": 0, "text": "ITIN Check", "id": 216557}, {"group": "Custom Fields", "value": "1989/05/25", "text": "Applicant DOB YYYYMMDD", "id": 216571}, {"group": "Custom Fields", "value": "NaN/NaN/NaN", "text": "CoApplicant DOB YYYYMMDD", "id": 216572}, {"group": "Custom Fields", "value": 0, "text": "SSI Boarding File Version 2", "id": 216573}, {"group": "Custom Fields", "value": 1, "text": "SSI Boarding File Version 1 Sunset", "id": 216574}, {"group": "Custom Fields", "value": 1, "text": "TWN Applicant Employer Match", "id": 216575}, {"group": "Custom Fields", "value": 1, "text": "TWN CoApplicant Employer Match", "id": 216576}, {"group": "Custom Fields", "value": "6/27/2028 5:00:00 AM", "text": "Calculated Maturity Date", "id": 216577}, {"group": "Custom Fields", "value": 1, "text": "Maturity Date Check", "id": 216578}, {"group": "Custom Fields", "value": "12345-SA", "text": "SSI Dealer ID", "id": 216579}, {"group": "Custom Fields", "value": "2022-08-18T14:17:01.000Z", "text": "Informed App Received Date", "id": 216584}, {"group": "Custom Fields", "value": "1989-05-25", "text": "Informed Applicant DOB", "id": 216585}, {"group": "Custom Fields", "value": "NaN-NaN-NaN", "text": "Informed CoApplicant DOB", "id": 216586}, {"group": "Custom Fields", "text": "Deferred Down Payment", "id": 216657}, {"group": "Custom Fields", "value": 44002.8, "text": "SmartCalcs Total Of Payments", "id": 216661}]}, "deal_type": "CONTRACT", "lender_id": "SA5", "request_date": "2022-06-07T13:46:00.0000000", "payment_call": "False", "source_system_id": "BBF3700439", "source_system": "DT", "version": 1, "deal_detail_id": 209, "source_dealer_id": 269472, "app_status": "B", "perform_dupe_check": 1, "create_date": "2022-06-07T13:48:42.6630000", "update_date": "2022-08-18T09:17:01.7770000", "updated_by": "jmcclintick", "decisioned_by": "jmcclintick", "bundle_id": 1082, "status_last_changed_on": "2022-08-18T09:17:01.4000000"}], "id": 1082, "bundle_id": 1082, "version": 1}}