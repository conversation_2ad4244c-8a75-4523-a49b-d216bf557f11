import { renderHook, act } from '@testing-library/react';
import useDictionaryStore from '../dictionaryStore';
import { api } from '@/helpers/web';

// Mock the API
jest.mock('@/helpers/web', () => ({
  api: {
    get: jest.fn(),
  },
}));

const mockApi = api as jest.Mocked<typeof api>;

// Mock localStorage for consistent testing
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('Dictionary Store Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.clear();
    
    // Reset store state
    act(() => {
      useDictionaryStore.setState({
        targetModels: null,
        sourceModels: null,
        targetSearchTerm: '',
      });
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Real-world cache scenarios', () => {
    const targetModels = [
      { id: '1', name: 'users', type: 'core' },
      { id: '2', name: 'orders', type: 'consumption' },
    ];

    const sourceModels = [
      { id: '1', name: 'raw_users', origin: 'postgres' },
      { id: '2', name: 'raw_orders', origin: 'postgres' },
    ];

    it('should handle complete app initialization flow', async () => {
      // Arrange - Fresh app load, no cache
      mockApi.get
        .mockResolvedValueOnce(targetModels) // First call for target models
        .mockResolvedValueOnce(sourceModels); // Second call for source models

      // Act - Simulate app initialization
      const { result } = renderHook(() => useDictionaryStore());
      
      await act(async () => {
        await Promise.all([
          result.current.fetchTargetModels({
            customerKey: 'test-customer',
            customerId: 'test-customer-id',
          }),
          result.current.fetchSourceModels({
            customerId: 'test-customer-id',
          }),
        ]);
      });

      // Assert
      expect(mockApi.get).toHaveBeenCalledTimes(2);
      expect(result.current.targetModels).toEqual(targetModels);
      expect(result.current.sourceModels).toEqual(sourceModels);
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(2);
    });

    it('should use cache on subsequent app loads', async () => {
      // Arrange - Simulate cached data from previous session
      const now = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(now);

      const targetCacheData = {
        data: targetModels,
        timestamp: now,
        ttl: 4 * 60 * 60 * 1000, // 4 hours
      };

      const sourceCacheData = {
        data: sourceModels,
        timestamp: now,
        ttl: 4 * 60 * 60 * 1000, // 4 hours
      };

      mockLocalStorage.setItem('dictionary_target_models_test-customer-id', JSON.stringify(targetCacheData));
      mockLocalStorage.setItem('dictionary_source_models_test-customer-id', JSON.stringify(sourceCacheData));

      // Act - Simulate app load with cache available
      const { result } = renderHook(() => useDictionaryStore());
      
      await act(async () => {
        await Promise.all([
          result.current.fetchTargetModels({
            customerKey: 'test-customer',
            customerId: 'test-customer-id',
          }),
          result.current.fetchSourceModels({
            customerId: 'test-customer-id',
          }),
        ]);
      });

      // Assert
      expect(mockApi.get).not.toHaveBeenCalled(); // Should not call API
      expect(result.current.targetModels).toEqual(targetModels);
      expect(result.current.sourceModels).toEqual(sourceModels);
    });

    it('should handle hard refresh scenarios correctly', async () => {
      // Arrange - Start with cached data
      const now = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(now);

      const cachedTargetModels = [{ id: 'cached', name: 'cached-target', type: 'core' }];
      const freshTargetModels = [{ id: 'fresh', name: 'fresh-target', type: 'core' }];

      const targetCacheData = {
        data: cachedTargetModels,
        timestamp: now,
        ttl: 4 * 60 * 60 * 1000,
      };

      mockLocalStorage.setItem('dictionary_target_models_test-customer-id', JSON.stringify(targetCacheData));
      mockApi.get.mockResolvedValue(freshTargetModels);

      // Act - First load (should use cache)
      const { result } = renderHook(() => useDictionaryStore());
      
      await act(async () => {
        await result.current.fetchTargetModels({
          customerKey: 'test-customer',
          customerId: 'test-customer-id',
        });
      });

      expect(result.current.targetModels).toEqual(cachedTargetModels);
      expect(mockApi.get).not.toHaveBeenCalled();

      // Act - Hard refresh (should bypass cache)
      await act(async () => {
        await result.current.fetchTargetModels({
          customerKey: 'test-customer',
          customerId: 'test-customer-id',
          forceRefresh: true,
        });
      });

      // Assert
      expect(mockApi.get).toHaveBeenCalledTimes(1);
      expect(result.current.targetModels).toEqual(freshTargetModels);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'dictionary_target_models_test-customer-id',
        expect.stringContaining('"data":' + JSON.stringify(freshTargetModels))
      );
    });

    it('should handle cache expiration gracefully', async () => {
      // Arrange - Set up expired cache
      const now = Date.now();
      const pastTime = now - 5 * 60 * 60 * 1000; // 5 hours ago
      jest.spyOn(Date, 'now').mockReturnValue(now);

      const expiredCacheData = {
        data: [{ id: 'expired', name: 'expired-model', type: 'core' }],
        timestamp: pastTime,
        ttl: 4 * 60 * 60 * 1000, // 4 hours TTL
      };

      mockLocalStorage.setItem('dictionary_target_models_test-customer-id', JSON.stringify(expiredCacheData));
      mockApi.get.mockResolvedValue(targetModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      
      await act(async () => {
        await result.current.fetchTargetModels({
          customerKey: 'test-customer',
          customerId: 'test-customer-id',
        });
      });

      // Assert
      expect(mockApi.get).toHaveBeenCalledTimes(1); // Should call API due to expired cache
      expect(result.current.targetModels).toEqual(targetModels);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('dictionary_target_models_test-customer-id');
    });

    it('should handle mixed cache states (some cached, some not)', async () => {
      // Arrange - Only target models cached, source models not cached
      const now = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(now);

      const targetCacheData = {
        data: targetModels,
        timestamp: now,
        ttl: 4 * 60 * 60 * 1000,
      };

      mockLocalStorage.setItem('dictionary_target_models_test-customer-id', JSON.stringify(targetCacheData));
      mockApi.get.mockResolvedValue(sourceModels); // Only source models will be fetched

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      
      await act(async () => {
        await Promise.all([
          result.current.fetchTargetModels({
            customerKey: 'test-customer',
            customerId: 'test-customer-id',
          }),
          result.current.fetchSourceModels({
            customerId: 'test-customer-id',
          }),
        ]);
      });

      // Assert
      expect(mockApi.get).toHaveBeenCalledTimes(1); // Only source models API call
      expect(mockApi.get).toHaveBeenCalledWith('/api/dataDictionary/getSourceDataDictionaries', {
        customerId: 'test-customer-id',
      });
      expect(result.current.targetModels).toEqual(targetModels); // From cache
      expect(result.current.sourceModels).toEqual(sourceModels); // From API
    });

    it('should handle localStorage errors gracefully', async () => {
      // Arrange
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      mockApi.get.mockResolvedValue(targetModels);

      // Act & Assert - Should not throw and should fallback to API
      const { result } = renderHook(() => useDictionaryStore());
      
      await act(async () => {
        await result.current.fetchTargetModels({
          customerKey: 'test-customer',
          customerId: 'test-customer-id',
        });
      });

      expect(mockApi.get).toHaveBeenCalledTimes(1);
      expect(result.current.targetModels).toEqual(targetModels);
    });
  });
});
