import { renderHook, act } from '@testing-library/react';
import useDictionaryStore from '../dictionaryStore';
import { api } from '@/helpers/web';
import { cache } from '@/utils/cache';

// Mock the API
jest.mock('@/helpers/web', () => ({
  api: {
    get: jest.fn(),
  },
}));

// Mock the cache utility
jest.mock('@/utils/cache', () => ({
  cache: {
    get: jest.fn(),
    set: jest.fn(),
  },
  CACHE_KEYS: {
    DICTIONARY_TARGET_MODELS: 'dictionary_target_models',
    DICTIONARY_SOURCE_MODELS: 'dictionary_source_models',
  },
}));

const mockApi = api as jest.Mocked<typeof api>;
const mockCache = cache as jest.Mocked<typeof cache>;

describe('dictionaryStore cache behavior', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Reset the store state directly (no need for renderHook here)
    useDictionaryStore.setState({
      targetModels: null,
      sourceModels: null,
      targetSearchTerm: '',
    });
  });

  describe('fetchTargetModels', () => {
    const mockTargetModels = [
      { id: '1', name: 'model1', type: 'core' },
      { id: '2', name: 'model2', type: 'consumption' },
    ];

    const fetchProps = {
      customerKey: 'test-customer',
      customerId: 'test-customer-id',
    };

    it('should fetch from API when cache is empty', async () => {
      // Arrange
      mockCache.get.mockReturnValue(null); // No cached data
      mockApi.get.mockResolvedValue(mockTargetModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await act(() => result.current.fetchTargetModels(fetchProps));

      // Assert
      expect(mockCache.get).toHaveBeenCalledWith('dictionary_target_models_test-customer-id');
      expect(mockApi.get).toHaveBeenCalledWith('/api/dataDictionary/getTargetDataDictionaries', {
        customerKey: 'test-customer',
        customerId: 'test-customer-id',
      });
      expect(mockCache.set).toHaveBeenCalledWith(
        'dictionary_target_models_test-customer-id',
        mockTargetModels,
      );
      expect(result.current.targetModels).toEqual(mockTargetModels);
    });

    it('should use cached data when available and forceRefresh is false', async () => {
      // Arrange
      const cachedModels = [{ id: 'cached', name: 'cached-model', type: 'core' }];
      mockCache.get.mockReturnValue(cachedModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await act(() => result.current.fetchTargetModels(fetchProps));

      // Assert
      expect(mockCache.get).toHaveBeenCalledWith('dictionary_target_models_test-customer-id');
      expect(mockApi.get).not.toHaveBeenCalled(); // Should not call API
      expect(mockCache.set).not.toHaveBeenCalled(); // Should not update cache
      expect(result.current.targetModels).toEqual(cachedModels);
    });

    it('should bypass cache when forceRefresh is true', async () => {
      // Arrange
      const cachedModels = [{ id: 'cached', name: 'cached-model', type: 'core' }];
      mockCache.get.mockReturnValue(cachedModels);
      mockApi.get.mockResolvedValue(mockTargetModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await act(() => result.current.fetchTargetModels({ ...fetchProps, forceRefresh: true }));

      // Assert
      expect(mockCache.get).not.toHaveBeenCalled(); // Should skip cache check
      expect(mockApi.get).toHaveBeenCalledWith('/api/dataDictionary/getTargetDataDictionaries', {
        customerKey: 'test-customer',
        customerId: 'test-customer-id',
      });
      expect(mockCache.set).toHaveBeenCalledWith(
        'dictionary_target_models_test-customer-id',
        mockTargetModels,
      );
      expect(result.current.targetModels).toEqual(mockTargetModels);
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      mockCache.get.mockReturnValue(null);
      const apiError = new Error('API Error');
      mockApi.get.mockRejectedValue(apiError);

      // Act & Assert
      const { result } = renderHook(() => useDictionaryStore());
      await expect(result.current.fetchTargetModels(fetchProps)).rejects.toThrow('API Error');

      expect(mockCache.set).not.toHaveBeenCalled(); // Should not cache failed response
      expect(result.current.targetModels).toBeNull(); // Should not update store
    });
  });

  describe('fetchSourceModels', () => {
    const mockSourceModels = [
      { id: '1', name: 'source1', origin: 'test-origin' },
      { id: '2', name: 'source2', origin: 'test-origin' },
    ];

    const fetchProps = {
      customerId: 'test-customer-id',
    };

    it('should fetch from API when cache is empty', async () => {
      // Arrange
      mockCache.get.mockReturnValue(null);
      mockApi.get.mockResolvedValue(mockSourceModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await act(() => result.current.fetchSourceModels(fetchProps));

      // Assert
      expect(mockCache.get).toHaveBeenCalledWith('dictionary_source_models_test-customer-id');
      expect(mockApi.get).toHaveBeenCalledWith('/api/dataDictionary/getSourceDataDictionaries', {
        customerId: 'test-customer-id',
      });
      expect(mockCache.set).toHaveBeenCalledWith(
        'dictionary_source_models_test-customer-id',
        mockSourceModels,
      );
      expect(result.current.sourceModels).toEqual(mockSourceModels);
    });

    it('should use cached data when available and forceRefresh is false', async () => {
      // Arrange
      const cachedModels = [{ id: 'cached', name: 'cached-source', origin: 'cached-origin' }];
      mockCache.get.mockReturnValue(cachedModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await act(() => result.current.fetchSourceModels(fetchProps));

      // Assert
      expect(mockCache.get).toHaveBeenCalledWith('dictionary_source_models_test-customer-id');
      expect(mockApi.get).not.toHaveBeenCalled();
      expect(result.current.sourceModels).toEqual(cachedModels);
    });

    it('should bypass cache when forceRefresh is true', async () => {
      // Arrange
      const cachedModels = [{ id: 'cached', name: 'cached-source', origin: 'cached-origin' }];
      mockCache.get.mockReturnValue(cachedModels);
      mockApi.get.mockResolvedValue(mockSourceModels);

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await act(() => result.current.fetchSourceModels({ ...fetchProps, forceRefresh: true }));

      // Assert
      expect(mockCache.get).not.toHaveBeenCalled();
      expect(mockApi.get).toHaveBeenCalled();
      expect(mockCache.set).toHaveBeenCalledWith(
        'dictionary_source_models_test-customer-id',
        mockSourceModels,
      );
      expect(result.current.sourceModels).toEqual(mockSourceModels);
    });
  });

  describe('cache independence', () => {
    it('should manage target and source model caches independently', async () => {
      // Arrange
      const targetModels = [{ id: '1', name: 'target1', type: 'core' }];
      const sourceModels = [{ id: '1', name: 'source1', origin: 'test' }];

      mockCache.get.mockReturnValue(null);
      mockApi.get
        .mockResolvedValueOnce(targetModels) // First call for target models
        .mockResolvedValueOnce(sourceModels); // Second call for source models

      // Act
      const { result } = renderHook(() => useDictionaryStore());
      await Promise.all([
        result.current.fetchTargetModels({
          customerKey: 'test-customer',
          customerId: 'test-customer-id',
        }),
        result.current.fetchSourceModels({
          customerId: 'test-customer-id',
        }),
      ]);

      // Assert - Different cache keys used
      expect(mockCache.set).toHaveBeenCalledWith(
        'dictionary_target_models_test-customer-id',
        targetModels,
      );
      expect(mockCache.set).toHaveBeenCalledWith(
        'dictionary_source_models_test-customer-id',
        sourceModels,
      );
      expect(mockCache.set).toHaveBeenCalledTimes(2);
    });
  });
});
