'use client';
import { create } from 'zustand';
import { Edge } from '@xyflow/react';
import { GeneralNode, ModelJoin } from '@/types';
import { cloneDeep, forEach } from 'lodash';
import { updateArrayItem } from '@/utils/data';

type State = {
  nodes: GeneralNode[];
  edges: Edge[];
  openNodeId: string | null;
  transformModalOpen: boolean;
  joinModalOpen: boolean;
  modelJoins: ModelJoin[];
};

type Actions = {
  addNodes: (newNodes: GeneralNode[]) => void;
  editNodeValue: (nodeId: string | null, vals: Record<string, any>) => void;
  deleteNode: (nodeId: string) => void;
  addEdges: (newEdges: Edge[]) => void;
  setOpenNodeId: (id: string) => void;
  setTransformModalOpen: (val: boolean) => void;
  setJoinModalOpen: (val: boolean) => void;
  setModelJoins: (joins: ModelJoin[]) => void;
  addModelJoin: (join: ModelJoin) => void;
  deleteModelJoin: (joinId: string) => void;
  editModelJoin: (joinIdx: number, vals: ModelJoin) => void;
  setState: (partial: Partial<State> | ((state: State) => Partial<State>)) => void;
  reset: () => void;
};

const initialState: State = {
  nodes: [],
  edges: [],
  openNodeId: null,
  transformModalOpen: false,
  joinModalOpen: false,
  modelJoins: [],
};

const useMappingStore = create<State & Actions>((set) => ({
  ...initialState,
  addNodes: (newNodes: GeneralNode[]) => {
    set(({ nodes }) => ({ nodes: [...nodes, ...newNodes] }));
  },
  editNodeValue: (nodeId, vals) => {
    if (!nodeId) {
      return;
    }

    set(({ nodes }) => {
      const nodesClone = cloneDeep(nodes);

      const currentNode = nodesClone.find((node) => node.id === nodeId);

      if (!currentNode) {
        return { nodes };
      }

      forEach(vals, (val, key) => {
        currentNode.data.value[key] = val;
      });

      return { nodes: nodesClone };
    });
  },
  deleteNode: (nodeId) => {
    set(({ nodes }) => ({ nodes: nodes.filter((n) => n.id !== nodeId) }));
  },
  addEdges: (newEdges: Edge[]) => {
    set(({ edges }) => ({ edges: [...edges, ...newEdges] }));
  },
  setOpenNodeId: (openNodeId) => {
    set({ openNodeId });
  },
  setTransformModalOpen: (transformModalOpen) => {
    set({ transformModalOpen });
  },
  setJoinModalOpen: (joinModalOpen) => {
    set({ joinModalOpen });
  },
  setModelJoins: (modelJoins) => {
    set({ modelJoins });
  },
  addModelJoin: (modelJoin) => {
    set(({ modelJoins }) => ({ modelJoins: [...modelJoins, modelJoin] }));
  },
  deleteModelJoin: (joinId) => {
    set(({ modelJoins }) => ({
      modelJoins: modelJoins.filter((join) => join.id !== joinId),
    }));
  },
  editModelJoin: (joinIdx, vals) => {
    set(({ modelJoins }) => {
      const updated = updateArrayItem<ModelJoin>({
        array: modelJoins,
        itemIdx: joinIdx,
        newVals: vals,
      });

      return {
        modelJoins: updated,
      };
    });
  },
  setState: set,
  reset: () => set(initialState),
}));

export default useMappingStore;
