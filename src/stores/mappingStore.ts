'use client';
import { create } from 'zustand';
import { Edge } from '@xyflow/react';
import { GeneralNode, ModelJoin } from '@/types';
import { cloneDeep, forEach } from 'lodash';
import { updateArrayItem } from '@/utils/data';
import { getLayoutedElements } from '@/utils/mapping';

type State = {
  nodes: GeneralNode[];
  edges: Edge[];
  tempNodes: GeneralNode[];
  tempEdges: Edge[];
  openNodeId: string | null;
  transformModalOpen: boolean;
  joinModalOpen: boolean;
  modelJoins: ModelJoin[];
};

type Actions = {
  addNodes: (newNodes: GeneralNode[]) => void;
  // Temp nodes are used to store nodes/edges that are in the process of being added to the canvas, but shouldn't be committed yet.
  // On save, they can be committed with a single commit action. This allows us to break up the work of creating nodes and edges into modular pieces
  // without having to write to the actual canvas.
  addTempNodes: (newNodes: GeneralNode[]) => void;
  editNodeValue: (nodeId: string | null, vals: Record<string, any>) => void;
  deleteNode: (nodeId: string) => void;
  addEdges: (newEdges: Edge[]) => void;
  deleteEdge: (edgeId: string) => void;
  addTempEdges: (newEdges: Edge[]) => void;
  setOpenNodeId: (id: string) => void;
  setTransformModalOpen: (val: boolean) => void;
  setJoinModalOpen: (val: boolean) => void;
  setModelJoins: (joins: ModelJoin[]) => void;
  addModelJoin: (join: ModelJoin) => void;
  deleteModelJoin: (joinId: string) => void;
  editModelJoin: (joinIdx: number, vals: ModelJoin) => void;
  setState: (partial: Partial<State> | ((state: State) => Partial<State>)) => void;
  commitTemp: () => void;
  clearTemp: () => void;
  reset: () => void;
  layoutCanvas: (direction?: string) => void;
};

const initialState: State = {
  nodes: [],
  edges: [],
  tempNodes: [],
  tempEdges: [],
  openNodeId: null,
  transformModalOpen: false,
  joinModalOpen: false,
  modelJoins: [],
};

const useMappingStore = create<State & Actions>((set, get) => ({
  ...initialState,
  addNodes: (newNodes) => {
    set(({ nodes }) => ({ nodes: [...nodes, ...newNodes] }));
  },
  addTempNodes: (newNodes) => {
    set(({ tempNodes }) => ({ tempNodes: [...tempNodes, ...newNodes] }));
  },
  editNodeValue: (nodeId, vals) => {
    if (!nodeId) {
      return;
    }

    set(({ nodes }) => {
      const nodesClone = cloneDeep(nodes);

      const currentNode = nodesClone.find((node) => node.id === nodeId);

      if (!currentNode) {
        return { nodes };
      }

      forEach(vals, (val, key) => {
        currentNode.data.value[key] = val;
      });

      return { nodes: nodesClone };
    });
  },
  deleteNode: (nodeId) => {
    set(({ nodes }) => ({ nodes: nodes.filter((n) => n.id !== nodeId) }));
  },
  addEdges: (newEdges) => {
    set(({ edges }) => ({ edges: [...edges, ...newEdges] }));
  },
  deleteEdge: (edgeId) => {
    set(({ edges }) => ({ edges: edges.filter((e) => e.id !== edgeId) }));
  },
  addTempEdges: (newEdges) => {
    set(({ tempEdges }) => ({ tempEdges: [...tempEdges, ...newEdges] }));
  },
  setOpenNodeId: (openNodeId) => {
    set({ openNodeId });
  },
  setTransformModalOpen: (transformModalOpen) => {
    set({ transformModalOpen });
  },
  setJoinModalOpen: (joinModalOpen) => {
    set({ joinModalOpen });
  },
  setModelJoins: (modelJoins) => {
    set({ modelJoins });
  },
  addModelJoin: (modelJoin) => {
    set(({ modelJoins }) => ({ modelJoins: [...modelJoins, modelJoin] }));
  },
  deleteModelJoin: (joinId) => {
    set(({ modelJoins }) => ({
      modelJoins: modelJoins.filter((join) => join.id !== joinId),
    }));
  },
  editModelJoin: (joinIdx, vals) => {
    set(({ modelJoins }) => {
      const updated = updateArrayItem<ModelJoin>({
        array: modelJoins,
        itemIdx: joinIdx,
        newVals: vals,
      });

      return {
        modelJoins: updated,
      };
    });
  },
  commitTemp: () => {
    const { tempNodes, tempEdges, addNodes, addEdges, clearTemp } = get();

    addNodes(tempNodes);
    addEdges(tempEdges);
    clearTemp();
  },
  clearTemp: () => {
    set({ tempNodes: [], tempEdges: [] });
  },
  setState: set,
  layoutCanvas: (direction = 'LR') => {
    set(({ nodes, edges }) => {
      const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(nodes, edges, {
        direction,
      });

      return { nodes: layoutedNodes, edges: layoutedEdges };
    });
  },
  reset: () => set(initialState),
}));

export default useMappingStore;
