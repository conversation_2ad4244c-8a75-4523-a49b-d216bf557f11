import { User } from '@/types';
import { create } from 'zustand';
import { api } from '@/helpers/web';

type State = {
  user: User | null;
};

type Actions = {
  fetchUserInfo: () => Promise<void>;
};

const initialState: State = {
  user: null,
};

const useUserStore = create<State & Actions>((set) => ({
  ...initialState,
  fetchUserInfo: async () => {
    const user = await api.get('/api/user/getUser', {});

    set({ user });
  },
}));

export default useUserStore;
