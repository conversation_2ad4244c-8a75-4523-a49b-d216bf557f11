import { User } from '@/types';
import { create } from 'zustand';
import { api } from '@/helpers/web';

type State = {
  user: User | null;
};

type Actions = {
  fetchUserInfo: () => Promise<void>;
};

const initialState: State = {
  user: null,
};

const useUserStore = create<State & Actions>((set, get) => ({
  ...initialState,
  fetchUserInfo: async () => {
    console.log('[userStore] Fetching user');

    const { user: existingUser } = get();

    if (existingUser) {
      console.log('[userStore] User already loaded');

      return;
    }

    const user = await api.get('/api/user/getUser', {});

    set({ user });
  },
}));

export default useUserStore;
