import { create } from 'zustand';
import { TargetModel, SourceModel } from '@/types';
import { api } from '@/helpers/web';
import { cache, CACHE_KEYS } from '@/utils/cache';

type State = {
  targetModels: TargetModel[] | null;
  sourceModels: SourceModel[] | null;
  unmappedModelsByOrigin: Map<string, SourceModel[]>;
  targetSearchTerm: string;
};

type FetchTargetModelsProps = {
  customerKey: string;
  customerId: string;
  forceRefresh?: boolean;
};

type InitializeSourceModelsProps = {
  customerId: string;
  forceRefresh?: boolean;
};

type LoadUnmappedForOriginProps = {
  customerId: string;
  origin: string;
};

type Actions = {
  fetchTargetModels: (props: FetchTargetModelsProps) => Promise<void>;
  initializeSourceModels: (props: InitializeSourceModelsProps) => Promise<void>;
  loadUnmappedForOrigin: (props: LoadUnmappedForOriginProps) => Promise<void>;
  setTargetSearchTerm: (searchTerm: string) => void;
};

const initialState: State = {
  targetModels: null,
  sourceModels: null,
  unmappedModelsByOrigin: new Map(),
  targetSearchTerm: '',
};

const useDictionaryStore = create<State & Actions>((set, get) => ({
  ...initialState,

  // Target and source models are cached using local storage (browser) across refreshes to avoid unnecessary
  // query compute and load time. The cache persists for a given TTL and can be overriden with forceRefresh.
  fetchTargetModels: async (props) => {
    console.log('[dictionaryStore] Fetching target models');

    const { customerKey, customerId, forceRefresh = false } = props;
    const { targetModels } = get();
    const cacheKey = `${CACHE_KEYS.DICTIONARY_TARGET_MODELS}_${customerId}`;

    if (!forceRefresh) {
      // return store data if already loaded (except in situations of force refresh)
      if (!!targetModels) {
        console.log('[dictionaryStore] Target models already loaded');

        return;
      }

      // Next, check cache
      const cachedModels = await cache.get<TargetModel[]>(cacheKey);

      if (cachedModels) {
        set({ targetModels: cachedModels });

        return;
      }
    }

    // If initial load or force refresh, clear all cached target models (including other customer environments) to avoid storage limits.
    await cache.removeByPrefix(CACHE_KEYS.DICTIONARY_TARGET_MODELS);

    // Fetch from API
    const models = await api.get('/api/dataDictionary/getTargetDataDictionaries', {
      customerKey,
      customerId,
    });

    set({ targetModels: models });
    await cache.set(cacheKey, models);
  },
  setTargetSearchTerm: (searchTerm) => {
    set({ targetSearchTerm: searchTerm });
  },
  // On initialization, only mapped source models are fetched to limit size/load time.
  initializeSourceModels: async (props) => {
    const { customerId, forceRefresh } = props;
    const { sourceModels } = get();
    const cacheKey = `${CACHE_KEYS.DICTIONARY_SOURCE_MODELS}_${customerId}_mapped`;

    if (!forceRefresh) {
      if (!!sourceModels) {
        console.log('[dictionaryStore] Source models already initialized');

        return;
      }

      const cachedModels = await cache.get<SourceModel[]>(cacheKey);

      if (cachedModels) {
        set({ sourceModels: cachedModels });

        return;
      }
    }

    // Clear cached source models on initial load or force refresh
    await cache.removeByPrefix(CACHE_KEYS.DICTIONARY_SOURCE_MODELS);

    const models = await api.get('/api/dataDictionary/getSourceDataDictionaries', { customerId });

    set({ sourceModels: models });
    await cache.set(cacheKey, models);
  },
  // We don't fetch unmapped source models unless they are specifically requested, and then only
  // for that origin.
  loadUnmappedForOrigin: async (props) => {
    const { customerId, origin } = props;
    const { unmappedModelsByOrigin } = get();

    // Check if unmapped models are already loaded for this origin
    if (unmappedModelsByOrigin.has(origin)) {
      console.log(`[dictionaryStore] Unmapped models for ${origin} already loaded`);

      return;
    }

    // Check the cache for this origin's unmapped models
    const cacheKey = `${CACHE_KEYS.DICTIONARY_SOURCE_MODELS}_${customerId}_${origin}_unmapped`;
    const cached = await cache.get<SourceModel[]>(cacheKey);

    if (cached) {
      const newMap = new Map(unmappedModelsByOrigin);

      newMap.set(origin, cached);
      set({ unmappedModelsByOrigin: newMap });

      return;
    }

    // If unmapped models are not available from the cache, fetch from API
    const models = await api.get('/api/dataDictionary/getUnmappedByOrigin', { customerId, origin });

    await cache.set(cacheKey, models);

    const newMap = new Map(unmappedModelsByOrigin);

    newMap.set(origin, models);
    set({ unmappedModelsByOrigin: newMap });
  },
}));

export default useDictionaryStore;
