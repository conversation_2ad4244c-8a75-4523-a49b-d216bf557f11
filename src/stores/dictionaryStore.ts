import { create } from 'zustand';
import { TargetModel, SourceModel } from '@/types';
import { api } from '@/helpers/web';

type State = {
  targetModels: TargetModel[] | null;
  sourceModels: SourceModel[] | null;
  targetSearchTerm: string;
  sourceSearchTerm: string;
};

type FetchTargetModelsProps = {
  customerKey: string;
  customerId: string;
};

type FetchSourceModelsProps = {
  customerId: string;
};

type Actions = {
  fetchTargetModels: (props: FetchTargetModelsProps) => Promise<void>;
  fetchSourceModels: (props: FetchSourceModelsProps) => Promise<void>;
  setTargetSearchTerm: (searchTerm: string) => void;
  setSourceSearchTerm: (searchTerm: string) => void;
};

const initialState: State = {
  targetModels: null,
  sourceModels: null,
  targetSearchTerm: '',
  sourceSearchTerm: '',
};

const useDictionaryStore = create<State & Actions>((set) => ({
  ...initialState,
  fetchTargetModels: async (props) => {
    const { customerKey, customerId } = props;

    const models = await api.get('/api/dataDictionary/getTargetDataDictionaries', {
      customerKey,
      customerId,
    });

    set({ targetModels: models });
  },
  fetchSourceModels: async (props) => {
    const { customerId } = props;

    const models = await api.get('/api/dataDictionary/getSourceDataDictionaries', { customerId });

    set({ sourceModels: models });
  },
  setTargetSearchTerm: (searchTerm) => {
    set({ targetSearchTerm: searchTerm });
  },
  setSourceSearchTerm: (searchTerm) => {
    set({ sourceSearchTerm: searchTerm });
  },
}));

export default useDictionaryStore;
