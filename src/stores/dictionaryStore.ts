import { create } from 'zustand';
import { TargetModel, SourceModel } from '@/types';
import { api } from '@/helpers/web';
import { cache, CACHE_KEYS } from '@/utils/cache';

type State = {
  targetModels: TargetModel[] | null;
  sourceModels: SourceModel[] | null;
  targetSearchTerm: string;
};

type FetchTargetModelsProps = {
  customerKey: string;
  customerId: string;
  forceRefresh?: boolean;
};

type FetchSourceModelsProps = {
  customerId: string;
  forceRefresh?: boolean;
};

type Actions = {
  fetchTargetModels: (props: FetchTargetModelsProps) => Promise<void>;
  fetchSourceModels: (props: FetchSourceModelsProps) => Promise<void>;
  setTargetSearchTerm: (searchTerm: string) => void;
};

const initialState: State = {
  targetModels: null,
  sourceModels: null,
  targetSearchTerm: '',
};

const useDictionaryStore = create<State & Actions>((set) => ({
  ...initialState,
  // Target and source models are cached using local storage (browser) across refreshes to avoid unnecessary
  // query compute and load time. The cache persists for a given TTL and can be overriden with forceRefresh.
  fetchTargetModels: async (props) => {
    const { customerKey, customerId, forceRefresh = false } = props;
    const cacheKey = `${CACHE_KEYS.DICTIONARY_TARGET_MODELS}_${customerId}`;

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cachedModels = await cache.get<TargetModel[]>(cacheKey);

      if (cachedModels) {
        set({ targetModels: cachedModels });

        return;
      }
    }

    // If initial load or force refresh, clear all cached target models (including other customer environments) to avoid storage limits.
    await cache.removeByPrefix(CACHE_KEYS.DICTIONARY_TARGET_MODELS);

    // Fetch from API
    const models = await api.get('/api/dataDictionary/getTargetDataDictionaries', {
      customerKey,
      customerId,
    });

    // Update store and cache
    set({ targetModels: models });
    await cache.set(cacheKey, models);
  },
  fetchSourceModels: async (props) => {
    const { customerId, forceRefresh = false } = props;
    const cacheKey = `${CACHE_KEYS.DICTIONARY_SOURCE_MODELS}_${customerId}`;

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cachedModels = await cache.get<SourceModel[]>(cacheKey);

      if (cachedModels) {
        set({ sourceModels: cachedModels });

        return;
      }
    }

    // clear all cached source models, see comment above.
    await cache.removeByPrefix(CACHE_KEYS.DICTIONARY_SOURCE_MODELS);

    // Fetch from API
    const models = await api.get('/api/dataDictionary/getSourceDataDictionaries', { customerId });

    // Update store and cache
    set({ sourceModels: models });
    await cache.set(cacheKey, models);
  },
  setTargetSearchTerm: (searchTerm) => {
    set({ targetSearchTerm: searchTerm });
  },
}));

export default useDictionaryStore;
