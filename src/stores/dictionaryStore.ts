import { create } from 'zustand';
import { Model } from '@/types';
import { api } from '@/helpers/web';

type State = {
  models: Model[] | null;
};

type FetchModelsProps = {
  customerKey: string;
  customerId: string;
};

type Actions = {
  fetchModels: (props: FetchModelsProps) => Promise<void>;
};

const initialState: State = {
  models: null,
};

const useDictionaryStore = create<State & Actions>((set) => ({
  ...initialState,
  fetchModels: async (props) => {
    const { customerKey, customerId } = props;

    const models = await api.get('/api/dataDictionary/getTargetDataDictionaries', {
      customerKey,
      customerId,
    });

    set({ models });
  },
}));

export default useDictionaryStore;
