import { create } from 'zustand';
import { TargetModel, SourceModel } from '@/types';
import { api } from '@/helpers/web';
import { cache, CACHE_KEYS } from '@/utils/cache';

type State = {
  targetModels: TargetModel[] | null;
  sourceModels: SourceModel[] | null;
  targetSearchTerm: string;
  sourceSearchTerm: string;
};

type FetchTargetModelsProps = {
  customerKey: string;
  customerId: string;
};

type FetchSourceModelsProps = {
  customerId: string;
};

type Actions = {
  fetchTargetModels: (props: FetchTargetModelsProps, forceRefresh?: boolean) => Promise<void>;
  fetchSourceModels: (props: FetchSourceModelsProps, forceRefresh?: boolean) => Promise<void>;
  setTargetSearchTerm: (searchTerm: string) => void;
  setSourceSearchTerm: (searchTerm: string) => void;
  invalidateCache: () => void;
};

const initialState: State = {
  targetModels: null,
  sourceModels: null,
  targetSearchTerm: '',
  sourceSearchTerm: '',
};

const useDictionaryStore = create<State & Actions>((set) => ({
  ...initialState,
  fetchTargetModels: async (props, forceRefresh = false) => {
    const { customerKey, customerId } = props;
    const cacheKey = `${CACHE_KEYS.DICTIONARY_TARGET_MODELS}_${customerId}`;

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cachedModels = cache.get<TargetModel[]>(cacheKey);

      if (cachedModels) {
        set({ targetModels: cachedModels });

        return;
      }
    }

    // Fetch from API
    console.log('Fetching target models from API');

    const models = await api.get('/api/dataDictionary/getTargetDataDictionaries', {
      customerKey,
      customerId,
    });

    // Update store and cache
    set({ targetModels: models });
    cache.set(cacheKey, models);
  },
  fetchSourceModels: async (props, forceRefresh = false) => {
    const { customerId } = props;
    const cacheKey = `${CACHE_KEYS.DICTIONARY_SOURCE_MODELS}_${customerId}`;

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cachedModels = cache.get<SourceModel[]>(cacheKey);

      if (cachedModels) {
        set({ sourceModels: cachedModels });

        return;
      }
    }

    // Fetch from API
    console.log('Fetching source models from API');

    const models = await api.get('/api/dataDictionary/getSourceDataDictionaries', { customerId });

    // Update store and cache
    set({ sourceModels: models });
    cache.set(cacheKey, models);
  },
  setTargetSearchTerm: (searchTerm) => {
    set({ targetSearchTerm: searchTerm });
  },
  setSourceSearchTerm: (searchTerm) => {
    set({ sourceSearchTerm: searchTerm });
  },
  invalidateCache: () => {
    // Clear all dictionary-related cache entries
    cache.clearByPrefix(CACHE_KEYS.DICTIONARY_TARGET_MODELS);
    cache.clearByPrefix(CACHE_KEYS.DICTIONARY_SOURCE_MODELS);
  },
}));

export default useDictionaryStore;
