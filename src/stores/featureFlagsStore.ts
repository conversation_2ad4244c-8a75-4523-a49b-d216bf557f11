import { create } from 'zustand';
import { mapValues } from 'lodash';
import type { UserProfile } from '@auth0/nextjs-auth0/client';

type Config = {
  enabled: boolean;
  'enabled-orgs': string[];
  'enabled-users': string[];
};

type FeatureFlagsConfig = Record<string, Config>;

// map of flags to their enabled status
type FlagsEnabled = Record<string, { enabled: boolean }>;

type State = {
  featureFlags: FlagsEnabled | null;
};

type Actions = {
  setFeatureFlags: (featureFlagsConfig: FeatureFlagsConfig, user: UserProfile) => void;
};

const initialState: State = {
  featureFlags: null,
};

const useFeatureFlagsStore = create<State & Actions>((set) => ({
  ...initialState,
  setFeatureFlags: (featureFlagsConfig, user) => {
    const flagsEnabled = mapValues(featureFlagsConfig, (val) => {
      // A flag is enabled if the current org is in the list of enabled orgs or the current user
      // is in the list of enabled users.
      const enabled =
        val['enabled-orgs']?.includes(user!.org_id!) || val['enabled-users']?.includes(user!.sub!);

      return { enabled };
    });

    set({ featureFlags: flagsEnabled });
  },
}));

export default useFeatureFlagsStore;
