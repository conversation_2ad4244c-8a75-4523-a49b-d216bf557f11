import { Customer } from '@/types';
import { create } from 'zustand';

type State = {
  customer: Customer | null;
};

type Actions = {
  setCustomer: (customer: Customer) => void;
};

const initialState: State = {
  customer: null,
};

const useCustomerStore = create<State & Actions>((set) => ({
  ...initialState,
  setCustomer: (customer) => {
    set({ customer });
  },
}));

export default useCustomerStore;
