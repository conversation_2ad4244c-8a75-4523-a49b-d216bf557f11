import { create } from 'zustand';
import { DataSource } from '@/types';
import { api } from '@/helpers/web';

type State = {
  dataSources: DataSource[] | null;
  currentDataSource: DataSource | null;
  currentDataSourceIdx: number | null;
};

type FetchDataSourcesProps = {
  customerId: string;
};

type Actions = {
  fetchDataSources: (props: FetchDataSourcesProps) => Promise<void>;
  setCurrentDataSource: (dataSourceId: string) => void;
  reset: () => void;
};

const initialState: State = {
  dataSources: null,
  currentDataSource: null,
  currentDataSourceIdx: null,
};

const useDataSourceStore = create<State & Actions>((set, get) => ({
  ...initialState,

  fetchDataSources: async (props) => {
    const { customerId } = props;

    const dataSources = await api.get('/api/dataSources/getCustomerDataSources', {
      customerId,
    });

    set({ dataSources });
  },

  setCurrentDataSource: (dataSourceId: string) => {
    const { dataSources } = get();

    const currentDataSource = dataSources?.find((dataSource) => dataSource.id === dataSourceId);

    const currentDataSourceIdx = dataSources?.findIndex(
      (dataSource) => dataSource.id === dataSourceId,
    );

    set({ currentDataSource, currentDataSourceIdx });
  },

  reset: () => {
    set(initialState);
  },
}));

export default useDataSourceStore;
