import Dagre from '@dagrejs/dagre';
import { Node, Edge } from '@xyflow/react';
import { GeneralNode } from '@/types';

type LayoutOptions = {
  direction?: string;
};

export function getLayoutedElements(nodes: Node[], edges: Edge[], opts?: LayoutOptions) {
  const { direction = 'LR' } = opts || {};

  const isHorizontal = direction === 'LR';

  const graph = new Dagre.graphlib.Graph();

  graph.setDefaultEdgeLabel(() => ({}));

  graph.setGraph({ rankdir: direction });

  const mappedNodes: Node[] = [];
  const unmappedNodes: Node[] = [];

  nodes.forEach((node) => {
    const isMapped = !!edges.find((edge) => edge.source === node.id || edge.target === node.id);

    // Only format mapped nodes – the edges dictate the canvas shape
    if (isMapped) {
      mappedNodes.push(node);

      graph.setNode(node.id, {
        ...node,
        width: node.measured?.width ?? 0,
        height: node.measured?.height ?? 0,
      });
    } else {
      unmappedNodes.push(node);
    }
  });

  edges.forEach((edge) => graph.setEdge(edge.source, edge.target));

  Dagre.layout(graph);

  const newPositionedNodes = mappedNodes.map((node) => {
    const position = graph.node(node.id);

    return {
      ...node,
      targetPosition: isHorizontal ? 'left' : 'top',
      sourcePosition: isHorizontal ? 'right' : 'bottom',
      // We are shifting the dagre node position (anchor=center center) to the top left
      // so it matches the React Flow node anchor point (top left).
      position: {
        x: position.x - (node.measured?.width ?? 0) / 2,
        y: position.y - (node.measured?.height ?? 0) / 2,
      },
    };
  });

  // dagre and react flow libraries not playing nicely together, so ignoring types here
  // @ts-ignore
  const finalNodes = unmappedNodes.concat(newPositionedNodes);

  return {
    nodes: finalNodes as GeneralNode[],
    edges,
  };
}
