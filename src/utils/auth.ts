function isValidHost(hostname: string | null): boolean {
  if (!hostname) {
    return false;
  }

  // Allow localhost for development
  if (hostname.startsWith('localhost')) {
    return true;
  }

  // Allow any subdomain of gestalttech.com (but not the root domain)
  if (hostname.endsWith('.gestalttech.com')) {
    return true;
  }

  return false;
}

export function getBaseUrl(hostname): string {
  // Validate hostname to prevent host header injection
  if (!isValidHost(hostname)) {
    throw new Error('Invalid hostname');
  }

  const scheme = hostname.startsWith('localhost') ? 'http' : 'https';
  return `${scheme}://${hostname}`;
}
