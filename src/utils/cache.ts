// indexedDBCache.ts
type CacheItem<T> = {
  data: T;
  timestamp: number;
  ttl: number; // ms
};

type CacheOptions = {
  ttl?: number;
  dbName?: string;
  version?: number;
};

const DEFAULT_TTL = 4 * 60 * 60 * 1000; // 4 hours
const DEFAULT_DB_NAME = 'AppCache';

class IndexedDBCache {
  private defaultTTL: number;
  private dbName: string;
  private version: number;
  private db: IDBDatabase | null = null;

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || DEFAULT_TTL;
    this.dbName = options.dbName || DEFAULT_DB_NAME;
    this.version = options.version || 1;
  }

  private async initDB(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache');
        }
      };
    });
  }

  async set<T>(key: string, data: T, customTTL?: number): Promise<void> {
    try {
      const db = await this.initDB();
      const ttl = customTTL || this.defaultTTL;

      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl,
      };

      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      return new Promise((resolve, reject) => {
        const request = store.put(cacheItem, key);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Failed to set cache item:', error);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');

      return new Promise((resolve) => {
        const request = store.get(key);

        request.onsuccess = () => {
          const result = request.result as CacheItem<T> | undefined;

          if (!result) {
            resolve(null);
            return;
          }

          const now = Date.now();
          const isExpired = now - result.timestamp > result.ttl;

          // If the cached item has exceeded the time to live, get returns null. Any operations that
          // fetch cache items need to explicitly handle this (see dictionaryStore – if !cache, fetch from API)
          if (isExpired) {
            this.remove(key); // Don't await, let it run async
            resolve(null);

            return;
          }

          resolve(result.data);
        };

        // Cache fetch errors should warn but not throw. Cache failure results in a different
        // data fetch strategy, not app crash.
        request.onerror = () => {
          console.warn('Failed to get cache item:', request.error);
          resolve(null);
        };
      });
    } catch (error) {
      console.warn('Failed to get cache item:', error);
      return null;
    }
  }

  async remove(key: string): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      return new Promise((resolve) => {
        const request = store.delete(key);
        request.onsuccess = () => resolve();
        request.onerror = () => {
          console.warn('Failed to remove cache item:', request.error);
          resolve();
        };
      });
    } catch (error) {
      console.warn('Failed to remove cache item:', error);
    }
  }

  // This is used to clear a category of data, like all target or all source models.
  async removeByPrefix(prefix: string, opts?: { excludeKeys?: string[] }): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      return new Promise((resolve) => {
        const request = store.getAllKeys();

        request.onsuccess = () => {
          const keys = request.result as string[];
          const keysToRemove = keys.filter((key) => {
            const matchesPrefix = key.startsWith(prefix);
            const isExcluded = opts?.excludeKeys?.includes(key);

            return matchesPrefix && !isExcluded;
          });

          const deletePromises = keysToRemove.map((key) => this.remove(key));
          Promise.all(deletePromises).then(() => resolve());
        };

        request.onerror = () => {
          console.warn('Failed to remove cache items by prefix:', request.error);
          resolve();
        };
      });
    } catch (error) {
      console.warn('Failed to remove cache items by prefix:', error);
    }
  }

  async has(key: string): Promise<boolean> {
    const item = await this.get(key);
    return item !== null;
  }

  async clear(): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      return new Promise((resolve) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => {
          console.warn('Failed to clear cache:', request.error);
          resolve();
        };
      });
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }
}

// Export singleton instance
export const cache = new IndexedDBCache();

// Keep the same cache keys
export const CACHE_KEYS = {
  DICTIONARY_SOURCE_MODELS: 'dictionary_source_models',
  DICTIONARY_TARGET_MODELS: 'dictionary_target_models',
} as const;
