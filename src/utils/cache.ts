type CacheItem<T> = {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
};

type CacheOptions = {
  ttl?: number; // Default TTL in milliseconds
};

class LocalStorageCache {
  private defaultTTL: number;

  constructor(options: CacheOptions = {}) {
    // Default to 4 hours (4 * 60 * 60 * 1000 ms)
    this.defaultTTL = options.ttl || 4 * 60 * 60 * 1000;
  }

  /**
   * Check if localStorage is available
   */
  private isAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Set an item in cache with TTL
   */
  set<T>(key: string, data: T, customTTL?: number): void {
    if (!this.isAvailable()) {
      return;
    }

    const ttl = customTTL || this.defaultTTL;

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
    };

    try {
      localStorage.setItem(key, JSON.stringify(cacheItem));
    } catch (error) {
      console.warn('Failed to set cache item:', error);
    }
  }

  /**
   * Get an item from cache, returns null if expired or not found
   */
  get<T>(key: string): T | null {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const item = localStorage.getItem(key);
      if (!item) {
        return null;
      }

      const cacheItem: CacheItem<T> = JSON.parse(item);
      const now = Date.now();
      const isExpired = now - cacheItem.timestamp > cacheItem.ttl;

      if (isExpired) {
        this.remove(key);
        return null;
      }

      return cacheItem.data;
    } catch (error) {
      console.warn('Failed to get cache item:', error);
      this.remove(key);
      return null;
    }
  }

  /**
   * Remove an item from cache
   */
  remove(key: string): void {
    if (!this.isAvailable()) {
      return;
    }

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove cache item:', error);
    }
  }

  /**
   * Clear all cache items with a specific prefix
   */
  clearByPrefix(prefix: string): void {
    if (!this.isAvailable()) {
      return;
    }

    try {
      const keys = Object.keys(localStorage);

      keys.forEach((key) => {
        if (key.startsWith(prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear cache by prefix:', error);
    }
  }

  /**
   * Check if an item exists and is not expired
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Get cache info for debugging
   */
  getInfo(key: string): { exists: boolean; expired?: boolean; age?: number } {
    if (!this.isAvailable()) {
      return { exists: false };
    }

    try {
      const item = localStorage.getItem(key);
      if (!item) {
        return { exists: false };
      }

      const cacheItem: CacheItem<any> = JSON.parse(item);
      const now = Date.now();
      const age = now - cacheItem.timestamp;
      const expired = age > cacheItem.ttl;

      return { exists: true, expired, age };
    } catch {
      return { exists: false };
    }
  }
}

// Export a singleton instance
export const cache = new LocalStorageCache();

// Export cache keys as constants to avoid typos
export const CACHE_KEYS = {
  DICTIONARY_SOURCE_MODELS: 'dictionary_source_models',
  DICTIONARY_TARGET_MODELS: 'dictionary_target_models',
} as const;

// Helper function to format cache age for debugging
export function formatCacheAge(ageMs: number): string {
  const minutes = Math.floor(ageMs / (1000 * 60));
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  return `${minutes}m`;
}
