import { cache, CACHE_KEYS } from '../cache';

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: jest.fn((index: number) => Object.keys(store)[index] || null),
  };
})();

// Replace the global localStorage
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('Cache utility', () => {
  beforeEach(() => {
    // Clear localStorage and reset mocks before each test
    mockLocalStorage.clear();
    jest.clearAllMocks();
    
    // Reset Date.now to current time
    jest.spyOn(Date, 'now').mockRestore();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('TTL behavior', () => {
    const testData = { id: 1, name: 'test' };
    const cacheKey = 'test-key';

    it('should store and retrieve data within TTL', () => {
      // Arrange
      const now = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, testData);
      
      // Move forward 1 hour (within 4-hour default TTL)
      jest.spyOn(Date, 'now').mockReturnValue(now + 1 * 60 * 60 * 1000);
      const result = cache.get(cacheKey);

      // Assert
      expect(result).toEqual(testData);
      expect(mockLocalStorage.removeItem).not.toHaveBeenCalled();
    });

    it('should return null and remove expired data', () => {
      // Arrange
      const now = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, testData);
      
      // Move forward 5 hours (beyond 4-hour default TTL)
      jest.spyOn(Date, 'now').mockReturnValue(now + 5 * 60 * 60 * 1000);
      const result = cache.get(cacheKey);

      // Assert
      expect(result).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(cacheKey);
    });

    it('should respect custom TTL', () => {
      // Arrange
      const now = Date.now();
      const customTTL = 30 * 60 * 1000; // 30 minutes
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, testData, customTTL);
      
      // Move forward 45 minutes (beyond custom 30-minute TTL)
      jest.spyOn(Date, 'now').mockReturnValue(now + 45 * 60 * 1000);
      const result = cache.get(cacheKey);

      // Assert
      expect(result).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(cacheKey);
    });

    it('should handle data at exact TTL boundary', () => {
      // Arrange
      const now = Date.now();
      const customTTL = 60 * 60 * 1000; // 1 hour
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, testData, customTTL);
      
      // Move forward exactly 1 hour
      jest.spyOn(Date, 'now').mockReturnValue(now + customTTL);
      const result = cache.get(cacheKey);

      // Assert - At exact boundary, should be expired
      expect(result).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(cacheKey);
    });
  });

  describe('cache overwrite behavior', () => {
    const cacheKey = 'test-key';

    it('should overwrite existing cache data', () => {
      // Arrange
      const originalData = { id: 1, name: 'original' };
      const newData = { id: 2, name: 'updated' };

      // Act
      cache.set(cacheKey, originalData);
      cache.set(cacheKey, newData); // Overwrite
      const result = cache.get(cacheKey);

      // Assert
      expect(result).toEqual(newData);
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(2);
    });

    it('should update TTL when overwriting', () => {
      // Arrange
      const now = Date.now();
      const originalData = { id: 1, name: 'original' };
      const newData = { id: 2, name: 'updated' };
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, originalData);
      
      // Move forward 2 hours
      jest.spyOn(Date, 'now').mockReturnValue(now + 2 * 60 * 60 * 1000);
      cache.set(cacheKey, newData); // Overwrite with new timestamp
      
      // Move forward another 3 hours (5 hours total, but only 3 hours since overwrite)
      jest.spyOn(Date, 'now').mockReturnValue(now + 5 * 60 * 60 * 1000);
      const result = cache.get(cacheKey);

      // Assert - Should still be valid because TTL reset on overwrite
      expect(result).toEqual(newData);
    });
  });

  describe('error handling', () => {
    it('should handle localStorage unavailable gracefully', () => {
      // Arrange
      const originalSetItem = mockLocalStorage.setItem;
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage unavailable');
      });

      // Act & Assert - Should not throw
      expect(() => cache.set('test', { data: 'test' })).not.toThrow();
      expect(() => cache.get('test')).not.toThrow();

      // Restore
      mockLocalStorage.setItem = originalSetItem;
    });

    it('should handle corrupted cache data', () => {
      // Arrange
      const cacheKey = 'corrupted-key';
      mockLocalStorage.setItem(cacheKey, 'invalid-json');

      // Act
      const result = cache.get(cacheKey);

      // Assert
      expect(result).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(cacheKey);
    });
  });

  describe('cache info and utilities', () => {
    it('should provide accurate cache info', () => {
      // Arrange
      const now = Date.now();
      const testData = { id: 1, name: 'test' };
      const cacheKey = 'info-test';
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, testData);
      
      // Move forward 1 hour
      jest.spyOn(Date, 'now').mockReturnValue(now + 60 * 60 * 1000);
      const info = cache.getInfo(cacheKey);

      // Assert
      expect(info.exists).toBe(true);
      expect(info.expired).toBe(false);
      expect(info.age).toBe(60 * 60 * 1000); // 1 hour in milliseconds
    });

    it('should correctly identify expired cache', () => {
      // Arrange
      const now = Date.now();
      const testData = { id: 1, name: 'test' };
      const cacheKey = 'expired-test';
      jest.spyOn(Date, 'now').mockReturnValue(now);

      // Act
      cache.set(cacheKey, testData);
      
      // Move forward 5 hours (beyond TTL)
      jest.spyOn(Date, 'now').mockReturnValue(now + 5 * 60 * 60 * 1000);
      const info = cache.getInfo(cacheKey);

      // Assert
      expect(info.exists).toBe(true);
      expect(info.expired).toBe(true);
      expect(info.age).toBe(5 * 60 * 60 * 1000);
    });

    it('should handle non-existent cache keys', () => {
      // Act
      const info = cache.getInfo('non-existent');

      // Assert
      expect(info.exists).toBe(false);
      expect(info.expired).toBeUndefined();
      expect(info.age).toBeUndefined();
    });
  });

  describe('cache constants', () => {
    it('should have correct cache key constants', () => {
      expect(CACHE_KEYS.DICTIONARY_TARGET_MODELS).toBe('dictionary_target_models');
      expect(CACHE_KEYS.DICTIONARY_SOURCE_MODELS).toBe('dictionary_source_models');
    });
  });
});
