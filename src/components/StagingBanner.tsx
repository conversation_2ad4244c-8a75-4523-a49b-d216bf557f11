'use client';

import { useEffect, useState } from 'react';

export default function StagingBanner() {
  const [isStaging, setIsStaging] = useState(false);
  const branchName = process.env.NEXT_PUBLIC_BRANCH_NAME;
  const sha = process.env.NEXT_PUBLIC_SHA;

  useEffect(() => {
    setIsStaging(window.location.hostname.startsWith('staging.'));
  }, []);

  if (!isStaging) {
    return null;
  }

  return (
    <div className="bg-yellow-500 text-black text-center py-2 px-4 font-bold text-sm">
      🚧 STAGING 🚧 {branchName && `${branchName}`} 🚧 {sha && `${sha.substring(0, 8)}`} 🚧
    </div>
  );
}
