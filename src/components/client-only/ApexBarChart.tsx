'use client';
import ApexChart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { BarChartProps } from '@/types';

const DEFAULT_COLOR = '#2D3383';

export default function ApexBarChart(props: BarChartProps) {
  const { title, xCategories, xTitle, yTitle, seriesData, style } = props;

  const color = style?.color || DEFAULT_COLOR;

  const options: ApexOptions = {
    chart: {
      id: 'basic-bar',
      toolbar: {
        show: true,
        tools: {
          download: true,
          selection: true,
          zoom: true,
          zoomin: true,
          zoomout: true,
          pan: true,
          reset: true,
        },
      },
      fontFamily: 'Helvetica, Arial, sans-serif',
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        borderRadius: 4,
        dataLabels: {
          position: 'top',
        },
      },
    },
    colors: [color],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: true,
      width: 2,
      colors: ['transparent'],
    },
    xaxis: {
      categories: xCategories,
      title: {
        text: xTitle,
      },
    },
    yaxis: {
      title: {
        text: yTitle,
      },
    },
    fill: {
      opacity: 1,
    },
    title: {
      text: title,
      align: 'left',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
      },
    },
  };

  return <ApexChart type="bar" options={options} series={seriesData} />;
}
