import { ReactNode } from 'react';
import Popup from './Popup';

type ActionOption = {
  label: string;
  icon?: ReactNode;
  onClick: () => void | Promise<void>;
  disabled?: boolean;
};

type Props = {
  actionOptions: ActionOption[];
};

export default function ActionsMenu(props: Props) {
  const { actionOptions } = props;

  return (
    <Popup buttonIcon="DotsHorizontal">
      <div
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="options-menu"
        className="stroke-zinc-900 text-zinc-900"
      >
        {actionOptions.map((action, index) => (
          <div
            key={index}
            className={`flex items-center py-2 px-4 gap-x-4 hover:cursor-pointer hover:bg-zinc-100 ${
              action.disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={async () => {
              if (!action.disabled) {
                await action.onClick();
              }
            }}
          >
            <div className="w-7 flex justify-center">{action.icon}</div>
            <p className="text-xs">{action.label}</p>
          </div>
        ))}
      </div>
    </Popup>
  );
}
