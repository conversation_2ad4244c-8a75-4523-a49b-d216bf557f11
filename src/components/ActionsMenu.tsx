import { map } from 'lodash';
import Toggle from './Toggle';
import Popup from './Popup';
import { ToggleOption, ToggleOptions, SetStateFunction } from '@/types';

type ActionOption = {
  label: string;
  onClick: () => void | Promise<void>;
  disabled?: boolean;
  loading?: boolean;
};

type Props = {
  toggleOptions?: ToggleOptions;
  onToggleOptionsChange?: SetStateFunction<ToggleOptions>;
  onToggleSelect?: (opt: ToggleOption) => void | Promise<void>;
  actionOptions?: ActionOption[];
};

const TOGGLE_HEIGHT = 14;
const TOGGLE_WIDTH = 28;

export default function ActionsMenu(props: Props) {
  const { toggleOptions, onToggleOptionsChange, onToggleSelect, actionOptions } = props;

  const hasToggles = toggleOptions && Object.keys(toggleOptions).length > 0;
  const hasActions = actionOptions && actionOptions.length > 0;

  if (!hasToggles && !hasActions) {
    return null;
  }

  return (
    <Popup buttonIcon="DotsHorizontal">
      <div role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
        {/* Render toggle options */}
        {hasToggles &&
          onToggleOptionsChange &&
          map(toggleOptions, (opt, key) => {
            const { label, enabled } = opt;

            return (
              <div
                key={key}
                className="flex items-center py-2 px-4 gap-x-4 hover:cursor-pointer hover:bg-zinc-100"
                onClick={async () => {
                  onToggleOptionsChange((prev) => ({
                    ...prev,
                    [key]: { ...prev[key], enabled: !prev[key].enabled },
                  }));

                  if (onToggleSelect) {
                    await onToggleSelect(opt);
                  }
                }}
              >
                <Toggle checked={enabled} height={TOGGLE_HEIGHT} width={TOGGLE_WIDTH} />
                <p className="text-xs">{label}</p>
              </div>
            );
          })}

        {/* Render separator if both toggles and actions exist */}
        {hasToggles && hasActions && <div className="border-t border-zinc-200 my-1" />}

        {/* Render action options */}
        {hasActions &&
          actionOptions.map((action, index) => (
            <div
              key={index}
              className={`flex items-center py-2 px-4 gap-x-4 hover:cursor-pointer hover:bg-zinc-100 ${
                action.disabled ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              onClick={async () => {
                if (!action.disabled) {
                  await action.onClick();
                }
              }}
            >
              <div className="w-7 flex justify-center">
                {action.loading ? (
                  <div className="w-3 h-3 border border-zinc-300 border-t-indigo-600 rounded-full animate-spin" />
                ) : (
                  <div className="w-3 h-3 rounded-full bg-indigo-600" />
                )}
              </div>
              <p className="text-xs">{action.label}</p>
            </div>
          ))}
      </div>
    </Popup>
  );
}
