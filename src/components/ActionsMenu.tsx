import { ReactNode, useState } from 'react';
import Popup from './Popup';

type ActionOption = {
  label: string;
  icon?: ReactNode;
  onClick: () => void | Promise<void>;
  disabled?: boolean;
};

type Props = {
  actionOptions: ActionOption[];
};

export default function ActionsMenu(props: Props) {
  const { actionOptions } = props;

  // Use a key to force popup to remount (and close) when an action is performed
  const [popupKey, setPopupKey] = useState(0);

  const handleActionClick = async (action: ActionOption) => {
    if (!action.disabled) {
      // Force popup to remount by changing the key. This closes the popup on click
      setPopupKey((prev) => prev + 1);

      await action.onClick();
    }
  };

  return (
    <Popup key={popupKey} buttonIcon="DotsHorizontal">
      <div
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="options-menu"
        className="stroke-zinc-900 text-zinc-900"
      >
        {actionOptions.map((action, index) => (
          <div
            key={index}
            className={`flex items-center py-2 px-4 gap-x-4 hover:cursor-pointer hover:bg-zinc-100 ${
              action.disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={() => handleActionClick(action)}
          >
            <div className="w-7 flex justify-center">{action.icon}</div>
            <p className="text-xs">{action.label}</p>
          </div>
        ))}
      </div>
    </Popup>
  );
}
