import { ReactNode } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { DragVertical } from './icons';

type ListItem = {
  key: string;
  item: ReactNode;
};

type Props = {
  id: string;
  items: ListItem[];
  onDragEnd: (result: DropResult) => void;
};

export default function DraggableList(props: Props) {
  const { id, items, onDragEnd } = props;

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId={id}>
        {(provided) => (
          <div
            ref={provided.innerRef}
            className="flex flex-col gap-y-2 h-full"
            {...provided.droppableProps}
          >
            {items.map(({ key, item }, idx) => {
              const draggableId = `${key}${idx}`;

              return (
                <Draggable key={draggableId} draggableId={draggableId} index={idx}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      className="hover:cursor-grab"
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      {item}
                    </div>
                  )}
                </Draggable>
              );
            })}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}
