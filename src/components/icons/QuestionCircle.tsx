import React from 'react';

export default function QuestionCircle(props) {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      strokeWidth={2}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 9.203a3.259 3.259 0 0 1 1.348-1.748 2.922 2.922 0 0 1 2.086-.408c.72.132 1.371.531 1.84 1.127.47.596.727 1.35.726 2.129 0 2.198-3.088 3.297-3.088 3.297m.04 3.4h-.04"
      />
      <circle cx={12} cy={12} r={10} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
