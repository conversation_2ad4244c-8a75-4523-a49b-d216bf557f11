import { map } from 'lodash';
import { useState } from 'react';
import Toggle from './Toggle';
import Popup from './Popup';
import { ToggleOption, ToggleOptions, SetStateFunction } from '@/types';

type Props = {
  options: ToggleOptions;
  onOptionsChange: SetStateFunction<ToggleOptions>;
  onSelect?: (opt: ToggleOption) => void | Promise<void>;
};

const TOGGLE_HEIGHT = 14;
const TOGGLE_WIDTH = 28;

export default function ToggleOptionsComponent(props: Props) {
  const { options, onOptionsChange, onSelect } = props;

  // Use a key to force popup to remount (and close) when a toggle is changed
  const [popupKey, setPopupKey] = useState(0);

  const handleToggleClick = async (opt: ToggleOption, key: string) => {
    onOptionsChange((prev) => ({
      ...prev,
      [key]: { ...prev[key], enabled: !prev[key].enabled },
    }));

    if (onSelect) {
      await onSelect(opt);
    }

    // Force popup to remount by changing the key
    setPopupKey((prev) => prev + 1);
  };

  return (
    <Popup key={popupKey} buttonIcon="DotsHorizontal">
      <div role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
        {map(options, (opt, key) => {
          const { label, enabled } = opt;

          return (
            <div
              key={key}
              className="flex items-center py-2 px-4 gap-x-4 hover:cursor-pointer hover:bg-zinc-100"
              onClick={() => handleToggleClick(opt, key)}
            >
              <Toggle checked={enabled} height={TOGGLE_HEIGHT} width={TOGGLE_WIDTH} />
              <p className="text-xs">{label}</p>
            </div>
          );
        })}
      </div>
    </Popup>
  );
}
