import React, { useEffect, ReactNode } from 'react';
import XClose from '@components/icons/XClose';

type Props = {
  title?: any;
  titleComponent?: ReactNode;
  open: boolean;
  setIsOpen: Function;
  className?: string;
  titleUnderline?: boolean;
  noPadding?: boolean;
  children?: any;
  width?: string;
  fillContent?: boolean;
  titleClass?: string;
  noTitle?: boolean;
};

const defaultTitleClass = 'flex gap-2 text-2xl font-medium text-zinc-900';

export default function Modal(props: Props) {
  const {
    title,
    open,
    setIsOpen,
    className,
    titleUnderline,
    noPadding,
    children,
    width = 'w-96',
    fillContent,
    titleClass = defaultTitleClass,
    noTitle,
  } = props;

  // exit modal on escape
  useEffect(() => {
    const handleEscape = ({ key }) => {
      if (key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);

    return () => document.removeEventListener('keydown', handleEscape);
  }, [setIsOpen]);

  let contentDivClass = 'p-6 pt-0';

  if (noPadding) {
    contentDivClass = 'p-0';
  }

  if (fillContent) {
    contentDivClass = `${contentDivClass} w-full h-full`;
  }

  if (!open) {
    return;
  }

  if (!open) {
    return;
  }

  const titleComponent = props.titleComponent || <h2 className={titleClass}>{title}</h2>;

  const titleSection = (
    <div
      className={`w-full p-6 flex justify-between items-center border-b ${
        noPadding ? 'mb-0' : 'mb-6'
      } ${titleUnderline ? 'border-b-zinc-200' : 'border-b-transparent pb-0'}`}
    >
      {titleComponent}
      <button onClick={() => setIsOpen(false)}>
        <XClose className="stroke-zinc-900" />
      </button>
    </div>
  );

  return (
    <>
      <div
        onClick={() => setIsOpen(false)}
        className="fixed top-0 left-0 w-screen h-screen bg-zinc-700/75 z-50"
      />
      <div className="flex justify-center items-center fixed top-0 left-0 w-screen h-screen pointer-events-none z-50">
        <div className={`${width} max-w-full bg-white rounded-lg pointer-events-auto ${className}`}>
          {!noTitle && titleSection}
          <div className={contentDivClass}>{children}</div>
        </div>
      </div>
    </>
  );
}
