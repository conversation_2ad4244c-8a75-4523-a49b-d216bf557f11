import { ReactNode, useMemo } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import MenuTooltip from './MenuTooltip';

type ButtonType = 'button' | 'submit' | 'reset';

type Props = {
  onClick?: (e) => void;
  href?: string;
  type?: string;
  // used for html button type, like "submit"
  buttonType?: ButtonType;
  theme?: string;
  label?: string;
  icon?: string;
  iconLeft?: string;
  iconRight?: string;
  iconOnly?: boolean;
  disabled?: boolean;
  Icon?: any;
  children?: ReactNode;
  tooltipText?: string;
};

const Button = (props: Props) => {
  const {
    onClick,
    href,
    type = 'primary',
    buttonType,
    theme = 'default',
    label,
    icon = '',
    iconLeft,
    iconRight = '',
    iconOnly = false,
    disabled = false,
    Icon,
    children,
    tooltipText,
  } = props;

  // Set dynamic button and label classNames
  let buttonClass =
    'h-10 flex justify-center items-center gap-2 border rounded-md ring-2 ring-transparent transition-all focus:!ring-offset-1 focus:!ring-indigo-600';

  let textClass = 'text-sm font-medium select-none whitespace-nowrap';
  let iconClass = '';

  if (iconOnly) {
    buttonClass = `${buttonClass} w-10`;
  } else {
    buttonClass = `${buttonClass} px-4 py-2.5`;
  }

  switch (type) {
    case 'primary':
      buttonClass = `${buttonClass} shadow-sm bg-indigo-600 hover:bg-indigo-700 active:bg-indigo-800 disabled:bg-indigo-100 disabled:hover:border-zinc-200 disabled:shadow-none disabled:cursor-not-allowed`;
      textClass = `${textClass} text-white`;
      iconClass = 'w-5 h-5 stroke-2 stroke-white disabled:stroke-zinc-200';

      break;

    case 'secondary':
      buttonClass = `${buttonClass} shadow-sm bg-white border-zinc-200 hover:bg-zinc-50 hover:border-zinc-50 active:bg-zinc-100 active:border-zinc-100 disabled:bg-zinc-100 disabled:hover:border-zinc-200 disabled:shadow-none disabled:cursor-not-allowed`;
      textClass = `${textClass} text-zinc-900`;
      iconClass = 'w-5 h-5 stroke-2 stroke-zinc-900 !disabled:stroke-zinc-100';

      break;

    case 'transparent':
      buttonClass = `${buttonClass} bg-white border-none`;
      textClass = `${textClass} text-zinc-900`;
      iconClass = 'w-5 h-5 stroke-2 stroke-zinc-900';

      break;
  }

  switch (theme) {
    case 'success':
      buttonClass = `${buttonClass} !bg-emerald-200 border-none !shadow-none ring-0 hover:bg-emerald-200 active:bg-emerald-200 active:border-none`;
      textClass = `${textClass} !stroke-emerald-700 !text-emerald-700`;

      break;

    case 'critical':
      textClass = `${textClass} !text-red-900`;
      iconClass = `${iconClass} !stroke-red-900`;

      break;
  }

  const iconLeftComponent = useMemo(() => {
    if (Icon) {
      return <Icon className={iconClass} />;
    }

    if (!iconLeft && !icon) {
      return null;
    }

    const iconLeftName = iconLeft || icon;

    const IconLeft = dynamic(() => import(`@components/icons/${iconLeftName}`));

    // @ts-ignore
    return <IconLeft className={iconClass} />;
  }, [Icon, iconLeft, icon, iconClass]);

  const iconRightComponent = useMemo(() => {
    if (!iconRight) {
      return null;
    }

    const IconRight = dynamic(() => import(`@components/icons/${iconRight}`));

    // @ts-ignore
    return <IconRight className={iconClass} />;
  }, [iconRight, iconClass]);

  const button = (
    <div className="relative group">
      <button onClick={onClick} className={buttonClass} disabled={disabled} type={buttonType}>
        {iconLeftComponent}
        {children}
        {!iconOnly && <p className={textClass}>{label}</p>}
        {iconRightComponent}
      </button>
      {tooltipText && <MenuTooltip label={tooltipText} position="top" />}
    </div>
  );

  return href ? <Link href={href}>{button}</Link> : button;
};

export default dynamic(() => Promise.resolve(Button), { ssr: false });
