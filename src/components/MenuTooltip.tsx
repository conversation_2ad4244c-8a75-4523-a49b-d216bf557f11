type Props = {
  label: string;
  position?: 'bottom' | 'right';
};

export default function MenuTooltip(props: Props) {
  const { label, position = 'right' } = props;

  const positionClasses = {
    right: {
      base: 'left-10',
      hover: 'group-hover:left-14',
    },
    bottom: {
      base: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
      hover: 'group-hover:opacity-100',
    },
  };

  const { base, hover } = positionClasses[position];

  return (
    <span
      className={`absolute ${base} opacity-0 px-2 py-1 flex justify-center items-center text-xs font-normal text-white whitespace-nowrap bg-zinc-900 rounded select-none pointer-events-none transition-all group-hover:opacity-100 ${hover} z-50`}
    >
      {label}
    </span>
  );
}
