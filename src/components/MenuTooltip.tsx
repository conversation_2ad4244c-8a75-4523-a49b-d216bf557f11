type Props = {
  label: string;
  position?: 'bottom' | 'right';
};

export default function MenuTooltip(props: Props) {
  const { label, position = 'right' } = props;

  const absPos = position === 'right' ? 'left-10' : 'top-10';

  return (
    <span
      className={`absolute ${absPos} opacity-0 px-2 py-1 flex justify-center items-center text-xs font-normal text-white whitespace-nowrap bg-zinc-900 rounded select-none pointer-events-none transition-all group-hover:opacity-100 group-hover:left-14`}
    >
      {label}
    </span>
  );
}
