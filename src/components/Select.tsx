import { ReactNode } from 'react';
import Select, { ActionMeta, components, MenuPlacement } from 'react-select';
import { Option } from '@/types';
import { ChevronDown } from '@/components/icons';

type Props = {
  // name of input
  name?: string;
  className?: string;
  options: Option[];
  value?: Option | Option[] | null;
  onChange: (opt: Option | Option[], actionMeta?: ActionMeta<Option>) => void;
  image?: ReactNode;
  formatOptionLabel?: (opt: Option) => ReactNode;
  styles?: Record<string, Function>;
  placeholder?: string;
  disabled?: boolean;
  // can be used to target the opened menu to a different location than the parent container,
  // e.g. document.body if inside fixed-height/scrollable content
  menuPortalTarget?: any;
  menuPlacement?: MenuPlacement;
  errorMessage?: string;
  isMulti?: boolean;
};

const defaultStyles = {
  control: (baseStyles, state) => {
    let bgColor = 'white';

    if (state.isFocused || state.isDisabled) {
      bgColor = '#fafafa';
    }
    return {
      label: 'control',
      alignItems: 'center',
      cursor: 'default',
      display: 'flex',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      minHeight: 38,
      position: 'relative',
      transition: 'all 100ms',
      backgroundColor: bgColor,
      outline: state.isFocused ? '2px solid #4f46e5' : 'none',
      borderRadius: 6,
      paddingTop: '1px',
      paddingBottom: '1px',
      borderStyle: 'solid',
      boxShadow: '0 1px 2px 0 #e4e4e7',
      borderColor: '#e4e4e7',
      borderWidth: '1px',
      '&:hover': {
        cursor: 'pointer',
        backgroundColor: '#fafafa',
        borderColor: '#fafafa',
      },
      boxSizing: 'border-box',
    };
  },
  indicatorSeparator: () => ({
    display: 'none',
  }),
  option: (baseStyles, state) => ({
    ...baseStyles,
    backgroundColor: state.isSelected ? '#4f46e5' : 'white',
    '&:hover': {
      cursor: 'pointer',
      backgroundColor: '#f4f4f5 !important',
    },
    '&:nth-child(even)': {
      backgroundColor: '#fafafa',
    },
  }),
};

const DropdownIndicator = (props) => (
  <components.DropdownIndicator {...props}>
    <ChevronDown className="w-4 h-4 stroke-zinc-400" />
  </components.DropdownIndicator>
);

export default function SelectComponent(props: Props) {
  const {
    options,
    name,
    value,
    placeholder,
    onChange,
    image,
    formatOptionLabel,
    styles,
    className = 'w-80',
    disabled,
    menuPortalTarget,
    menuPlacement = 'auto',
    errorMessage,
    isMulti = false,
  } = props;

  const componentProps: Record<string, any> = {
    options,
    name,
    value,
    className,
    onChange,
    placeholder,
    interactive: true,
    isDisabled: disabled,
    menuPortalTarget,
    menuPlacement,
    styles: { ...defaultStyles, ...styles },
    isMulti,
  };

  if (formatOptionLabel) {
    componentProps.formatOptionLabel = formatOptionLabel;
  } else if (image) {
    componentProps.formatOptionLabel = (opt) => (
      <div className="flex items-center gap-x-2">
        {image}
        {opt.label}
      </div>
    );
  } else if (options.some((opt) => !!opt.icon)) {
    componentProps.formatOptionLabel = (opt) => (
      <div className="flex items-center gap-x-2">
        {opt.icon}
        {opt.label}
      </div>
    );
  }

  const containerClass = errorMessage?.length ? '!ring-red-600 ring-2' : '';

  return (
    <div className={containerClass}>
      <Select {...componentProps} components={{ DropdownIndicator }} />
    </div>
  );
}
