'use client';
import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect, useRef, ReactElement } from 'react';
import { usePage } from '@/hooks';
import { useUserStore } from '@/stores';
import MenuTooltip from './MenuTooltip';
import {
  SidebarCollapse,
  SidebarExpand,
  Database01,
  FileCheck02,
  ChevronDown,
  LogOut01,
  Bell03,
  LayoutBottom,
  PlayCircle,
  Settings02,
} from '@/components/icons';

type NavItem = {
  type?: string;
  href: string;
  aliasUrl?: string | null;
  name: string;
  icon: ReactElement;
  hidden?: boolean;
};

type Props = {
  pathname: string;
  user: any;
};

export default function Sidebar(props: Props) {
  const { pathname, user } = props;

  const { pageProps, setPageProps } = usePage();
  const { sidebarOpen } = pageProps;

  const { user: storeUser } = useUserStore();

  const profileRef = useRef<any>();
  const [isProfileActive, setIsProfileActive] = useState(false);

  useEffect(() => {
    const handleProfile = (e) => {
      if (profileRef.current && !profileRef.current.contains(e.target)) setIsProfileActive(false);
    };
    document.addEventListener('click', handleProfile);
  }, []);

  const toggleOpen = () => {
    setPageProps({ sidebarOpen: !sidebarOpen });
  };

  const navigation: NavItem[] = [
    {
      href: '/',
      aliasUrl: '/datasources',
      name: 'Data Sources',
      icon: <Database01 />,
      hidden: false,
    },
    {
      href: '/dictionary',
      aliasUrl: '/dictionary',
      name: 'Dictionary',
      icon: <LayoutBottom />,
    },
    {
      href: '/insights',
      aliasUrl: null,
      name: 'Insights',
      icon: <FileCheck02 />,
    },
  ];

  const navsFooter: NavItem[] = [
    {
      type: 'link',
      href: '/freeretros',
      aliasUrl: '/freeretros',
      name: 'Free retros',
      icon: <PlayCircle />,
    },
    {
      type: 'link',
      href: '/notifications',
      name: 'Notifications',
      icon: <Bell03 />,
    },
  ];

  if (storeUser?.isAdmin) {
    navsFooter.push({
      type: 'link',
      href: '/admin',
      name: 'Admin',
      icon: <Settings02 />,
    });
  }

  return (
    <>
      <div className="hidden lg:block fixed top-0 left-0 h-screen pl-2 py-2 z-10">
        {/* Desktop menu */}
        {/* If desktop menu is not minimized */}
        {sidebarOpen ? (
          <nav className="w-56 h-full space-y-8 bg-white rounded-lg">
            <div className="flex flex-col h-full">
              {/* Branding and collapse button */}
              <div className="relative w-full h-32 flex items-center justify-center">
                {/* Logo */}
                <Link href="/" className="flex-none">
                  <Image
                    src="/gestalt.svg"
                    alt="Gestalt"
                    width={138}
                    height={138}
                    className="mx-auto"
                  />
                </Link>

                {/* Collapse button */}
                <button
                  onClick={toggleOpen}
                  className="w-6 h-6 absolute -right-2.5 top-12 flex justify-center items-center bg-white rounded cursor-pointer hover:bg-zinc-50 active:bg-zinc-100 group"
                >
                  <SidebarCollapse className="w-5 h-5 stroke-zinc-300 group-hover:stroke-indigo-400" />
                </button>
              </div>
              {/* Sidebar content */}
              <div className="flex-1 flex flex-col h-full">
                {/* Top nav links */}
                <ul className="px-4 flex flex-col gap-2 flex-1">
                  {navigation
                    .filter((nav) => !nav.hidden)
                    .map((item, idx) => (
                      <li key={idx}>
                        <Link
                          href={item.href}
                          className={`flex items-center justify-start gap-x-2 group ${
                            pathname === item.href || pathname.includes(item.aliasUrl!)
                              ? 'text-indigo-700 font-semibold bg-gray-50'
                              : 'font-medium text-zinc-800'
                          } px-4 py-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150`}
                        >
                          <div
                            className={`${
                              pathname === item.href || pathname.includes(item.aliasUrl!)
                                ? 'stroke-indigo-700'
                                : 'stroke-zinc-400'
                            }`}
                          >
                            {item.icon}
                          </div>
                          {item.name}
                        </Link>
                      </li>
                    ))}
                </ul>
                {/* Bottom */}
                <div>
                  {/* Bottom nav links */}
                  <ul className="px-4 py-4">
                    {navsFooter
                      .filter((item) => !item.hidden)
                      .map((item, idx) =>
                        item.type === 'anchor' ? (
                          <li key={idx}>
                            <a
                              rel="noopener noreferrer"
                              target="_blank"
                              href={item.href}
                              className="flex items-center justify-start gap-x-2 group font-medium text-zinc-800 px-4 py-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150"
                            >
                              <div className="stroke-zinc-400">{item.icon}</div>
                              {item.name}
                            </a>
                          </li>
                        ) : (
                          <li key={idx}>
                            <Link
                              href={item.href}
                              className={`flex items-center justify-start gap-x-2 group ${
                                pathname === item.href || pathname.includes(item.aliasUrl!)
                                  ? 'text-indigo-700 font-semibold bg-gray-50'
                                  : 'font-medium text-zinc-800'
                              } px-4 py-2 rounded-lg hover:bg-gray-50 active:bg-gray-100 duration-150`}
                            >
                              <div
                                className={`${
                                  pathname === item.href || pathname.includes(item.aliasUrl!)
                                    ? 'stroke-indigo-700'
                                    : 'stroke-zinc-400'
                                }`}
                              >
                                {item.icon}
                              </div>
                              {item.name}
                            </Link>
                          </li>
                        ),
                      )}
                  </ul>

                  {/* Profile information */}
                  <div className="relative">
                    <button
                      ref={profileRef}
                      className="h-[88px] w-full pl-6 pr-4 py-6 border-zinc-100 border-t hover:bg-zinc-50/50 active:bg-zinc-50"
                      onClick={() => setIsProfileActive(!isProfileActive)}
                    >
                      <div className="flex items-center gap-x-2">
                        <Image
                          src={user.picture}
                          alt="User picture"
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                        <div className="w-full flex items-center">
                          <span className="block w-full text-left text-zinc-900">
                            {user.nickname
                              ? user.nickname
                              : user.name.includes('@')
                              ? user.name.split('@')[0]
                              : user.name}
                          </span>
                          <ChevronDown className="w-5 h-5 stroke-zinc-500" />
                        </div>
                      </div>
                    </button>
                    {isProfileActive ? (
                      <Link
                        href="/api/auth/logout"
                        prefetch={false}
                        className="absolute -top-14 left-6 flex justify-start items-center p-6 bg-white border border-zinc-200 rounded-lg shadow-xl hover:bg-red-50"
                      >
                        <div className="w-44 flex gap-3 px-4 py-2 rounded">
                          <LogOut01 className="stroke-zinc-400" />
                          <p className="text-base font-medium text-red-700">Sign out</p>
                        </div>
                      </Link>
                    ) : (
                      ''
                    )}
                  </div>
                </div>{' '}
                {/* end of bottom */}
              </div>{' '}
              {/* end of sidebar content */}
            </div>
          </nav>
        ) : (
          <>
            {/* If desktop menu is closed/collapsed */}
            <nav className="w-20 h-full bg-white space-y-8 rounded-xl">
              <div className="flex flex-col h-full">
                {/* Branding top */}
                <div className="relative h-32 flex items-center justify-center px-8">
                  {/* Logo */}
                  <Link href="/" className="flex-none">
                    <Image src="/logo.svg" alt="Logo" width={35} height={35} className="mx-auto" />
                  </Link>

                  {/* Expand button */}
                  <button
                    onClick={toggleOpen}
                    className="w-6 h-6 absolute -right-2.5 top-12 flex justify-center items-center bg-white rounded cursor-pointer hover:bg-zinc-50 active:bg-zinc-100 group"
                  >
                    <SidebarExpand className="w-5 h-5 stroke-zinc-300 group-hover:stroke-indigo-400" />
                  </button>
                </div>

                {/* Nav menu items */}
                <div className="flex-1 flex flex-col h-full">
                  {/* Top navlinks */}
                  <ul className="flex flex-col gap-2 px-4 flex-1">
                    {navigation
                      .filter((nav) => !nav.hidden)
                      .map((item, idx) => (
                        <li key={idx}>
                          <Link
                            href={item.href}
                            className="relative w-12 h-12 flex items-center justify-center gap-x-2 text-gray-600 p-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150 group"
                          >
                            <div
                              className={`${
                                pathname === item.href || pathname.includes(item.aliasUrl!)
                                  ? 'stroke-indigo-700'
                                  : 'stroke-zinc-400'
                              }`}
                            >
                              {item.icon}
                            </div>
                            <MenuTooltip label={item.name} />
                          </Link>
                        </li>
                      ))}
                  </ul>

                  {/* Bottom navlinks and profile */}
                  <div>
                    <ul className="px-4 py-4">
                      {navsFooter.map((item, idx) =>
                        item.type === 'anchor' ? (
                          <li key={idx}>
                            <a
                              target="_blank"
                              rel="noopener noreferrer"
                              href={item.href}
                              className="relative w-12 h-12 flex items-center justify-center gap-x-2 text-gray-600 p-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150 group"
                            >
                              <div className="stroke-zinc-300">{item.icon}</div>
                              <MenuTooltip label={item.name} />
                            </a>
                          </li>
                        ) : (
                          <li key={idx}>
                            <Link
                              href={item.href}
                              className="relative w-12 h-12 flex items-center justify-center gap-x-2 text-gray-600 p-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150 group"
                            >
                              <div className="stroke-zinc-300">{item.icon}</div>
                              <MenuTooltip label={item.name} />
                            </Link>
                          </li>
                        ),
                      )}
                    </ul>

                    {/* Profile */}
                    <div className="relative h-[88px] w-full flex justify-center items-center">
                      <button
                        ref={profileRef}
                        className="w-10 h-10 flex items-center cursor-pointer rounded-full duration-150"
                        onClick={() => setIsProfileActive(!isProfileActive)}
                      >
                        <Image
                          src={user.picture}
                          alt="User picture"
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                      </button>
                      {isProfileActive ? (
                        <Link
                          href="/api/auth/logout"
                          prefetch={false}
                          className="absolute -top-14 left-6 flex justify-start items-center p-6 bg-white border border-zinc-200 rounded-lg shadow-xl hover:bg-red-50"
                        >
                          <div className="w-44 flex gap-3 px-4 py-2 rounded">
                            <LogOut01 className="stroke-zinc-400" />
                            <p className="text-base font-medium text-red-700">Sign out</p>
                          </div>
                        </Link>
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </nav>
          </>
        )}
      </div>{' '}
      {/* End of desktop menu */}
      {/* Mobile header + menu */}
      <div className="lg:hidden xl:hidden 2xl:hidden">
        <nav
          className={`absolute z-20 bg-white w-full lg:static lg:text-md lg:border-none ${
            sidebarOpen ? 'shadow-lg rounded-b-xl lg:shadow-none' : ''
          }`}
        >
          <div className="items-center gap-x-14 px-4 max-w-full mx-auto lg:flex lg:px-8">
            {/* Mobile header */}
            <div className="flex items-center justify-between py-3 lg:py-5 lg:block">
              <Link href="/">
                <Image src="/gestalt.svg" width={120} height={50} alt="Float UI logo" />
              </Link>
              <div>
                <button className="text-gray-500 hover:text-gray-800" onClick={toggleOpen}>
                  {sidebarOpen ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="w-6 h-6"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 6.75A.75.75 0 013.75 6h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 6.75zM3 12a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 12zm8.25 5.25a.75.75 0 01.75-.75h8.25a.75.75 0 010 1.5H12a.75.75 0 01-.75-.75z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              </div>
            </div>
            {/* Mobile menu */}
            <div
              className={`nav-menu h-full w-full min-h-[calc(100dvh-60px)] p-4 flex flex-col justify-between rounded-b-lg bg-white border-t border-zinc-200 ${
                sidebarOpen ? 'block' : 'hidden'
              }`}
            >
              <ul className="items-center space-y-2 lg:flex lg:space-x-6 lg:space-y-0">
                {navigation
                  .filter((nav) => !nav.hidden)
                  .map((item, idx) => (
                    <li key={idx}>
                      <Link
                        href={item.href}
                        className={`flex items-center gap-x-2 ${
                          pathname === item.href ? 'text-blue-600' : 'text-gray-600'
                        } p-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150`}
                      >
                        <div className="text-gray-500">{item.icon}</div>
                        {item.name}
                      </Link>
                    </li>
                  ))}
              </ul>
              <div>
                <ul className="px-4 pb-4 text-md font-medium">
                  {navsFooter.map((item, idx) =>
                    item.type === 'anchor' ? (
                      <li key={idx}>
                        <a
                          target="_blank"
                          rel="noopener noreferrer"
                          href={item.href}
                          className="flex items-center gap-x-2 text-gray-600 p-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150"
                        >
                          <div className="text-gray-500">{item.icon}</div>
                          {item.name}
                        </a>
                      </li>
                    ) : (
                      <li key={idx}>
                        <Link
                          href={item.href}
                          className="flex items-center gap-x-2 text-gray-600 p-2 rounded-lg  hover:bg-gray-50 active:bg-gray-100 duration-150"
                        >
                          <div className="text-gray-500">{item.icon}</div>
                          {item.name}
                        </Link>
                      </li>
                    ),
                  )}
                </ul>
                <div className="w-full flex justify-between items-center py-6 px-2 border-t">
                  <div className="flex justify-center items-center space-x-2">
                    <div className="w-10 h-10 rounded-full overflow-hidden">
                      <Image src={user.picture} fill alt="user" />
                    </div>
                    <p className="text-zinc-900 font-medium">{user.name}</p>
                  </div>
                  <Link href="/api/auth/logout" prefetch={false}>
                    <button className="font-medium text-red-700 hover:text-red-800">
                      Sign out
                    </button>
                  </Link>
                </div>
              </div>
            </div>{' '}
            {/* End mobile menu */}
          </div>
        </nav>
      </div>{' '}
      {/* End mobile menu + header */}
    </>
  );
}
