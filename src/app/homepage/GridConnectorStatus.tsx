import { ConnectorStatus } from '@/types';
import Image from 'next/image';
import { Popover, Typography } from 'antd';
import { imgSrc } from 'lib/source';

const { Text } = Typography;

const popOverContent = (warnings) => {
  return (
    <div>
      {warnings.map((warning, i) => {
        return <p key={i}>{warning.message}</p>;
      })}
    </div>
  );
};

type Props = {
  status: ConnectorStatus;
  connectorType: string;
};

const connectedStatuses = ['connected', 'incomplete'];

export default function GridConnectorStatus(props: Props) {
  const { connectorType } = props;

  const status = props.status || { setup_state: 'incomplete' };

  if (connectedStatuses.includes(status.setup_state)) {
    return (
      <>
        <div className="flex space-x-1.5">
          {status?.warnings?.length > 0 && (
            <>
              <Popover
                content={popOverContent(status.warnings)}
                title={<Text type="danger">Warning !</Text>}
              >
                <Image width={16} height={16} src="img/sourceLink_Warning.svg" alt="" />
              </Popover>
            </>
          )}
          <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-emerald-50">
            <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
            <p className="text-sm font-medium capitalize text-emerald-700">online</p>
          </div>
        </div>
        <div
          className={
            'absolute -right-[72px] h-1 w-16 bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms] ' +
            (imgSrc.getOrDefault(connectorType).direction === 'inbound'
              ? 'sourceLinkOnline'
              : 'outboundLinkOnline')
          }
        ></div>
      </>
    );
  }

  if (status.setup_state === 'delay') {
    return (
      <>
        <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-yellow-50">
          <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
          <p className="text-sm font-medium capitalize text-yellow-700">delayed</p>
        </div>
        <div
          className={
            'absolute -right-[72px] h-1 w-16 bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms] ' +
            (imgSrc.getOrDefault(connectorType).direction === 'inbound'
              ? 'sourceLinkDelayed'
              : 'outboundLinkDelayed')
          }
        ></div>
      </>
    );
  }

  return (
    <>
      <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-red-50">
        <div className="w-2 h-2 rounded-full bg-red-500"></div>
        <p className="text-sm font-medium capitalize text-red-700">error</p>
      </div>
      <Image
        width={40}
        height={40}
        src="img/sourceLink_Error.svg"
        alt=""
        className="absolute -right-[72px] w-16"
      />
    </>
  );
}
