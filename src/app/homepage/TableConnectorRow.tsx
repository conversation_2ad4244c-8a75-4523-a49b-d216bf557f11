import Link from 'next/link';
import Dayjs from 'dayjs';
import { DataSource } from '@/types';
import { DataSourceBox } from '@/components';
import TableConnectorStatus from './TableConnectorStatus';

type Props = {
  dataSource: DataSource;
};

export default function TableConnectorRow(props: Props) {
  const { dataSource } = props;
  const { displayName, connector } = dataSource;

  return (
    <tr className="h-16 cursor-pointer hover:bg-zinc-100">
      <td className="px-6 py-3">
        <div className="flex space-x-3 items-center">
          <DataSourceBox
            // assume custom if no connector is present yet.
            connectorType={connector.connector_type || 'custom'}
            width="w-14"
            height="h-14"
          />
          <p className="text-sm font-medium text-gray-900">{displayName}</p>
        </div>
      </td>
      <TableConnectorStatus connector={connector} />
      <td className="px-6 py-3 text-sm text-gray-500 capitalize">
        {Dayjs.duration(connector.sync_frequency, 'minutes').humanize()}
      </td>
      <td className="px-6 py-3 text-sm text-gray-500">
        {connector.last_file_received && Dayjs(connector.last_file_received).utc().fromNow()}
      </td>
      <td className="px-6 py-3 text-sm text-gray-500">
        {Dayjs(connector.executed_at).add(connector.sync_frequency, 'minutes').utc().fromNow()}
      </td>
      <td className="px-6 py-3 text-sm font-semibold text-indigo-700 text-right">
        <Link key={connector.id} href={`datasources/${dataSource.id}`}>
          View details
        </Link>
      </td>
    </tr>
  );
}
