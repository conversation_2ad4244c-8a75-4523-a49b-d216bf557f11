import { ConnectorStatus, MappingStatus, Status } from '@/types';
import { Badge } from '@/components';
import { Popover, Typography } from 'antd';
import Image from 'next/image';

const { Text } = Typography;

type Props = {
  connectorStatus: ConnectorStatus;
  mappingStatus?: MappingStatus | null;
};

type BadgeProps = { type: Status; label: string };

const statusProps: Record<string, BadgeProps> = {
  new: {
    type: 'new',
    label: 'New data',
  },
  connected: { type: 'success', label: 'Online' },
  incomplete: { type: 'neutral', label: 'Incomplete' },
  delay: { type: 'warning', label: 'Delayed' },
  broken: { type: 'error', label: 'Error' },
};

const popOverContent = (warnings) => {
  return (
    <div>
      {warnings.map((warning, i) => {
        return <p key={i}>{warning.message}</p>;
      })}
    </div>
  );
};

const getBadgeProps = (props: Props): BadgeProps => {
  const { mappingStatus, connectorStatus } = props;

  if (mappingStatus === 'new') {
    return statusProps.new;
  }

  return statusProps[connectorStatus.setup_state] || statusProps.incomplete;
};

export default function GridConnectorBadge(props: Props) {
  const { connectorStatus } = props;

  const { type, label } = getBadgeProps(props);

  const hasWarnings = connectorStatus?.warnings?.length > 0;

  return (
    <div className={`flex space-x-1.5 ${hasWarnings && 'pr-3'}`}>
      {hasWarnings && (
        <>
          <Popover
            content={popOverContent(connectorStatus.warnings)}
            title={<Text type="danger">Warning !</Text>}
          >
            <Image width={16} height={16} src="img/sourceLink_Warning.svg" alt="" />
          </Popover>
        </>
      )}
      <Badge type={type} label={label} />
    </div>
  );
}
