import { ConnectorStatus, MappingStatus, Status } from '@/types';
import { Badge } from '@/components';
import { Popover, Typography } from 'antd';
import Image from 'next/image';

const { Text } = Typography;

// const CURRENT_POSSIBLE_WARNINGS = [
//   'unsupported_parquet_data_type',
//   'teleport_limits_warning',
//   'resync_table_to_capture_deletes',
//   'duplicate_columns',
//   'blocked_sync_mode_migration',
//   'teleport_sql_server_compatibility_level',
//   'snowflake_discontinuing_password_auth',
//   'salesforce_skipped_tables',
// ];

// If we encounter warnings we want to display, they can be added here
const SUPPORTED_WARNINGS: string[] = [];

type Props = {
  connectorStatus: ConnectorStatus;
  mappingStatus?: MappingStatus | null;
};

type BadgeProps = { type: Status; label: string };

const statusProps: Record<string, BadgeProps> = {
  new: {
    type: 'new',
    label: 'New data',
  },
  connected: { type: 'success', label: 'Online' },
  incomplete: { type: 'neutral', label: 'Incomplete' },
  delay: { type: 'warning', label: 'Delayed' },
  broken: { type: 'error', label: 'Error' },
};

const getBadgeProps = (props: Props): BadgeProps => {
  const { mappingStatus, connectorStatus } = props;

  if (mappingStatus === 'new') {
    return statusProps.new;
  }

  return statusProps[connectorStatus.setup_state] || statusProps.incomplete;
};

export default function GridConnectorBadge(props: Props) {
  const { connectorStatus } = props;

  const { type, label } = getBadgeProps(props);

  const supportedWarnings = connectorStatus?.warnings?.filter((warning) =>
    SUPPORTED_WARNINGS.includes(warning.code),
  );

  const hasWarnings = supportedWarnings?.length > 0;

  return (
    <div className={`flex space-x-1.5 ${hasWarnings && 'pr-3'}`}>
      {hasWarnings && (
        <>
          <Popover
            content={
              <div>
                {supportedWarnings.map((warning, i) => {
                  return <p key={i}>{warning.message}</p>;
                })}
              </div>
            }
            title={<Text type="danger">Warning !</Text>}
          >
            <Image width={16} height={16} src="img/sourceLink_Warning.svg" alt="" />
          </Popover>
        </>
      )}
      <Badge type={type} label={label} />
    </div>
  );
}
