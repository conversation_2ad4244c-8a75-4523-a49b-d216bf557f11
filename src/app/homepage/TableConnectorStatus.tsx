import { ConnectorBase } from '@/types';

type Props = {
  connector: ConnectorBase;
};

export default function TableConnectorStatus(props: Props) {
  const { connector } = props;

  const status = connector.status || { setup_state: 'incomplete' };

  if (status.setup_state === 'connected') {
    return (
      <td className="h-16 flex items-center px-6 py-3 font-medium text-sm text-gray-900">
        <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-emerald-50">
          <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
          <p className="text-sm font-medium capitalize text-emerald-700">Online</p>
        </div>
      </td>
    );
  }

  if (status.setup_state === 'delay') {
    return (
      <td className="h-16 flex items-center px-6 py-3 font-medium text-sm text-gray-900">
        <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-yellow-50">
          <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
          <p className="text-sm font-medium capitalize text-yellow-700">Delayed</p>
        </div>
      </td>
    );
  }

  return (
    <td className="h-16 flex items-center px-6 py-3 font-medium text-sm text-gray-900">
      <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-red-50">
        <div className="w-2 h-2 rounded-full bg-red-500"></div>
        <p className="text-sm font-medium capitalize text-red-700">Error</p>
      </div>
    </td>
  );
}
