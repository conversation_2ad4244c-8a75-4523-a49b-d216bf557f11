import Link from 'next/link';
import { DataSource } from '@/types';
import { DataSourceBox, ConnectorAnimation } from '@/components';
import { Route } from '@/components/icons';
import GridConnectorBadge from './GridConnectorBadge';
import { useFeatureFlags } from '@/hooks';

type Props = {
  dataSource: DataSource;
};

export default function GridConnectorRow(props: Props) {
  const { dataSource } = props;
  const { connector, mappingStatus } = dataSource;

  const { featureFlags } = useFeatureFlags();

  const smartMapperEnabled = featureFlags['can-run-smart-mapper']?.enabled;

  const connectorStatus = connector.status || { setup_state: 'imcomplete' };

  let destination = `datasources/${dataSource.id}`;

  if (mappingStatus === 'new' && smartMapperEnabled) {
    destination = `${destination}/data-mapping`;
  }

  return (
    <div className="flex items-center gap-x-3 w-full">
      <Link
        key={dataSource.id}
        href={destination}
        className="w-full justify-between items-center pr-1 bg-white border border-zinc-200 shadow-sm rounded-xl cursor-pointer hover:bg-zinc-50 active:bg-zinc-100 active:border-zinc-100 group"
      >
        <div className="flex flex-col w-full gap-y-4 p-2">
          <div className="flex w-full">
            <div className="flex flex-col w-full gap-y-4">
              <div className="h-full flex items-center space-x-4">
                <DataSourceBox connectorType={connector.connector_type || 'custom'} />
                <div className="flex flex-col">
                  <h2 className="text-xl font-medium text-zinc-900">{dataSource.displayName}</h2>
                  <p className="text-sm text-zinc-500 group-hover:text-indigo-600">View details</p>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <GridConnectorBadge connectorStatus={connectorStatus} mappingStatus={mappingStatus} />
            </div>
          </div>
          {mappingStatus === 'new' && smartMapperEnabled && (
            <div className="flex items-center p-2 justify-between rounded-md bg-purple-50 w-full">
              <div className="flex items-center gap-x-2">
                <Route className="stroke-purple-500 w-4 h-4" />
                <p className="text-purple-700 text-sm">Your data is ready to be mapped</p>
              </div>
              <p className="text-indigo-700 text-sm">Get started</p>
            </div>
          )}
        </div>
      </Link>
      <ConnectorAnimation
        connectorStatus={connectorStatus}
        connectorType={connector.connector_type}
        mappingStatus={mappingStatus}
        width="w-32"
      />
    </div>
  );
}
