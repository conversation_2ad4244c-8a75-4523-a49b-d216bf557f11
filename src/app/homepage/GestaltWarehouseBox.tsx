import Image from 'next/image';

export default function GestaltWarehouseBox() {
  return (
    <div className="w-full h-full flex flex-col justify-center items-center space-y-10 bg-white rounded-xl">
      <Image src="/img/Gestalt_Cube.svg" width={160} height={160} alt="Gestalt Logo Cube" />
      <h2 className="text-5xl font-medium text-zinc-900 text-center leading-tight">
        Gestalt
        <br />
        Data Warehouse
      </h2>{' '}
      <div className="flex justify-center items-center space-x-2 bg-emerald-100 p-1 pr-3 rounded-full">
        <div className="px-2 py-0.5 flex space-x-1.5 justify-center items-center rounded-full bg-emerald-50">
          <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
          <p className="text-sm font-medium capitalize text-emerald-700">Online</p>
        </div>
        <p className="text-sm font-medium text-emerald-700">All systems operational</p>
      </div>
    </div>
  );
}
