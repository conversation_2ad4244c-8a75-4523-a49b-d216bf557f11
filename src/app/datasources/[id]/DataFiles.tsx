import { useState, useEffect, useMemo, useCallback } from 'react';
import { FileMetadata, FileProgress } from '@/types';
import { useCustomer } from '@/hooks';
import { Loading, InputText, Button } from '@/components';
import DataFileRow from './DataFileRow';
import { filterData } from '@/utils/data';
import FileUploadModal from './FileUploadModal';
import { getAvgUploadProgress } from '@/utils/file';
import { handleFileUpload } from '../new/utils';
import { api } from '@/helpers/web';
import { useDataSourceStore } from '@/stores';

export default function DataFiles() {
  const { currentDataSource: dataSource } = useDataSourceStore();
  const { config, connector } = dataSource!;

  const connectorType = connector.connector_type;

  const { customerKey } = useCustomer();

  const [files, setFiles] = useState<any[]>();
  const [uploadModalOpen, setUploadModalOpen] = useState<boolean>(false);
  const [newFiles, setNewFiles] = useState<File[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [progress, setProgress] = useState<FileProgress>({});
  // total percent complete for all files
  const [uploadCompletion, setUploadCompletion] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState<string>();

  const getFiles = useCallback(async () => {
    const genericFiles = await api.get('/api/dataSources/getGenericObjects', {
      customerKey,
      sourceRecordName: config?.sourceRecordName,
    });

    setFiles(genericFiles);
  }, [customerKey, config]);

  useEffect(() => {
    if (!customerKey) {
      return;
    }

    getFiles();
  }, [getFiles, customerKey]);

  useEffect(() => {
    if (!submitting) {
      return;
    }

    const avgProgress = getAvgUploadProgress(progress);

    setUploadCompletion(avgProgress);
  }, [submitting, progress]);

  const filteredFiles = useMemo(
    () => filterData<FileMetadata>(files || [], { searchTerm, nameField: 'displayName' }),
    [files, searchTerm],
  );

  if (!files) {
    return <Loading />;
  }

  if (!files.length) {
    return (
      <div className="h-full w-full flex justify-center items-center py-16">
        <p className="text-zinc-400">No files</p>
      </div>
    );
  }

  const handleNewFileUpload = async () => {
    try {
      setSubmitting(true);

      const fileInfo = await handleFileUpload({
        customSourceName: config!.sourceRecordName!,
        customerKey,
        files: newFiles,
        onProgress: setProgress,
      });

      await api.post('/api/dataSources/handleGenericUpload', {
        customerKey,
        fileInfo,
        connectorSlug: connector.connector_id,
      });

      setSubmitting(false);
      setUploadCompletion(100);
      setUploadModalOpen(false);
      setNewFiles([]);
      await getFiles();
    } catch (error: any) {
      console.error({ error, stack: error.stack });

      setSubmitting(false);
      setUploadCompletion(0);
    }
  };

  return (
    <>
      <div className="flex flex-col">
        <InputText
          icon={'SearchMd'}
          placeholder="Search"
          onChange={(e) => setSearchTerm(e.target.value)}
          value={searchTerm}
        />
        <div className="mb-6">
          {filteredFiles.map((file, idx) => (
            <DataFileRow key={`${file.key}${idx}`} connectorType={connectorType} file={file} />
          ))}
        </div>
        <Button label="Add new file" onClick={() => setUploadModalOpen(true)} />
      </div>
      <FileUploadModal
        allowedTypes={['.csv', '.xlsx']}
        submitting={submitting}
        uploadCompletion={uploadCompletion}
        isOpen={uploadModalOpen}
        onOpenChange={setUploadModalOpen}
        files={newFiles}
        onFilesChange={setNewFiles}
        handleSubmit={handleNewFileUpload}
      />
    </>
  );
}
