import { useState, useEffect, useMemo } from 'react';
import { message } from 'antd';
import { isNil } from 'lodash';
import { ConnectorDetail, ToggleOptions } from '@/types';
import { PaginationProps } from './page';
import PendingConnector from './PendingConnector';
import { Overview, Loading, ActionsMenu } from '@/components';
import { getConnectorOverview, getConnectorHistory } from './utils';
import { useCustomer } from '@/hooks';
import SyncHistory from './SyncHistory';
import DataOceansSyncHistory from './DataOceansSyncHistory';
import DataDetails from './DataDetails';
import { api } from '@/helpers/web';
import { useDictionaryStore, useDataSourceStore } from '@/stores';

export default function DataSourceContent() {
  const { currentDataSource: dataSource } = useDataSourceStore();

  const connectorSlug = dataSource?.connector?.connector_id;

  const [messageApi, contextHolder] = message.useMessage();
  const { customerKey, customerId } = useCustomer();

  const { sourceModels, fetchSourceModels, invalidateCache } = useDictionaryStore();

  const [connectorDetail, setConnectorDetail] = useState<ConnectorDetail>({
    historyLoading: true,
    error: null,
    connector: null,
  });

  const [currentPage, setCurrentPage] = useState<PaginationProps>({
    pageNo: 1,
    hasMore: false,
  });

  const [customConnectorPending, setCustomConnectorPending] = useState<boolean>();
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  // This should default to enabled: false when we have a better process for generating mappings promptly. Customers should have their mappings
  // defined when they're given access to the web app. For now, we still have a backlog of mappings to document.
  const [toggleOptions, setToggleOptions] = useState<ToggleOptions>({
    includeUnmapped: { label: 'Include unmapped models', enabled: false },
  });

  const filteredModels = useMemo(() => {
    if (!sourceModels || !dataSource) {
      return null;
    }

    return sourceModels.filter((model) => {
      const isDataSourceModel = model.origin === dataSource.sourceModelKey;

      if (!toggleOptions.includeUnmapped.enabled) {
        return isDataSourceModel && model.mappingCount > 0;
      }

      return isDataSourceModel;
    });
  }, [sourceModels, dataSource, toggleOptions]);

  const { connector } = connectorDetail;

  const handleHardRefresh = async () => {
    setRefreshing(true);
    try {
      // Invalidate cache and force refresh source models
      invalidateCache();

      await fetchSourceModels({ customerId, forceRefresh: true });

      messageApi.success('Data refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh data:', error);
      messageApi.error('Failed to refresh data');
    } finally {
      setRefreshing(false);
    }
  };

  // fetch connector sync history
  useEffect(() => {
    if (!connectorSlug) {
      return;
    }

    getConnectorHistory({
      name: connectorSlug,
      onConnectorDetailChange: setConnectorDetail,
    });
  }, [connectorSlug]);

  // set current history page
  useEffect(() => {
    if (connector && connector.sync_histories.length >= 10) {
      setCurrentPage((prev) => ({ ...prev, hasMore: true }));
    }
  }, [connector]);

  // check if the raw data has been processed and the views are present in {customer_db}.customer. If not, display pending state.
  useEffect(() => {
    if (!customerKey || !dataSource) {
      return;
    }

    if (!dataSource.connector.connector_id) {
      setCustomConnectorPending(true);

      return;
    }

    const sourceRecordName = dataSource?.config?.sourceRecordName;
    const isCustom = dataSource.connector.connector_type === 'custom';

    const checkGenericData = async () => {
      const { viewExists } = await api.get('/api/dataSources/checkGenericSourceData', {
        customerKey,
        sourceRecordName,
      });

      const pending = isCustom && !viewExists;

      setCustomConnectorPending(pending);
    };

    checkGenericData();
  }, [customerKey, dataSource, connector]);

  if (!dataSource) {
    return <Loading />;
  }

  if (customConnectorPending) {
    return <PendingConnector sourceName={dataSource.displayName} />;
  }

  if (!connector || isNil(customConnectorPending)) {
    return <Loading />;
  }

  const isCustom = connector.connector_type === 'custom';

  return (
    <div className="flex flex-col gap-10">
      {contextHolder}
      <Overview overviewContents={getConnectorOverview(connector)} />

      {/* Inbound data bottom */}
      {connector.connector_type !== 'dataoceans' ? (
        <div className="flex gap-10">
          {/* Data tables */}
          <div className="flex flex-1 flex-col space-y-3 min-w-0">
            <div className="flex justify-between items-center">
              <h2 className={`text-2xl font-medium text-zinc-900 ${isCustom ? 'mb-2' : ''}`}>
                {isCustom ? 'Data files' : 'Data models'}
              </h2>
              {!isCustom && (
                <ActionsMenu
                  toggleOptions={toggleOptions}
                  onToggleOptionsChange={setToggleOptions}
                  actionOptions={[
                    {
                      label: refreshing ? 'Refreshing...' : 'Hard Refresh',
                      onClick: handleHardRefresh,
                      disabled: refreshing,
                      loading: refreshing,
                    },
                  ]}
                />
              )}
            </div>
            <div className="relative max-h-[800px] overflow-y-scroll flex flex-col bg-white divide-y divide-zinc-200 rounded-lg px-8 py-8 no-scrollbar">
              <DataDetails sourceModels={filteredModels} refreshing={refreshing} />
            </div>
          </div>
          {/* Run history */}
          <div className="flex flex-1 min-w-0">
            <SyncHistory
              connector={connector}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
              onConnectorDetailChange={setConnectorDetail}
              messageApi={messageApi}
              loading={historyLoading}
              onLoading={setHistoryLoading}
            />
          </div>
        </div>
      ) : (
        // Show the letters created if dataoceans is current page
        <DataOceansSyncHistory connector={connector} />
      )}
    </div>
  );
}
