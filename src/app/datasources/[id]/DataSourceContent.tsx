import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { isNil } from 'lodash';
import {
  DataSource,
  ConnectorDetail,
  ModelSummary,
  ToggleOption,
  ToggleOptions,
  ModelQueryOptions,
} from '@/types';
import { PaginationProps } from './page';
import PendingConnector from './PendingConnector';
import { Overview, Loading, ToggleOptions as ToggleOptionsComponent } from '@/components';
import { getConnectorOverview, getConnectorHistory } from './utils';
import { useCustomer } from '@/hooks';
import SyncHistory from './SyncHistory';
import DataOceansSyncHistory from './DataOceansSyncHistory';
import DataDetails from './DataDetails';
import { api } from '@/helpers/web';
import { getSourceModelSummaries } from './utils';

type Props = {
  dataSource?: DataSource;
};

export default function DataSourceContent(props: Props) {
  const { dataSource } = props;

  const connectorSlug = dataSource?.connector?.connector_id;

  const [messageApi, contextHolder] = message.useMessage();
  const { customerKey, customerId } = useCustomer();

  const [connectorDetail, setConnectorDetail] = useState<ConnectorDetail>({
    historyLoading: true,
    error: null,
    connector: null,
  });

  const [currentPage, setCurrentPage] = useState<PaginationProps>({
    pageNo: 1,
    hasMore: false,
  });

  const [customConnectorPending, setCustomConnectorPending] = useState<boolean>();
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [sourceModels, setSourceModels] = useState<ModelSummary[]>();
  const [sourceModelsLoading, setSourceModelsLoading] = useState<boolean>(true);
  // This should default to enabled: false when we have a better process for generating mappings promptly. Customers should have their mappings
  // defined when they're given access to the web app. For now, we still have a backlog of mappings to document.
  const [toggleOptions, setToggleOptions] = useState<ToggleOptions>({
    includeUnmapped: { label: 'Include unmapped models', enabled: false },
  });

  const { connector } = connectorDetail;

  // Fetch source model summaries
  const getSourceModels = useCallback(
    async (opts?: ModelQueryOptions) => {
      if (!customerId || !dataSource) {
        return;
      }

      setSourceModelsLoading(true);

      const sourceModels = await getSourceModelSummaries({
        sourceModelKey: dataSource.sourceModelKey,
        opts,
      });

      setSourceModels(sourceModels);

      setSourceModelsLoading(false);
    },
    [customerId, dataSource],
  );

  useEffect(() => {
    getSourceModels({ includeUnmapped: toggleOptions.includeUnmapped.enabled });
  }, [getSourceModels, toggleOptions.includeUnmapped.enabled]);

  // refetch when include empty option changes
  const optRefetch = async (opt: ToggleOption) => {
    await getSourceModels({ includeUnmapped: !opt.enabled });
  };

  // fetch connector sync history
  useEffect(() => {
    if (!connectorSlug) {
      return;
    }

    getConnectorHistory({
      name: connectorSlug,
      onConnectorDetailChange: setConnectorDetail,
    });
  }, [connectorSlug]);

  // set current history page
  useEffect(() => {
    if (connector && connector.sync_histories.length >= 10) {
      setCurrentPage((prev) => ({ ...prev, hasMore: true }));
    }
  }, [connector]);

  // check if the raw data has been processed and the views are present in {customer_db}.customer. If not, display pending state.
  useEffect(() => {
    if (!customerKey || !dataSource) {
      return;
    }

    if (!dataSource.connector.connector_id) {
      setCustomConnectorPending(true);

      return;
    }

    const sourceRecordName = dataSource?.config?.sourceRecordName;
    const isCustom = dataSource.connector.connector_type === 'custom';

    const checkGenericData = async () => {
      const { viewExists } = await api.get('/api/dataSources/checkGenericSourceData', {
        customerKey,
        sourceRecordName,
      });

      const pending = isCustom && !viewExists;

      setCustomConnectorPending(pending);
    };

    checkGenericData();
  }, [customerKey, dataSource, connector]);

  if (!dataSource) {
    return <Loading />;
  }

  if (customConnectorPending) {
    return <PendingConnector sourceName={dataSource.displayName} />;
  }

  if (!connector || isNil(customConnectorPending)) {
    return <Loading />;
  }

  const isCustom = connector.connector_type === 'custom';

  return (
    <div className="flex flex-col gap-10">
      {contextHolder}
      <Overview overviewContents={getConnectorOverview(connector)} />

      {/* Inbound data bottom */}
      {connector.connector_type !== 'dataoceans' ? (
        <div className="flex gap-10">
          {/* Data tables */}
          <div className="flex flex-1 flex-col space-y-3 min-w-0">
            <div className="flex justify-between items-center">
              <h2 className={`text-2xl font-medium text-zinc-900 ${isCustom ? 'mb-2' : ''}`}>
                {isCustom ? 'Data files' : 'Data models'}
              </h2>
              {!isCustom && (
                <ToggleOptionsComponent
                  options={toggleOptions}
                  onOptionsChange={setToggleOptions}
                  onSelect={optRefetch}
                />
              )}
            </div>
            <div className="relative max-h-[800px] overflow-y-scroll flex flex-col bg-white divide-y divide-zinc-200 rounded-lg px-8 py-8 no-scrollbar">
              <DataDetails
                dataSource={dataSource}
                sourceModels={sourceModels}
                sourceModelsLoading={sourceModelsLoading}
              />
            </div>
          </div>
          {/* Run history */}
          <div className="flex flex-1 min-w-0">
            <SyncHistory
              connector={connector}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
              onConnectorDetailChange={setConnectorDetail}
              messageApi={messageApi}
              loading={historyLoading}
              onLoading={setHistoryLoading}
            />
          </div>
        </div>
      ) : (
        // Show the letters created if dataoceans is current page
        <DataOceansSyncHistory connector={connector} />
      )}
    </div>
  );
}
