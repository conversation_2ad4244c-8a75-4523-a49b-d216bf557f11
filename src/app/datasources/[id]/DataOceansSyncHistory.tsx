// import Dayjs from 'dayjs';
import { Connector } from '@/types';
// import Link from 'next/link';

type Props = {
  connector: Connector;
};

// Code commented out on 7/18/24 because it's outdated and unused.
export default function DataOceansSyncHistory(_: Props) {
  return (
    <div className="w-7/12 w-full min-w-full flex flex-col space-y-3">
      <h2 className="text-2xl font-medium text-zinc-900">Letters created</h2>
      <div className="w-full px-8 py-4 flex flex-col justify-center items-start bg-white rounded-lg overflow-hidden divide-y divide-zinc-200">
        {/* {connector.histories.map((history, index) => (
          <div className="w-full flex justify-between items-center py-4" key={index}>
            <div className="flex flex-col w-full justify-start">
              <h3 className="text-xl font-medium text-zinc-900">
                {'TRG_TestFile_' +
                  Dayjs(history.created, 'MMMM D, YYYY at h:mm a').format('YYYYMMDD') +
                  '.csv'}
              </h3>
              <div className="flex gap-2">
                <div className="flex justify-center items-center px-3 py-1 text-sm font-medium text-zinc-900 bg-zinc-100 rounded">
                  <p>{(history as any).type}</p>
                </div>
                <p className="text-sm text-zinc-700">
                  Generated on {history.created && Dayjs(history.created).format('LL')}
                  {(history as any).type === 'manual' && <span> by {(history as any).author}</span>}
                </p>
              </div>
            </div>
            <Link
              href="#"
              className="flex justify-center items-center px-4 py-2.5 bg-white border border-zinc-200 rounded-md shadow-sm whitespace-nowrap text-sm font-medium hover:bg-zinc-50 hover:border-zinc-50 active:bg-zinc-100 active:border-zinc-100"
            >
              Download letter
            </Link>
          </div>
        ))} */}
      </div>
    </div>
  );
}
