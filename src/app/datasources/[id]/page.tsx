'use client';

import Dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
Dayjs.extend(customParseFormat);
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import LocalizedFormat from 'dayjs/plugin/localizedFormat';
import duration from 'dayjs/plugin/duration';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { imgSrc } from '@lib/source';
import { Button, Select, PageTitle, Loading } from '@/components';
import DataSourceContent from './DataSourceContent';
import { useDataSourceStore } from '@/stores';
import { getDisplayConnectorType } from '@/utils/dataSources';

Dayjs.extend(relativeTime);
Dayjs.extend(utc);
Dayjs.extend(LocalizedFormat);
Dayjs.extend(duration);

type Params = {
  id: string;
};

type Props = {
  params: Params;
};

export type PaginationProps = {
  pageNo: number;
  hasMore: boolean;
};

export default function Page(props: Props) {
  const { params } = props;
  const { id: dataSourceId } = params;

  const router = useRouter();
  const { dataSources, currentDataSource, currentDataSourceIdx } = useDataSourceStore();

  const loading = !currentDataSource;

  const dataSourceOptions =
    dataSources?.map((dataSource) => ({
      label: dataSource.displayName,
      value: dataSource,
    })) || [];

  return (
    <div className="w-full max-w-full h-full min-h-full flex flex-col overflow-clip">
      {!loading ? (
        <>
          <PageTitle
            title={currentDataSource?.displayName}
            subpage={true}
            breadcrumbs={[
              { label: 'Data sources', url: '/' },
              {
                label: `${currentDataSource?.displayName}`,
                url: `/datasources/${currentDataSource?.id}`,
              },
            ]}
          >
            {/* Data source switcher */}
            {dataSources && (
              <div className="flex gap-3 sm:mt-0 sm:flex-row sm:items-center">
                <Select
                  className="w-52"
                  options={dataSourceOptions}
                  formatOptionLabel={({ label, value }) => {
                    // assume custom if no connector is connected yet.
                    const displayConnectorType = getDisplayConnectorType(value);

                    const imgConfig = imgSrc.getOrDefault(displayConnectorType);

                    return (
                      <div className="flex items-center gap-x-2">
                        <Image
                          src={imgConfig.imgPath}
                          alt={imgConfig.alt}
                          {...imgConfig.dropdownStyle}
                        />
                        <span className="text-sm text-zinc-900">{label}</span>
                      </div>
                    );
                  }}
                  value={dataSourceOptions.find((opt) => opt.value.id === dataSourceId)}
                  onChange={(opt) => {
                    router.push(`/datasources/${opt.value.id}`);
                  }}
                />
                <Button
                  iconOnly
                  type="secondary"
                  icon="ArrowLeft"
                  disabled={currentDataSourceIdx! === 0}
                  href={`/datasources/${dataSources[currentDataSourceIdx! - 1]?.id}`}
                />
                <Button
                  iconOnly
                  type="secondary"
                  icon="ArrowRight"
                  disabled={currentDataSourceIdx === dataSources.length - 1}
                  href={`/datasources/${dataSources[currentDataSourceIdx! + 1]?.id}`}
                />
              </div>
            )}
          </PageTitle>
          <div className="flex flex-col px-8 pb-28 min-w-0">
            <DataSourceContent />
          </div>
        </>
      ) : (
        <Loading />
      )}
    </div>
  );
}
