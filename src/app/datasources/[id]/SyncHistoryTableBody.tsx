import { Connector } from '@/types';
import Dayjs from 'dayjs';
import { Badge, Loading } from '@/components';

type Props = {
  isS3Connector: boolean;
  connector: Connector;
  loading: boolean;
};

export default function SyncHistoryTableBody(props: Props) {
  const { isS3Connector, connector, loading } = props;

  const colSpan = isS3Connector ? 5 : 4;

  if (loading) {
    return (
      <tbody>
        <tr>
          <td className="h-full p-8" colSpan={colSpan}>
            <Loading />
          </td>
        </tr>
      </tbody>
    );
  }

  return (
    <tbody className="divide-y divide-gray-100 border-t border-gray-100">
      {connector.sync_histories.map((history, idx) => {
        const {
          syncId,
          syncStartDate,
          syncEndDate,
          status,
          recordsModified,
          computedFilesModified,
        } = history;

        const syncStartDayJs = syncStartDate ? Dayjs(syncStartDate).utc(true) : null;
        const syncEndDayJs = Dayjs(syncEndDate).utc(true);

        return (
          <tr key={`${syncId}${idx}`} className={`${(idx + 2) % 4 === 0 ? 'bg-zinc-50' : ''}`}>
            <td className="px-6 py-3 font-medium text-sm text-gray-900">
              <p>{syncStartDayJs && syncStartDayJs.format('MMMM D, YYYY [at] h:mm a')}</p>
            </td>
            <td className="h-full px-6 py-3 flex justify-start items-center">
              <Badge
                label={status === 'SUCCESSFUL' ? 'Success' : 'Failure'}
                type={status === 'SUCCESSFUL' ? 'success' : 'error'}
              />
            </td>
            {isS3Connector && (
              <td className="px-6 py-3 text-sm text-gray-500">
                <span>{computedFilesModified}</span>
              </td>
            )}
            <td className="px-6 py-3 text-sm text-gray-500">
              <span>{recordsModified.toLocaleString()}</span>
            </td>
            <td className="px-6 py-3 text-sm text-gray-500">
              <>{syncStartDayJs && syncStartDayJs.from(syncEndDayJs, true)}</>
            </td>
          </tr>
        );
      })}
    </tbody>
  );
}
