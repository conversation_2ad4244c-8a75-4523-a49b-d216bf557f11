import { useState, useMemo } from 'react';
import { isNil } from 'lodash';
import { Loading, InputText } from '@/components';
import { filterData } from '@/utils/data';
import { getDisplayConnectorType } from '@/utils/dataSources';
import DataTableRow from './DataTableRow';
import { useDataSourceStore } from '@/stores';
import { SourceModel } from '@/types';

type Props = {
  sourceModels: SourceModel[] | null;
  refreshing: boolean;
};

export default function DataTables(props: Props) {
  const { sourceModels, refreshing } = props;

  const { currentDataSource } = useDataSourceStore();

  const [searchTerm, setSearchTerm] = useState<string>();

  const filteredModels = useMemo(
    () => filterData<SourceModel>(sourceModels || [], { searchTerm, nameField: 'name' }),
    [sourceModels, searchTerm],
  );

  if (isNil(sourceModels) || refreshing || isNil(currentDataSource)) {
    return <Loading />;
  }

  if (!filteredModels.length) {
    return (
      <div className="h-full w-full flex justify-center items-center py-16">
        <p className="text-zinc-400">No models</p>
      </div>
    );
  }

  const displayConnectorType = getDisplayConnectorType(currentDataSource);

  return (
    <>
      <InputText
        icon={'SearchMd'}
        placeholder="Search"
        onChange={(e) => setSearchTerm(e.target.value)}
        value={searchTerm}
      />
      {filteredModels.map((table, idx) => (
        <DataTableRow
          key={`${table.name}${idx}`}
          connectorType={displayConnectorType}
          table={table}
        />
      ))}
    </>
  );
}
