import { useState, useMemo } from 'react';
import { isNil } from 'lodash';
import { Loading, InputText } from '@/components';
import { ModelSummary } from '@/types';
import { filterData } from '@/utils/data';
import DataTableRow from './DataTableRow';

type Props = {
  sourceModels?: ModelSummary[];
  sourceModelsLoading: boolean;
  connectorType: string;
  useSmartMapper?: boolean;
};

export default function DataTables(props: Props) {
  const { sourceModels, sourceModelsLoading, connectorType, useSmartMapper } = props;

  const [searchTerm, setSearchTerm] = useState<string>();

  const filteredModels = useMemo(
    () => filterData<ModelSummary>(sourceModels || [], { searchTerm, nameField: 'name' }),
    [sourceModels, searchTerm],
  );

  if (sourceModelsLoading || isNil(sourceModels)) {
    return <Loading />;
  }

  if (!filteredModels.length) {
    return (
      <div className="h-full w-full flex justify-center items-center py-16">
        <p className="text-zinc-400">No models</p>
      </div>
    );
  }

  return (
    <>
      <InputText
        icon={'SearchMd'}
        placeholder="Search"
        onChange={(e) => setSearchTerm(e.target.value)}
      />
      {filteredModels.map((table, idx) => (
        <DataTableRow
          key={`${table.name}${idx}`}
          connectorType={connectorType}
          table={table}
          useSmartMapper={useSmartMapper}
        />
      ))}
    </>
  );
}
