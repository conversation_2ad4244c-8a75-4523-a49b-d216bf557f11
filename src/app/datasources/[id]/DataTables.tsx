import { useState, useMemo } from 'react';
import { isNil } from 'lodash';
import { Loading, InputText } from '@/components';
import { filterData } from '@/utils/data';
import DataTableRow from './DataTableRow';
import { useDataSourceStore } from '@/stores';
import { SourceModel } from '@/types';

type Props = {
  useSmartMapper?: boolean;
  sourceModels: SourceModel[] | null;
};

export default function DataTables(props: Props) {
  const { useSmartMapper, sourceModels } = props;

  const { currentDataSource } = useDataSourceStore();

  const {
    connector: { connector_type },
  } = currentDataSource!;

  const [searchTerm, setSearchTerm] = useState<string>();

  const filteredModels = useMemo(
    () => filterData<SourceModel>(sourceModels || [], { searchTerm, nameField: 'name' }),
    [sourceModels, searchTerm],
  );

  if (isNil(sourceModels)) {
    return <Loading />;
  }

  if (!filteredModels.length) {
    return (
      <div className="h-full w-full flex justify-center items-center py-16">
        <p className="text-zinc-400">No models</p>
      </div>
    );
  }

  return (
    <>
      <InputText
        icon={'SearchMd'}
        placeholder="Search"
        onChange={(e) => setSearchTerm(e.target.value)}
      />
      {filteredModels.map((table, idx) => (
        <DataTableRow
          key={`${table.name}${idx}`}
          connectorType={connector_type}
          table={table}
          useSmartMapper={useSmartMapper}
        />
      ))}
    </>
  );
}
