import { useState, useCallback, useEffect } from 'react';
import { Connector, ConnectorDetail, SetStateFunction } from '@/types';
import { PaginationProps } from './page';
import { getConnectorHistory } from './utils';
import { ActionsMenu } from '@/components';
import { FilterLines } from '@/components/icons';
import SyncHistoryTableBody from './SyncHistoryTableBody';

type Props = {
  connector: Connector;
  currentPage: PaginationProps;
  onPageChange: SetStateFunction<PaginationProps>;
  onConnectorDetailChange: SetStateFunction<ConnectorDetail>;
  messageApi: any;
  loading: boolean;
  onLoading: SetStateFunction<boolean>;
};

export default function SyncHistory(props: Props) {
  const {
    connector,
    currentPage,
    onPageChange,
    onConnectorDetailChange,
    messageApi,
    loading,
    onLoading,
  } = props;

  const [includeEmpty, setIncludeEmpty] = useState<boolean>(false);

  const isS3Connector = connector.service === 's3';
  const { connector_id: connectorId } = connector;
  const { pageNo } = currentPage;

  const hasPrev = pageNo > 1;
  // For now, assume that there are more histories to query if the sync histories equals the max page size
  const hasMore = connector.sync_histories.length === 10;

  // refetch when include empty option changes
  const optRefetch = useCallback(async () => {
    onLoading(true);

    await getConnectorHistory({
      name: connectorId,
      pageNo,
      onConnectorDetailChange,
      includeEmpty,
    });

    onLoading(false);
  }, [connectorId, pageNo, includeEmpty, onConnectorDetailChange, onLoading]);

  useEffect(() => {
    optRefetch();
  }, [optRefetch]);

  const paginationNext = async () => {
    onLoading(true);

    const success = await getConnectorHistory({
      name: connectorId,
      pageNo: pageNo + 1,
      onConnectorDetailChange: onConnectorDetailChange,
      messageApi,
      includeEmpty,
    });

    if ((connector?.executed ?? 0) / (pageNo + 2) > 10 && success) {
      onPageChange({ pageNo: pageNo + 1, hasMore: true });
    } else {
      onPageChange({ pageNo: pageNo + 1, hasMore: false });
    }

    onLoading(false);
  };

  const paginationPrevious = async () => {
    const { pageNo } = currentPage;

    if (pageNo > 1) {
      onLoading(true);

      const _ = await getConnectorHistory({
        name: connectorId,
        pageNo: pageNo - 1,
        onConnectorDetailChange: onConnectorDetailChange,
        messageApi,
        includeEmpty,
      });

      if (_) {
        onPageChange({ pageNo: pageNo - 1, hasMore: true });
      }

      onLoading(false);
    }
  };

  return (
    <div className="flex justify-stretch space-x-10">
      <div className="flex flex-col space-y-3">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-medium text-zinc-900">Run history</h2>
          <ActionsMenu
            actionOptions={[
              {
                label: !!includeEmpty
                  ? 'Exclude syncs with no data changes'
                  : 'Include syncs with no data changes',
                icon: <FilterLines />,
                onClick: () => setIncludeEmpty((prev) => !prev),
              },
            ]}
          />
        </div>
        <div className="rounded-lg overflow-hidden">
          <table className="w-full table-fixed border-collapse bg-white text-left text-sm text-zinc-900">
            <thead className="bg-zinc-50">
              <tr>
                <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Runtime</th>
                <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Status</th>
                {isS3Connector && (
                  <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Number of Files</th>
                )}
                <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Records Modified</th>
                <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Time elapsed</th>
              </tr>
            </thead>
            <SyncHistoryTableBody
              isS3Connector={isS3Connector}
              connector={connector}
              loading={loading}
            />
          </table>
        </div>
        <div className="flex justify-end items-center space-x-4">
          {hasPrev && (
            <button
              onClick={() => paginationPrevious()}
              className="flex space-x-2 justify-center items-center bg-white px-4 py-2.5 border border-zinc-200 rounded-md shadow-sm text-sm text-zinc-900 font-semibold hover:bg-zinc-50 hover:border-zinc-50 active:bg-zinc-100 active:border-zinc-100"
            >
              Previous
            </button>
          )}
          {hasMore && (
            <button
              onClick={() => paginationNext()}
              className="flex space-x-2 justify-center items-center bg-white px-4 py-2.5 border border-zinc-200 rounded-md shadow-sm text-sm text-zinc-900 font-semibold hover:bg-zinc-50 hover:border-zinc-50 active:bg-zinc-100 active:border-zinc-100"
            >
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
