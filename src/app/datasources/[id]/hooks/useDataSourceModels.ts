import { useState, useEffect, useMemo, useCallback } from 'react';
import { useDictionaryStore, useDataSourceStore } from '@/stores';
import { useCustomer } from '@/hooks';

export default function useDataSourceModels(messageApi: any) {
  const { sourceModels, unmappedModelsByOrigin, loadUnmappedForOrigin, initializeSourceModels } =
    useDictionaryStore();
  const { currentDataSource } = useDataSourceStore();
  const { customerId } = useCustomer();

  const [includeUnmapped, setIncludeUnmapped] = useState(false);
  const [loading, setLoading] = useState(false);

  const origin = currentDataSource?.sourceModelKey;

  // Handle loading unmapped models when toggled
  useEffect(() => {
    if (!origin || !includeUnmapped) {
      return;
    }

    // Check if we need to load unmapped models
    if (!unmappedModelsByOrigin.has(origin)) {
      setLoading(true);
      loadUnmappedForOrigin({ customerId, origin }).finally(() => setLoading(false));
    }
  }, [origin, includeUnmapped, unmappedModelsByOrigin, loadUnmappedForOrigin, customerId]);

  // Compute visible models
  const visibleModels = useMemo(() => {
    if (!origin || !sourceModels) {
      return null;
    }

    const mappedModels = sourceModels.filter(
      (model) => model.origin.toLowerCase() === origin.toLowerCase(),
    );

    if (!includeUnmapped) {
      return mappedModels;
    }

    const unmappedModels = unmappedModelsByOrigin.get(origin) || [];

    return [...mappedModels, ...unmappedModels];
  }, [origin, sourceModels, includeUnmapped, unmappedModelsByOrigin]);

  const handleHardRefresh = useCallback(async () => {
    setLoading(true);

    try {
      // Invalidate cache and force refresh of source models
      await initializeSourceModels({ customerId, forceRefresh: true });

      messageApi.success('Data refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh data:', error);

      messageApi.error('Failed to refresh data');
    } finally {
      setLoading(false);
    }
  }, [initializeSourceModels, messageApi, customerId]);

  return {
    visibleModels,
    includeUnmapped,
    setIncludeUnmapped,
    loading,
    origin,
    handleHardRefresh,
  };
}
