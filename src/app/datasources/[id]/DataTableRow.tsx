import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Columns02 } from '@/components/icons';
import { imgSrc } from 'lib/source';
import { SourceModel } from '@/types';
import MappingIndicator from './MappingIndicator';

type Props = {
  connectorType: string;
  table: SourceModel;
};

export default function DataTableRow(props: Props) {
  const { connectorType, table } = props;

  const pathname = usePathname();

  const hasMappings = !!table.mappingCount;

  const imgConfig = imgSrc.getOrDefault(connectorType);

  return (
    <div className="w-full flex justify-between items-center gap-x-2">
      <div className="min-w-0 flex-1 flex flex-col gap-1 py-4">
        <div className="min-w-0 flex items-center gap-2">
          <div className="w-8 h-8 relative flex justify-center items-center">
            <Image
              src={imgConfig.imgPath}
              fill
              alt={imgConfig.alt}
              style={imgConfig.dropdownStyle?.style}
            />
          </div>
          <p className="text-xl font-medium truncate" title={table.name.toLowerCase()}>
            {table.name.toLowerCase()}
          </p>
        </div>
        <div className="flex gap-3 stroke-zinc-500 text-sm text-zinc-500">
          <MappingIndicator hasMappings={hasMappings} />
          {/* show if it's a new table
          <Badge type="new" label="New table" />
        */}
          <div className="flex justify-center items-center gap-1">
            <Columns02 className="w-4 h-4" />
            <p>{table.fieldCount?.toLocaleString() ?? 0} fields</p>
          </div>
          {/* TODO: We don't have row info yet
          <div className="flex justify-center items-center gap-1">
            <Rows02 className="w-4 h-4"/>
            <p>{table.rows.toLocaleString()} rows</p>
          </div>
      */}
        </div>
      </div>
      <Link
        href={`${pathname}/${table.name.toLowerCase()}/${table.id}`}
        className="text-indigo-700 font-medium"
      >
        View model
      </Link>
    </div>
  );
}
