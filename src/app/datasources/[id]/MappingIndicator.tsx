import { LinkBroken03, Check } from '@/components/icons';

type Props = {
  hasMappings: boolean;
};

export default function MappingIndicator(props: Props) {
  const { hasMappings } = props;

  let mappingDisplay = (
    <>
      <LinkBroken03 className="w-4 h-4" />
      <p>Unmapped</p>
    </>
  );

  if (hasMappings) {
    mappingDisplay = (
      <>
        <Check className="w-4 h-4 stroke-emerald-600" />
        <p className="text-emerald-600">Mapped</p>
      </>
    );
  }

  return <div className="flex justify-center items-center gap-1">{mappingDisplay}</div>;
}
