import { Connector, ConnectorDetail, SetStateFunction } from '@/types';
import { SourceDictionaryFieldFlatMapping } from './types';
import Dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { OverviewContents } from '@/components/Overview';
import { api } from '@/helpers/web';

Dayjs.extend(customParseFormat);

export function getConnectorOverview(connector: Connector): OverviewContents {
  const {
    connector_type,
    executed_at,
    sync_frequency,
    files_received_since_last_sync,
    error_count,
    failed_at,
  } = connector;

  const isDataOceans = connector_type === 'dataoceans';

  const nextSyncMessage = isDataOceans ? 'Next creation' : 'Next sync';
  const totalMessage = isDataOceans ? 'Total letters created' : 'Number of files last synced';
  const lastSyncMessage = isDataOceans ? 'Last created' : 'Last synced';

  const now = Dayjs().utc();
  const nextSync = Dayjs(executed_at).add(sync_frequency, 'minutes').utc();

  return [
    {
      header: 'Data sync frequency',
      content: Dayjs.duration(sync_frequency, 'minutes').humanize(),
      subContent: `${now > nextSync ? 'Last Run' : nextSyncMessage}: ${nextSync.fromNow()}`,
    },
    {
      header: totalMessage,
      content: files_received_since_last_sync,
      subContent: `${lastSyncMessage}: ${Dayjs(executed_at).fromNow()}`,
    },
    {
      header: 'Total errors',
      content: error_count,
      subContent: `Last error: ${failed_at ? Dayjs(failed_at).format('LL') : 'none'}`,
    },
  ];
}

type GetHistoryProps = {
  name: string;
  pageNo?: number;
  onConnectorDetailChange: SetStateFunction<ConnectorDetail>;
  messageApi?: any;
  includeEmpty?: boolean;
};

// boolean return for success
export async function getConnectorHistory(props: GetHistoryProps): Promise<boolean> {
  const { name, pageNo = 1, onConnectorDetailChange, messageApi, includeEmpty } = props;

  try {
    const { data } = await api.get(`/api/connector/${name}`, {
      pageNum: pageNo,
      pageSize: 10,
      includeEmpty,
    });

    onConnectorDetailChange({
      historyLoading: false,
      error: null,
      connector: data,
    });

    // no success message for initial load (also prevents infinite loop from using the useMessage hook in useEffect)
    if (messageApi) {
      messageApi.open({
        type: 'success',
        content: `page no ${pageNo}`,
      });
    }

    return true;
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    onConnectorDetailChange({
      historyLoading: false,
      error,
      connector: null,
    });

    if (messageApi) {
      messageApi.open({
        type: 'error',
        content: `failed to load page no ${pageNo}`,
      });
    }

    return false;
  }
}

// TO DO: is this unique?
export function genMappingId(mapping: SourceDictionaryFieldFlatMapping): string {
  const { field_name, id_target_model, contract_mapping } = mapping;
  const { target_field } = contract_mapping;

  return [field_name, id_target_model, target_field].filter(Boolean).join('-');
}
