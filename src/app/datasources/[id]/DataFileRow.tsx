import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import dayjs from 'dayjs';
import { File04, Calendar } from '@/components/icons';
import { imgSrc } from 'lib/source';
import { FileMetadata } from '@/types';
import { getFileSizeDisplay } from '@/utils/file';

type Props = {
  connectorType: string;
  file: FileMetadata;
};

export default function DataFileRow(props: Props) {
  const { connectorType, file } = props;

  const pathname = usePathname();

  const modifiedDate = dayjs(file.lastModified).format('LL');

  const filePath = file.key.replace('partners/generic/land/', '');

  return (
    <div className="flex justify-between items-center">
      <div className="flex flex-col gap-1 py-4">
        <div className="flex items-center gap-1">
          <div className="w-8 h-8 relative flex justify-center items-center">
            <Image
              src={imgSrc.getOrDefault(connectorType).imgPath}
              fill
              alt={`${connectorType} logo`}
            />
          </div>
          <p className="text-xl font-medium">{file.displayName}</p>
        </div>
        <div className="flex gap-3 stroke-zinc-500 text-sm text-zinc-500">
          <div className="flex justify-center items-center gap-1">
            <File04 className="w-4 h-4" />
            <p>{getFileSizeDisplay(file.size)}</p>
          </div>
          <div className="flex justify-center items-center gap-1">
            <Calendar className="w-4 h-4" />
            <p>Last modified on {modifiedDate}</p>
          </div>
        </div>
      </div>
      <Link
        href={`${pathname}/files/${encodeURIComponent(filePath)}`}
        className="text-indigo-700 font-medium"
      >
        View file
      </Link>
    </div>
  );
}
