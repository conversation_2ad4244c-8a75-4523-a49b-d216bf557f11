'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { MappingMatch, ToggleOptions } from '@/types';
import { PageContent, Loading } from '@/components';
import { useFeatureFlags, useCustomer } from '@/hooks';
import { api } from '@/helpers/web';
import StepRenderer from './StepRenderer';
import { useDataSourceStore } from '@/stores';

type Params = {
  id: string;
};

type Props = {
  params: Params;
};

const CONFIDENT_MATCH_THRESHOLD = 0.9;
const POSSIBLE_MATCH_THRESHOLD = 0;

export default function MapperHome(props: Props) {
  const { params } = props;
  const { id: dataSourceId } = params;

  const [confidentMappings, setConfidentMappings] = useState<MappingMatch[]>();
  const [possibleMappings, setPossibleMappings] = useState<MappingMatch[]>();
  const [step, setStep] = useState<number>(0);
  const [mappingsOptions, setMappingsOptions] = useState<ToggleOptions>({
    filterMappingsByProduct: { label: 'Filter by product', enabled: true },
  });

  const { customerId } = useCustomer();
  const { featureFlags } = useFeatureFlags();
  const { currentDataSource: dataSource } = useDataSourceStore();

  const smartMapperEnabled = featureFlags['can-run-smart-mapper']?.enabled;

  // fetch data source and AI mapping info
  useEffect(() => {
    if (!dataSource) {
      return;
    }

    const getData = async () => {
      const maps: MappingMatch[] = await api.get(
        `/api/dataSources/${dataSourceId}/getMapperResults`,
        {
          dataSourceKey: dataSource.sourceModelKey,
          customerId,
          filterByProduct: mappingsOptions.filterMappingsByProduct.enabled,
        },
      );

      const confident = maps.filter((mapping) => mapping.confidence >= CONFIDENT_MATCH_THRESHOLD);
      const possible = maps.filter(
        (mapping) =>
          mapping.confidence < CONFIDENT_MATCH_THRESHOLD &&
          mapping.confidence >= POSSIBLE_MATCH_THRESHOLD,
      );

      setConfidentMappings(confident);
      setPossibleMappings(possible);
    };

    getData();
  }, [dataSourceId, customerId, mappingsOptions, dataSource]);

  const handleNext = useCallback(() => {
    setStep((prev) => prev + 1);
  }, []);

  const autoMappings = useMemo(() => {
    const confident: MappingMatch[] =
      confidentMappings?.map((mapping) => ({ ...mapping, review_status: 'accepted' })) || [];

    const possible = possibleMappings || [];

    return [...confident, ...possible];
  }, [confidentMappings, possibleMappings]);

  if (!smartMapperEnabled) {
    return null;
  }

  if (!dataSource || !possibleMappings || !confidentMappings) {
    return <Loading />;
  }

  return (
    <PageContent>
      <StepRenderer
        onNext={handleNext}
        confidentMappings={confidentMappings}
        possibleMappings={possibleMappings}
        onPossibleMappingsChange={setPossibleMappings}
        autoMappings={autoMappings}
        step={step}
        mappingsOptions={mappingsOptions}
        onMappingsOptionsChange={setMappingsOptions}
      />
    </PageContent>
  );
}
