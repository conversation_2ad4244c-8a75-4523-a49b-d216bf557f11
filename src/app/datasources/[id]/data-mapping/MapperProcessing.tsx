import dynamic from 'next/dynamic';
import { Card, Loading } from '@/components';

type Props = {
  displayName: string;
};

const DynamicLottiePlayer = dynamic(() => import('@/components/LottiePlayer'), {
  loading: () => <Loading />,
  ssr: false,
});

export default function MapperProcessing(props: Props) {
  const { displayName } = props;

  return (
    <Card>
      <div className="w-1/2 h-1/2 mb-12 flex justify-between items-center">
        <DynamicLottiePlayer src="public/lottie/searching.json" />
      </div>
      <div className="flex flex-col w-2/3 items-center gap-y-2">
        <span className="text-zinc-700 text-2xl text-center">
          Let’s start by having our smart mapper automatically map the bulk of {displayName} fields
          to Gestalt’s data model.
        </span>
        <span className="text-zinc-500">This should only take a moment.</span>
      </div>
    </Card>
  );
}
