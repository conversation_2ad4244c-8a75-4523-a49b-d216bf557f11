import { useState, useMemo, useCallback } from 'react';
import { map, groupBy, orderBy } from 'lodash';
import { FilterValue, MappingMatch, SetStateFunction, HandleMapperFeedbackProps } from '@/types';
import { Button, PageTitle } from '@/components';
import { filterData } from '@/utils/data';
import { useCustomer } from '@/hooks';
import { api } from '@/helpers/web';
import MappingReviewProgress from './MappingReviewProgress';
import ModelMappingReview from './ModelMappingReview';
import { useDataSourceStore } from '@/stores';

export type Props = {
  possibleMappings: MappingMatch[];
  onPossibleMappingsChange: SetStateFunction<MappingMatch[]>;
  onNext: () => void;
};

const filterFields = {
  type: 'target_type',
  review: (row, val) => {
    const isReviewed = !!row.review_status && row.review_status !== 'none';

    return val === 'reviewed' ? isReviewed : !isReviewed;
  },
};

const reviewedStatuses = ['accepted', 'rejected'];

export default function MappingReview(props: Props) {
  const { possibleMappings, onPossibleMappingsChange, onNext } = props;

  const { currentDataSource: dataSource } = useDataSourceStore();
  const {
    sourceModelKey,
    displayName,
    id: dataSourceId,
    connector: { connector_type },
  } = dataSource!;

  const { customerId, customerKey } = useCustomer();

  const [searchTerm, setSearchTerm] = useState<string>();
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
    review: { label: 'All', value: 'all', filter: false },
  });
  const [submitting, setSubmitting] = useState<boolean>(false);

  // group mappings by model, sort them by model name
  const targetModelGroups = useMemo(() => {
    const filtered = filterData<MappingMatch>(possibleMappings, {
      searchTerm,
      nameField: 'target_field',
      filterConfig,
      filterFields,
    });

    const groupedByModel = groupBy(filtered, 'target_model_name');

    const modelMappingsArray = map(groupedByModel, (modelMappings, modelName) => ({
      modelName,
      modelMappings,
    }));

    return orderBy(modelMappingsArray, 'modelName');
  }, [possibleMappings, searchTerm, filterConfig]);

  const reviewedCount = possibleMappings.filter(
    ({ review_status }) => review_status && reviewedStatuses.includes(review_status),
  ).length;

  const totalCount = possibleMappings.length;

  const completedPercent = totalCount === 0 ? 100 : Math.floor((reviewedCount / totalCount) * 100);

  const handleDone = useCallback(async () => {
    const reviewedMappings = possibleMappings.filter((mapping) => !!mapping.review_status);

    if (!reviewedMappings.length) {
      onNext();

      return;
    }

    setSubmitting(true);

    const body: HandleMapperFeedbackProps = {
      reviewedMappings,
      dataSourceKey: sourceModelKey,
      customerId,
    };

    await api.workerPost({ service: 'mapper:handleMapperFeedback', body, customerKey });

    onNext();
    setSubmitting(false);
  }, [possibleMappings, sourceModelKey, customerId, customerKey, onNext]);

  return (
    <>
      <PageTitle
        title={displayName}
        subpage
        breadcrumbs={[
          { label: 'Data sources', url: '/' },
          {
            label: `${displayName}`,
            url: `/datasources/${dataSourceId}/data-mapping`,
          },
          {
            label: `Data Mapping`,
            url: `/datasources/${dataSourceId}/data-mapping`,
          },
        ]}
      />
      <div className="flex flex-col gap-y-6">
        <MappingReviewProgress
          filterConfig={filterConfig}
          onFilterConfigChange={setFilterConfig}
          searchTerm={searchTerm}
          onSearchTermChange={setSearchTerm}
          reviewedCount={reviewedCount}
          totalCount={totalCount}
          completedPercent={completedPercent}
        />
        {targetModelGroups.map(({ modelName, modelMappings }) => (
          <ModelMappingReview
            dataSourceName={displayName}
            connectorType={connector_type}
            modelName={modelName}
            modelMappings={modelMappings}
            key={modelName}
            onMappingsChange={onPossibleMappingsChange}
          />
        ))}
        <div className="flex justify-end items-center">
          <Button
            label={submitting ? 'Saving...' : 'Next: create new mappings'}
            disabled={submitting}
            onClick={handleDone}
          />
        </div>
      </div>
    </>
  );
}
