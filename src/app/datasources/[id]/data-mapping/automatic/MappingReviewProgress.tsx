import { FilterValue, SetStateFunction } from '@/types';
import { Card, Progress, InputText, DataFilter } from '@/components';
import { getFilterOptions } from '@/utils/data';

type Props = {
  filterConfig: Record<string, FilterValue>;
  onFilterConfigChange: SetStateFunction<Record<string, FilterValue>>;
  searchTerm?: string;
  onSearchTermChange: SetStateFunction<string>;
  reviewedCount: number;
  totalCount: number;
  completedPercent: number;
};

const filterOptions = getFilterOptions('mappingReview');

export default function MappingReviewProgress(props: Props) {
  const {
    filterConfig,
    onFilterConfigChange,
    searchTerm,
    onSearchTermChange,
    reviewedCount,
    totalCount,
    completedPercent,
  } = props;

  return (
    <Card>
      <div className="flex w-full items-center justify-between">
        <div className="flex flex-col w-3/5 gap-y-2">
          <span>
            You've reviewed {reviewedCount}/{totalCount} possible mappings
          </span>
          <Progress completed={completedPercent} />
        </div>
        <div className="flex w-2/5 gap-x-4 justify-end">
          <InputText
            icon="SearchMd"
            placeholder="Search columns"
            onChange={(e) => onSearchTermChange(e.target.value)}
            value={searchTerm}
          />
          <DataFilter
            filterConfig={filterConfig}
            onFilterConfigChange={onFilterConfigChange}
            filterOptions={filterOptions}
          />
        </div>
      </div>
    </Card>
  );
}
