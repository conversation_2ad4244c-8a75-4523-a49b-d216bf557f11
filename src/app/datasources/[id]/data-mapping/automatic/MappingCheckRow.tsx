import { findGeneralType } from '@/utils/data';
import { FieldBox, FieldTypeIcon, ReviewIcons } from '@/components';
import { reviewStyles } from '@/components/ReviewIcons';
import { MappingMatch, MatchReviewStatus, SetStateFunction } from '@/types';
import { updateArrayItem } from '@/utils/data';
import { compareObjectsByKeys } from '@/utils/object';

type MatchReviewProps = {
  currentReviewStatus?: MatchReviewStatus;
  newReviewStatus: MatchReviewStatus;
};

type Props = {
  mappingMatch: MappingMatch;
  sourceImgSrc: string;
  sourceImgStyle?: Record<string, any>;
  gestaltImgSrc: string;
  onMappingsChange: SetStateFunction<MappingMatch[]>;
  connectorDisplayName?: string;
};

export default function MappingCheckRow(props: Props) {
  const {
    mappingMatch,
    sourceImgSrc,
    sourceImgStyle,
    gestaltImgSrc,
    connectorDisplayName,
    onMappingsChange,
  } = props;

  const {
    source_field,
    target_field,
    target_type = '',
    source_model_name,
    confidence,
    review_status,
  } = mappingMatch;

  const genMatchType = findGeneralType(target_type, target_field);

  const rowClass = review_status ? reviewStyles[review_status].row : reviewStyles.none.row;

  // update single match in the matches array
  const handleMatchReview = (props: MatchReviewProps) => {
    const { currentReviewStatus, newReviewStatus } = props;

    const finalReviewStatus = currentReviewStatus === newReviewStatus ? 'none' : newReviewStatus;

    onMappingsChange((prev) => {
      const itemIdx = prev.findIndex((match) =>
        compareObjectsByKeys(match, mappingMatch, [
          'source_field',
          'target_field',
          'source_model_id',
          'target_model_id',
        ]),
      );

      return updateArrayItem<MappingMatch>({
        array: prev,
        itemIdx,
        newVals: { review_status: finalReviewStatus },
      });
    });
  };

  return (
    <tr className={rowClass}>
      <td className="table-cell">
        <FieldBox
          imgSrc={sourceImgSrc}
          imgAlt={`${connectorDisplayName} Logo`}
          imgStyle={sourceImgStyle}
          field={source_field}
          subField={`Model: ${source_model_name?.toLowerCase()}`}
        />
      </td>
      {/* bring back when we implement sidescrolling table */}
      {/* <td>
        <UnmappedIcon width="w-20" iconSize="5" />
      </td> */}
      <td className="table-cell">
        <FieldBox imgSrc={gestaltImgSrc} imgAlt="Gestalt" field={target_field} />
      </td>
      <td className="table-cell">
        <div className="h-full flex flex-row justify-start items-center gap-2 text-zinc-600 capitalize">
          <FieldTypeIcon type={genMatchType} />
          {genMatchType}
        </div>
      </td>
      <td className="table-cell">{Math.floor(confidence * 100)}%</td>
      <td className="table-cell">
        <ReviewIcons
          reviewStatus={review_status}
          onReview={({ currentReviewStatus, newReviewStatus }) => {
            handleMatchReview({
              currentReviewStatus,
              newReviewStatus,
            });
          }}
        />
      </td>
    </tr>
  );
}
