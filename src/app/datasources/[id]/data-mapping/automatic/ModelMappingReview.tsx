import { useState } from 'react';
import { GestaltTableIcon, ExpanderIcon } from '@/components';
import { MappingMatch, SetStateFunction, MatchReviewStatus } from '@/types';
import ModelMappingReviewTable from './ModelMappingReviewTable';

type Props = {
  dataSourceName: string;
  connectorType: string;
  modelName: string;
  modelMappings: MappingMatch[];
  onMappingsChange: SetStateFunction<MappingMatch[]>;
};

export default function ModelMappingReview(props: Props) {
  const { modelName, modelMappings, dataSourceName, connectorType, onMappingsChange } = props;
  const [expanded, setExpanded] = useState<boolean>(true);
  const [hovered, setHovered] = useState<boolean>(false);

  const handleReviewAll = (reviewStatus: MatchReviewStatus) => {
    onMappingsChange((prev) => {
      return [
        ...prev.map((mapping) => {
          if (mapping.target_model_name === modelName) {
            return { ...mapping, review_status: reviewStatus };
          }

          return { ...mapping };
        }),
      ];
    });
  };

  return (
    <div className="flex flex-col gap-y-4">
      <div
        className="flex text-2xl gap-x-2 items-center hover:cursor-pointer"
        onClick={() => {
          setExpanded(!expanded);
        }}
        onMouseEnter={() => {
          setHovered(true);
        }}
        onMouseLeave={() => {
          setHovered(false);
        }}
      >
        <ExpanderIcon expanded={expanded} hovered={hovered} />
        <span className="mr-2">Possible mappings to</span>
        <GestaltTableIcon />
        <span>{modelName.toLowerCase()}</span>
      </div>
      {expanded && (
        <ModelMappingReviewTable
          dataSourceName={dataSourceName}
          connectorType={connectorType}
          filteredMappings={modelMappings}
          handleReviewAll={handleReviewAll}
          onMappingsChange={onMappingsChange}
        />
      )}
    </div>
  );
}
