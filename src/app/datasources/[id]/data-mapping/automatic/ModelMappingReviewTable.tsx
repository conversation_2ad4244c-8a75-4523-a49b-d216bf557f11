import { useState, useMemo } from 'react';
import { imgSrc } from '@lib/source';
import { MappingMatch, MatchReviewStatus, SetStateFunction, SortConfig } from '@/types';
import { filterData } from '@/utils/data';
import MappingCheckHead from './MappingCheckHead';
import MappingCheckRow from './MappingCheckRow';

type Props = {
  dataSourceName: string;
  connectorType: string;
  filteredMappings: MappingMatch[];
  handleReviewAll: (status: MatchReviewStatus) => void;
  onMappingsChange: SetStateFunction<MappingMatch[]>;
};

export default function ModelMappingReviewTable(props: Props) {
  const { dataSourceName, connectorType, filteredMappings, handleReviewAll, onMappingsChange } =
    props;

  const [sortConfig, setSortConfig] = useState<SortConfig | null>({
    colName: 'score',
    keyAccessor: 'score',
    asc: false,
  });

  const sortedMappings = useMemo(
    () => filterData<MappingMatch>(filteredMappings, { sortConfig }),
    [filteredMappings, sortConfig],
  );

  const { imgPath: sourceImgPath, dropdownStyle: sourceImgStyle } =
    imgSrc.getOrDefault(connectorType);
  const gestaltImgSrc = imgSrc.getOrDefault('gestalt').imgPath;

  return (
    <div className="w-full">
      <table className="table">
        <MappingCheckHead
          connectorDisplayName={dataSourceName}
          sortConfig={sortConfig}
          onSortConfig={setSortConfig}
          handleReviewAll={handleReviewAll}
        />
        <tbody>
          {sortedMappings.map((mapping, idx) => (
            <MappingCheckRow
              key={`${mapping.source_field}${idx}`}
              mappingMatch={mapping}
              onMappingsChange={onMappingsChange}
              sourceImgSrc={sourceImgPath}
              sourceImgStyle={sourceImgStyle}
              gestaltImgSrc={gestaltImgSrc}
              connectorDisplayName={dataSourceName}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
}
