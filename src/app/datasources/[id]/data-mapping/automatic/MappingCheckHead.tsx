import { useState } from 'react';
import { SortArrow } from '@/components';
import { ThumbsUp, ThumbsDown } from '@/components/icons';
import { handleSort } from '@/utils/data';
import { SortConfig, MatchReviewStatus, SetStateFunction } from '@/types';

type Props = {
  connectorDisplayName?: string;
  sortConfig: SortConfig | null;
  onSortConfig: SetStateFunction<SortConfig>;
  handleReviewAll: (status: MatchReviewStatus) => void;
};

export default function MappingCheckHead(props: Props) {
  const { connectorDisplayName, sortConfig, onSortConfig, handleReviewAll } = props;

  const [headReviewStatus, setHeadReviewStatus] = useState<MatchReviewStatus>();

  const handleReview = (status: MatchReviewStatus) => {
    const newStatus = status === headReviewStatus ? 'none' : status;
    handleReviewAll(newStatus);

    setHeadReviewStatus(newStatus);
  };

  return (
    <thead className="table-head">
      <tr>
        <th
          className="table-head-cell"
          onClick={() =>
            handleSort({ colName: 'source_field', keyAccessor: 'source_field' }, onSortConfig)
          }
        >
          {connectorDisplayName} field
          <SortArrow colName="source_field" sortConfig={sortConfig} />
        </th>
        {/* bring back when we implement sidescrolling table */}
        {/* <th /> */}
        <th
          className="table-head-cell"
          onClick={() =>
            handleSort({ colName: 'target_field', keyAccessor: 'target_field' }, onSortConfig)
          }
        >
          Gestalt field
          <SortArrow colName="target_field" sortConfig={sortConfig} />
        </th>
        <th
          className="table-head-cell"
          onClick={() =>
            handleSort({ colName: 'target_type', keyAccessor: 'target_type' }, onSortConfig)
          }
        >
          Type
          <SortArrow colName="target_type" sortConfig={sortConfig} />
        </th>
        <th
          className="table-head-cell"
          onClick={() => handleSort({ colName: 'score', keyAccessor: 'score' }, onSortConfig)}
        >
          Match confidence
          <SortArrow colName="score" sortConfig={sortConfig} />
        </th>
        <th className="table-head-cell">
          <div className="flex justify-center items-center space-x-4">
            <div
              className="flex items-center stroke-indigo-700 text-indigo-700 space-x-2"
              onClick={() => handleReview('accepted')}
            >
              <ThumbsUp className="w-4 h-4" />
              <span>Accept all</span>
            </div>
            <div
              className="flex items-center stroke-indigo-700 text-indigo-700 space-x-2"
              onClick={() => handleReview('rejected')}
            >
              <ThumbsDown className="w-4 h-4" />
              <span>Reject all</span>
            </div>
          </div>
        </th>
      </tr>
    </thead>
  );
}
