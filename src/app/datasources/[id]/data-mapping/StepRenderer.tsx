import MappingInit, { Props as MappingInitProps } from './MappingInit';
import MappingReview, { Props as MappingReviewProps } from './automatic/MappingReview';
import MappingBuilder, { Props as MappingBuilderProps } from './manual/MappingBuilder';

type Props = MappingInitProps & MappingReviewProps & MappingBuilderProps & { step: number };

// This ensures only the required step function is called to avoid render effects within each
// (e.g. MappingReview api calls only happen on step 2)
export default function StepRenderer(props: Props) {
  const {
    onNext,
    confidentMappings,
    possibleMappings,
    sourceModelKey,
    connectorType,
    onPossibleMappingsChange,
    dataSource,
    autoMappings,
    step,
    mappingsOptions,
    onMappingsOptionsChange,
  } = props;

  switch (step) {
    case 0:
      return (
        <MappingInit
          dataSource={dataSource}
          onNext={onNext}
          confidentMappings={confidentMappings}
          possibleMappings={possibleMappings}
          mappingsOptions={mappingsOptions}
          onMappingsOptionsChange={onMappingsOptionsChange}
        />
      );
    case 1:
      return (
        <MappingReview
          dataSource={dataSource}
          sourceModelKey={sourceModelKey}
          connectorType={connectorType}
          possibleMappings={possibleMappings}
          onPossibleMappingsChange={onPossibleMappingsChange}
          onNext={onNext}
        />
      );
    case 2:
      return <MappingBuilder dataSource={dataSource} autoMappings={autoMappings} />;
    default:
      return null;
  }
}
