import dynamic from 'next/dynamic';
import MappingInit, { Props as MappingInitProps } from './MappingInit';
import MappingReview, { Props as MappingReviewProps } from './automatic/MappingReview';
import { Props as MappingBuilderProps } from './manual/MappingBuilder';
import { Loading } from '@/components';

type Props = MappingInitProps & MappingReviewProps & MappingBuilderProps & { step: number };

const DynamicMappingBuilder = dynamic(() => import('./manual/MappingBuilderContainer'), {
  loading: () => <Loading />,
  ssr: false,
});

// This ensures only the required step function is called to avoid render effects within each
// (e.g. MappingReview api calls only happen on step 2)
export default function StepRenderer(props: Props) {
  const {
    onNext,
    confidentMappings,
    possibleMappings,
    onPossibleMappingsChange,
    autoMappings,
    step,
    mappingsOptions,
    onMappingsOptionsChange,
  } = props;

  switch (step) {
    case 0:
      return (
        <MappingInit
          onNext={onNext}
          confidentMappings={confidentMappings}
          possibleMappings={possibleMappings}
          mappingsOptions={mappingsOptions}
          onMappingsOptionsChange={onMappingsOptionsChange}
        />
      );
    case 1:
      return (
        <MappingReview
          possibleMappings={possibleMappings}
          onPossibleMappingsChange={onPossibleMappingsChange}
          onNext={onNext}
        />
      );
    case 2:
      return <DynamicMappingBuilder autoMappings={autoMappings} />;
    default:
      return null;
  }
}
