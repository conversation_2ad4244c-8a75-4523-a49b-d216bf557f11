import { TransformConfig } from '@/types';
import { Dataflow03, Key01, Calculator, Concat, Cast, String } from '@/components/icons';

const separatorOpts = [
  {
    label: 'Space ( )',
    value: 'space',
  },
  {
    label: 'Pipe (|)',
    value: '|',
  },
  {
    label: 'Comma (,)',
    value: ',',
  },
  {
    label: 'Hyphen (-)',
    value: '-',
  },
  {
    label: 'Underscore (_)',
    value: '_',
  },
];

export const dataTypeOpts = [
  {
    label: 'Date',
    value: 'date',
  },
  {
    label: 'Timestamp',
    value: 'timestamp',
  },
  {
    label: 'Float',
    value: 'float',
  },
  {
    label: 'Integer',
    value: 'integer',
  },
  {
    label: 'String',
    value: 'string',
  },
  {
    label: 'Boolean',
    value: 'boolean',
  },
];

const caseOpts = [
  { label: 'Sentence case', value: 'sentence' },
  { label: 'Title Case', value: 'title' },
  { label: 'UPPER CASE', value: 'upper' },
  { label: 'lower case', value: 'lower' },
];

const mathOpts = [
  { label: 'Sum (+)', value: '+' },
  { label: 'Subtract (-)', value: '-' },
  { label: 'Multiply (x)', value: '*' },
  { label: 'Divide (/)', value: '/' },
];

export const transforms: TransformConfig[] = [
  // { key: 'ifThen', label: 'If / Then', Icon: IfThen },
  {
    key: 'coalesce',
    label: 'Coalesce',
    Icon: Dataflow03,
    config: {
      sourceFieldMin: 2,
      sourceFieldOpts: {
        type: 'order',
        label: 'Fallback order',
      },
    },
  },
  {
    key: 'uuid',
    label: 'UUID',
    Icon: Key01,
  },
  {
    key: 'math',
    label: 'Math',
    Icon: Calculator,
    config: {
      sourceFieldMin: 2,
      dropdownOpts: {
        optKey: 'mathOperator',
        opts: mathOpts,
        label: 'Operator',
      },
      sourceFieldOpts: {
        type: 'order',
        label: 'Operation order',
      },
      defaultOpts: {
        mathOperator: '+',
      },
    },
  },
  {
    key: 'concat',
    label: 'Concat',
    Icon: Concat,
    config: {
      sourceFieldMin: 2,
      sourceFieldOpts: {
        type: 'order',
        label: 'String order',
      },
      dropdownOpts: {
        label: 'Separator',
        opts: separatorOpts,
        optKey: 'sourceSeparator',
      },
      defaultOpts: {
        sourceSeparator: 'space',
      },
    },
  },
  {
    key: 'cast',
    label: 'Cast',
    Icon: Cast,
    config: {
      sourceFieldMin: 1,
      sourceFieldMax: 1,
      dropdownOpts: {
        label: 'Target data type',
        opts: dataTypeOpts,
        optKey: 'targetDataType',
      },
      defaultOpts: {
        targetDataType: 'string',
      },
    },
  },
  {
    key: 'case',
    label: 'Case',
    Icon: String,
    config: {
      sourceFieldMin: 1,
      sourceFieldMax: 1,
      dropdownOpts: {
        label: 'Target case',
        opts: caseOpts,
        optKey: 'targetCase',
      },
      defaultOpts: {
        targetCase: 'lower',
      },
    },
  },
];
