import { useMemo } from 'react';
import { v4 as uuidV4 } from 'uuid';
import { last } from 'lodash';
import { ModelJoin, SetStateFunction } from '@/types';
import { DraggableList } from '@/components';
import { Plus } from '@/components/icons';
import { reorderArray } from '@/utils/data';
import { DropResult } from '@hello-pangea/dnd';
import { useMappingStore } from '@/stores';
import { genLetterTitle } from '@/utils/string';

type Props = {
  targetModelId?: string;
  currentJoinProps?: ModelJoin | null;
  onJoinProps: SetStateFunction<ModelJoin | null>;
};

export default function JoinList(props: Props) {
  const { currentJoinProps, onJoinProps, targetModelId } = props;

  const { modelJoins, setModelJoins, addModelJoin } = useMappingStore();

  const listItems = useMemo(
    () =>
      modelJoins?.map((join) => {
        const selected = join.id === currentJoinProps?.id;

        const component = (
          <div
            key={join.id}
            className={`flex items-center rounded-md w-full px-2 py-1 ${
              selected && 'bg-indigo-600'
            }`}
            onMouseUp={() => {
              onJoinProps(join);
            }}
          >
            <span className={selected ? 'text-white' : ''}>Join {genLetterTitle(join.order!)}</span>
          </div>
        );

        return { key: join.id!, item: component };
      }),
    [modelJoins, currentJoinProps, onJoinProps],
  );

  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    if (!destination) {
      return;
    }

    if (source.index === destination.index && source.droppableId === destination.droppableId) {
      return;
    }

    const newOrder = reorderArray(modelJoins, source.index, destination.index);

    setModelJoins(newOrder);
  };

  return (
    <div className="flex flex-col h-full bg-zinc-100 justify-between rounded-s-lg">
      <div className="flex flex-col p-4 h-full">
        <h1 className="text-lg text-zinc-900">Joins</h1>
        <div className="flex flex-col pt-6 gap-y-2">
          <DraggableList id={targetModelId!} items={listItems || []} onDragEnd={handleDragEnd} />
        </div>
      </div>
      <button
        onClick={() => {
          const newJoin = {
            id: uuidV4(),
            order: modelJoins?.length ? last(modelJoins)!.order! + 1 : 0,
          };

          addModelJoin(newJoin);
          onJoinProps(newJoin);
        }}
      >
        <div className="flex items-center gap-x-2 p-4">
          <Plus className="stroke-indigo-700" />
          <span className="text-indigo-700">Create join</span>
        </div>
      </button>
    </div>
  );
}
