import { isEqual } from 'lodash';
import { ModelJoin } from '@/types';

export function getCanSave(joins?: ModelJoin[], currentJoinProps?: ModelJoin | null): boolean {
  const validated =
    currentJoinProps?.sourceModelName &&
    currentJoinProps?.joinModelName &&
    currentJoinProps?.sourceModelField &&
    currentJoinProps?.joinModelField;

  if (!validated) {
    return false;
  }

  // no joins yet, so constructing a new join
  if (!joins?.length) {
    return true;
  }

  const currentJoin = joins.find((join) => join.id === currentJoinProps.id);

  // if not editing an existing join, can save
  if (!currentJoin) {
    return true;
  }

  const dirty = !isEqual(currentJoin, currentJoinProps);

  return dirty;
}
