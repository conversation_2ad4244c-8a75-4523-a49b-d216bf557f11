import { useState, useEffect, useMemo } from 'react';
import { Select, InputText } from '@/components';
import { Option, ModelJoinOptionValue, SourceModelDictionaryField } from '@/types';
import { filterData } from '@/utils/data';
import { imgSrc } from 'lib/source';
import JoinFieldB<PERSON> from '../JoinFieldBox';
import { omit } from 'lodash';
import { useDataSourceStore, useDictionaryStore } from '@/stores';

type Props = {
  currentModelId?: string;
  onCurrentModelChange: (val: ModelJoinOptionValue) => void;
  currentField?: string | null;
  onCurrentFieldChange: (val: string | null) => void;
};

export default function JoinModelList(props: Props) {
  const { currentModelId, onCurrentModelChange, currentField, onCurrentFieldChange } = props;

  const [searchTerm, setSearchTerm] = useState<string>();
  const [selectedModel, setSelectedModel] = useState<Option<ModelJoinOptionValue> | null>();

  const { currentDataSource: dataSource } = useDataSourceStore();
  const { sourceModels } = useDictionaryStore();

  const sourceModelOpts = useMemo(() => {
    const opts = sourceModels?.map((model) => ({
      label: model.name,
      value: omit(model, 'dictionary'),
    }));

    return opts || [];
  }, [sourceModels]);

  useEffect(() => {
    if (currentModelId) {
      setSelectedModel(sourceModelOpts.find((opt) => opt.value.id === currentModelId));
    } else {
      setSelectedModel(null);
    }
  }, [currentModelId, sourceModelOpts]);

  const displayFields = useMemo(() => {
    if (!currentModelId) {
      return [];
    }

    const modelFields =
      sourceModels?.find((model) => model.id === currentModelId)?.dictionary || [];

    return filterData<SourceModelDictionaryField>(modelFields, {
      searchTerm,
      nameField: 'field_name',
    });
  }, [sourceModels, searchTerm, currentModelId]);

  const { imgPath, alt } = imgSrc.getOrDefault(dataSource!.sourceModelKey);

  const handleFieldSelect = (field: SourceModelDictionaryField, selected: boolean) => {
    if (selected) {
      onCurrentFieldChange(null);

      return;
    }

    onCurrentFieldChange(field.field_name);
  };

  return (
    <div className="flex flex-col rounded-md border border-zinc-200 bg-gray-100 h-[360px] w-full">
      <div>
        <Select
          options={sourceModelOpts}
          formatOptionLabel={({ label, value }) => (
            // Add full warehouse name on hover
            <div className="w-full" title={value.warehouseName}>
              <span>{label}</span>
            </div>
          )}
          value={selectedModel}
          className="w-full"
          onChange={(opt) => {
            onCurrentModelChange(opt.value);
          }}
        />
      </div>
      <div className="flex flex-col p-4 gap-y-4 h-full overflow-scroll">
        <InputText
          placeholder="Search fields"
          onChange={(e) => {
            setSearchTerm(e.target.value);
          }}
          value={searchTerm}
        />
        <div className="flex flex-col gap-y-2">
          {displayFields.map((field, idx) => {
            const selected = currentField === field.field_name;

            return (
              <button
                onClick={() => {
                  handleFieldSelect(field, selected);
                }}
                key={`${field.field_name}${idx}`}
              >
                <JoinFieldBox imgSrc={imgPath} imgAlt={alt} field={field} selected={selected} />
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
}
