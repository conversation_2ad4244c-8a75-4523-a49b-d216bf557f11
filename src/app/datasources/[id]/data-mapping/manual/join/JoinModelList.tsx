import { useState, useEffect, useMemo } from 'react';
import { Select, InputText } from '@/components';
import { SourceFieldsByModel, SourceField, Option, ModelJoinOptionValue } from '@/types';
import { filterData } from '@/utils/data';
import { imgSrc } from 'lib/source';
import JoinFieldBox from '../JoinFieldBox';
import { omit } from 'lodash';

type Props = {
  sourceFieldsByModel: SourceFieldsByModel;
  sourceModelKey: string;
  currentModelId?: string;
  onCurrentModelChange: (val: ModelJoinOptionValue) => void;
  currentField?: string | null;
  onCurrentFieldChange: (val: string | null) => void;
};

export default function JoinModelList(props: Props) {
  const {
    sourceFieldsByModel,
    sourceModelKey,
    currentModelId,
    onCurrentModelChange,
    currentField,
    onCurrentFieldChange,
  } = props;

  const [searchTerm, setSearchTerm] = useState<string>();
  const [selectedModel, setSelectedModel] = useState<Option<ModelJoinOptionValue> | null>();

  const sourceModelOpts = useMemo(
    () =>
      sourceFieldsByModel.map((model) => ({
        label: model.modelName,
        value: omit(model, 'fields'),
      })),
    [sourceFieldsByModel],
  );

  useEffect(() => {
    if (currentModelId) {
      setSelectedModel(sourceModelOpts.find((opt) => opt.value.modelId === currentModelId));
    } else {
      setSelectedModel(null);
    }
  }, [currentModelId, sourceModelOpts]);

  const displayFields = useMemo(() => {
    if (!currentModelId) {
      return [];
    }

    const modelFields =
      sourceFieldsByModel.find(({ modelId }) => modelId === currentModelId)?.fields || [];

    return filterData<SourceField>(modelFields, { searchTerm, nameField: 'field_name' });
  }, [sourceFieldsByModel, searchTerm, currentModelId]);

  const { imgPath, alt } = imgSrc.getOrDefault(sourceModelKey);

  const handleFieldSelect = (field: SourceField, selected: boolean) => {
    if (selected) {
      onCurrentFieldChange(null);

      return;
    }

    onCurrentFieldChange(field.field_name);
  };

  return (
    <div className="flex flex-col rounded-md border border-zinc-200 bg-gray-100 h-[360px] w-full">
      <div>
        <Select
          options={sourceModelOpts}
          formatOptionLabel={({ label, value }) => (
            // Add full dbt name on hover
            <div className="w-full" title={value.dbtName}>
              <span>{label}</span>
            </div>
          )}
          value={selectedModel}
          className="w-full"
          onChange={(opt) => {
            onCurrentModelChange(opt.value);
          }}
        />
      </div>
      <div className="flex flex-col p-4 gap-y-4 h-full overflow-scroll">
        <InputText
          placeholder="Search fields"
          onChange={(e) => {
            setSearchTerm(e.target.value);
          }}
        />
        <div className="flex flex-col gap-y-2">
          {displayFields.map((field, idx) => {
            const selected = currentField === field.field_name;

            return (
              <button
                onClick={() => {
                  handleFieldSelect(field, selected);
                }}
                key={`${field.field_name}${idx}`}
              >
                <JoinFieldBox imgSrc={imgPath} imgAlt={alt} field={field} selected={selected} />
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
}
