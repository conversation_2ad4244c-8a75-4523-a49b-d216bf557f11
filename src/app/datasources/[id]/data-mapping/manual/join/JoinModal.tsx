import { useState } from 'react';
import { SourceFieldsByModel, ModelJoin } from '@/types';
import { Modal } from '@/components';
import JoinList from './JoinList';
import JoinCreate from './JoinCreate';
import { useMappingStore } from '@/stores';

type Props = {
  sourceFieldsByModel: SourceFieldsByModel;
  sourceModelKey: string;
  targetModelId?: string;
};

export default function JoinModal(props: Props) {
  const { sourceFieldsByModel, sourceModelKey, targetModelId } = props;

  const { joinModalOpen, setJoinModalOpen } = useMappingStore();

  const [currentJoinProps, setCurrentJoinProps] = useState<ModelJoin | null>();

  return (
    <Modal open={joinModalOpen} setIsOpen={setJoinModalOpen} noPadding width="w-[1000px]" noTitle>
      <div className="flex">
        <div className="w-1/4 min-w-1/4">
          <JoinList
            targetModelId={targetModelId}
            currentJoinProps={currentJoinProps}
            onJoinProps={setCurrentJoinProps}
          />
        </div>
        <div className="w-3/4 min-w-3/4">
          <JoinCreate
            sourceFieldsByModel={sourceFieldsByModel}
            sourceModelKey={sourceModelKey}
            currentJoinProps={currentJoinProps}
            onJoinProps={setCurrentJoinProps}
          />
        </div>
      </div>
    </Modal>
  );
}
