import { useState } from 'react';
import { ModelJoin } from '@/types';
import { Modal } from '@/components';
import JoinList from './JoinList';
import JoinCreate from './JoinCreate';
import { useMappingStore } from '@/stores';

type Props = {
  targetModelId?: string;
};

export default function JoinModal(props: Props) {
  const { targetModelId } = props;

  const { joinModalOpen, setJoinModalOpen } = useMappingStore();

  const [currentJoinProps, setCurrentJoinProps] = useState<ModelJoin | null>();

  return (
    <Modal open={joinModalOpen} setIsOpen={setJoinModalOpen} noPadding width="w-[1000px]" noTitle>
      <div className="flex">
        <div className="w-1/4 min-w-1/4">
          <JoinList
            targetModelId={targetModelId}
            currentJoinProps={currentJoinProps}
            onJoinProps={setCurrentJoinProps}
          />
        </div>
        <div className="w-3/4 min-w-3/4">
          <JoinCreate currentJoinProps={currentJoinProps} onJoinProps={setCurrentJoinProps} />
        </div>
      </div>
    </Modal>
  );
}
