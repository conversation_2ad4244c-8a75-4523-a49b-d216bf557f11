import { v4 as uuidV4 } from 'uuid';
import { isNil, last } from 'lodash';
import { SetStateFunction, ModelJoin, ModelJoinOptionValue } from '@/types';
import JoinModelList from './JoinModelList';
import { Button } from '@/components';
import { Trash01, XClose } from '@/components/icons';
import { getCanSave } from './utils';
import { useMappingStore } from '@/stores';

type Props = {
  currentJoinProps?: ModelJoin | null;
  onJoinProps: SetStateFunction<ModelJoin | null>;
};

export default function JoinCreate(props: Props) {
  const { currentJoinProps, onJoinProps } = props;

  const { modelJoins, setJoinModalOpen, editModelJoin, addModelJoin, deleteModelJoin } =
    useMappingStore();

  const handleSave = () => {
    const currentJoinIdx = modelJoins?.findIndex((join) => join.id === currentJoinProps?.id);

    if (!isNil(currentJoinIdx) && currentJoinIdx >= 0) {
      editModelJoin(currentJoinIdx, currentJoinProps as ModelJoin);

      return;
    }

    const newJoinId = uuidV4();
    const newOrder = modelJoins?.length ? last(modelJoins)!.order! + 1 : 0;

    const newJoin = {
      id: newJoinId,
      order: newOrder,
      ...currentJoinProps,
    };

    addModelJoin(newJoin);
    onJoinProps((prev) => ({ id: newJoinId, ...prev }));
  };

  const handleDelete = () => {
    if (!currentJoinProps?.id) {
      return;
    }

    deleteModelJoin(currentJoinProps.id);

    onJoinProps(null);
  };

  const canSave = getCanSave(modelJoins, currentJoinProps);

  return (
    <div className="flex flex-col p-4 gap-y-6">
      <div className="flex w-full justify-between">
        <h1 className="text-lg text-zinc-900">Create join</h1>
        <button onClick={() => setJoinModalOpen(false)}>
          <XClose className="stroke-zinc-900" />
        </button>
      </div>
      <div className="flex justify-between gap-x-6">
        <JoinModelList
          currentModelId={currentJoinProps?.sourceModelId}
          onCurrentModelChange={(val: ModelJoinOptionValue) => {
            onJoinProps((prev) => ({
              ...prev,
              sourceModelId: val.id,
              sourceModelName: val.name,
              sourceModelDbtName: val.dbtName,
            }));
          }}
          currentField={currentJoinProps?.sourceModelField}
          onCurrentFieldChange={(fieldName: string) => {
            onJoinProps((prev) => ({ ...prev, sourceModelField: fieldName }));
          }}
        />
        <JoinModelList
          currentModelId={currentJoinProps?.joinModelId}
          onCurrentModelChange={(val: ModelJoinOptionValue) => {
            onJoinProps((prev) => ({
              ...prev,
              joinModelId: val.id,
              joinModelName: val.name,
              joinModelDbtName: val.dbtName,
            }));
          }}
          currentField={currentJoinProps?.joinModelField}
          onCurrentFieldChange={(fieldName: string) => {
            onJoinProps((prev) => ({ ...prev, joinModelField: fieldName }));
          }}
        />
      </div>
      <div className="flex items-center justify-between">
        <Button
          type="secondary"
          theme="critical"
          label="Delete join"
          Icon={Trash01}
          disabled={!currentJoinProps?.id}
          onClick={handleDelete}
        />
        <div className="flex gap-x-2 items-center">
          <Button
            label="Cancel"
            type="secondary"
            onClick={() => {
              setJoinModalOpen(false);
            }}
          />
          <Button label="Save join" disabled={!canSave} onClick={handleSave} />
        </div>
      </div>
    </div>
  );
}
