import { useState, useMemo } from 'react';
import { BaseField, SourceField, FilterValue } from '@/types';
import { imgSrc } from 'lib/source';
import { filterData } from '@/utils/data';
import MappingFieldBox from './MappingFieldBox';
import { filterFields } from './FieldTray';
import { ExpanderIcon } from '@/components';

type Props = {
  modelName: string;
  dbtName: string;
  fields: SourceField[];
  sourceModelKey: string;
  onSelect: (field: BaseField) => void;
  searchTerm?: string;
  filterConfig: Record<string, FilterValue>;
};

export default function SourceFieldModelList(props: Props) {
  const { modelName, dbtName, fields, sourceModelKey, onSelect, searchTerm, filterConfig } = props;

  const sourceImgConfig = imgSrc.getOrDefault(sourceModelKey);
  const { imgPath, dropdownStyle } = sourceImgConfig;

  const [expanded, setExpanded] = useState<boolean>(false);
  const [hovered, setHovered] = useState<boolean>(false);

  const filteredFields = useMemo(
    () =>
      filterData<SourceField>(fields, {
        searchTerm,
        nameField: 'field_name',
        filterConfig,
        filterFields,
      }),
    [fields, searchTerm, filterConfig],
  );

  if (!filteredFields.length) {
    return null;
  }

  return (
    <div className="flex flex-col gap-y-4">
      <div
        className="flex items-center gap-x-2 w-full hover:cursor-pointer"
        onClick={() => {
          setExpanded(!expanded);
        }}
        onMouseEnter={() => {
          setHovered(true);
        }}
        onMouseLeave={() => {
          setHovered(false);
        }}
      >
        <ExpanderIcon
          expanded={expanded}
          hovered={hovered}
          style={{
            iconColor: 'stroke-zinc-600',
            iconHeight: 'h-5',
            iconWidth: 'w-5',
            boxHeight: 'h-6',
            boxWidth: 'w-6',
          }}
        />
        <span className="text-sm text-zinc-600" title={dbtName}>
          {modelName}
        </span>
      </div>
      {expanded && (
        <div className="flex flex-col gap-y-1">
          {filteredFields.map((field, idx) => (
            <div className="hover:cursor-pointer" key={`${field.field_name}${idx}`}>
              <MappingFieldBox
                imgSrc={imgPath}
                imgAlt={field.field_name}
                imgStyle={dropdownStyle}
                field={field}
                onSelect={onSelect}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
