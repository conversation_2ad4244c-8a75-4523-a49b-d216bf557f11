import { TargetModel } from '@/types';
import { NodeCanvas } from '@/components';
import SourceFieldTray from './SourceFieldTray';
import TargetFieldTray from './TargetFieldTray';
import TransformList from './transform/TransformList';
import NodeModal from './NodeModal';
import JoinModal from './join/JoinModal';
import { nodeTypes, edgeStyle } from './MappingBuilder';

type Props = {
  currentTargetModel: TargetModel;
};

export default function DualTrayBuilder(props: Props) {
  const { currentTargetModel } = props;

  return (
    <>
      <div className="flex gap-x-6 h-full">
        <SourceFieldTray />
        <div className="relative w-full h-full">
          <NodeCanvas nodeTypes={nodeTypes} edgeStyle={edgeStyle} />
          <div className="absolute top-0 left-0">
            <TransformList />
          </div>
        </div>
        <TargetFieldTray targetModel={currentTargetModel} />
      </div>
      <NodeModal />
      <JoinModal targetModelId={currentTargetModel?.id} />
    </>
  );
}
