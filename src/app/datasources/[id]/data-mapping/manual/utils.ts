import { Node, Edge } from '@xyflow/react';
import {
  BaseField,
  TransformNode,
  GeneralNode,
  MappingNode,
  StaticNode,
  BaseNodeProps,
  NodeKind,
  TransformConfig,
  DeliveryNodeData,
  DeliveryNode,
  DeliveryEdge,
  MappingMatch,
} from '@/types';
import { max, maxBy, findKey, pick, groupBy, flatten, map } from 'lodash';
import {
  SOURCE_NODE_X_POS,
  TARGET_NODE_X_POS,
  TRANSFORM_NODE_X_POS,
  VERT_NODE_SPACE,
  START_Y_POS,
  edgeStyle,
} from './MappingBuilder';
import { transforms } from '../constants';
import { getNormalizedFieldType } from '@/utils/data';
import { useMappingStore } from '@/stores';

type NodeCol = 'left' | 'middle' | 'right';

const nodeKindCols: Record<NodeCol, NodeKind[]> = {
  left: ['source', 'static'],
  middle: ['transform'],
  right: ['target'],
};

function getNodeCol(nodeKind: NodeKind): NodeCol {
  return findKey(nodeKindCols, (kinds) => kinds.includes(nodeKind)) as NodeCol;
}

type GenPositionProps = {
  currentNodes: Node<BaseNodeProps>[];
  nodeKind: NodeKind;
};

type Position = {
  x: number;
  y: number;
};

export function genNodePosition(props: GenPositionProps): Position {
  const { currentNodes, nodeKind } = props;

  const nodeCol = getNodeCol(nodeKind);

  const colNodes = currentNodes.filter((node) => getNodeCol(node.data.value.nodeKind) === nodeCol);

  const maxYNode = max(colNodes.map((node) => node.position.y)) ?? START_Y_POS;

  let xPos = 0;

  switch (nodeCol) {
    case 'left':
      xPos = SOURCE_NODE_X_POS;
      break;
    case 'middle':
      xPos = TRANSFORM_NODE_X_POS;
      break;
    case 'right':
      xPos = TARGET_NODE_X_POS;
      break;
  }

  return { x: xPos, y: maxYNode + VERT_NODE_SPACE };
}

export function genNodeId(currentNodes: Node[]): string {
  const currentMax = maxBy(currentNodes, (node) => parseInt(node.id));

  const currentMaxId = currentMax ? parseInt(currentMax.id) : 0;

  const newIdInt = currentMaxId + 1;

  return newIdInt.toString();
}

type ShiftClickProps = {
  nodeId: string;
  nodeSelected: boolean;
  currentNodes: Node<BaseNodeProps>[];
  addEdges: (newEdges: Edge[]) => void;
};

export function genShiftClickEdges(props: ShiftClickProps): void {
  const { nodeId, nodeSelected, currentNodes, addEdges } = props;

  const currentSelectedNodes = currentNodes.filter((node) => node.selected);

  if (nodeSelected || !currentSelectedNodes?.length) {
    return;
  }

  const newEdges = autoGenEdges({ selectedNodes: currentSelectedNodes, targetNodeId: nodeId });

  addEdges(newEdges);
}

type GenMappingNodeProps = {
  nodesAtGeneration: Node<BaseNodeProps>[];
  nodeKind: NodeKind;
  newField: BaseField;
  sourceModelKey: string;
};

// maybe not needed if using Dagre to handle positioning
export function genMappingNode(props: GenMappingNodeProps): MappingNode {
  const { nodesAtGeneration, nodeKind, sourceModelKey, newField } = props;

  return {
    id: genNodeId(nodesAtGeneration),
    type: 'mapping',
    data: {
      value: { field: newField, nodeKind, sourceModelKey },
    },
    position: genNodePosition({ currentNodes: nodesAtGeneration, nodeKind }),
  };
}

type GetSourceNodeProps = {
  nodeId: string;
  currentNodes: Node[];
  currentEdges: Edge[];
};

export function getSourceNodes(props: GetSourceNodeProps): Node[] {
  const { nodeId, currentNodes, currentEdges } = props;

  const sourceNodeIds = currentEdges
    .filter((edge) => edge.target === nodeId)
    .map((edge) => edge.source);

  return currentNodes.filter((node) => sourceNodeIds.includes(node.id));
}

type GenTransformNodeProps = {
  transformKey: string;
};

export function genTransformNode(props: GenTransformNodeProps): TransformNode {
  const { transformKey } = props;
  const { nodes: nodesAtGeneration } = useMappingStore.getState();

  const nodeKind = 'transform';

  const transform = transforms.find((t) => t.key === transformKey);

  return {
    id: genNodeId(nodesAtGeneration),
    type: nodeKind,
    data: {
      value: {
        transformKey,
        nodeKind,
        opts: transform?.config?.defaultOpts,
      },
    },
    position: genNodePosition({ currentNodes: nodesAtGeneration, nodeKind }),
  };
}

export function genStaticNode(): StaticNode {
  const { nodes: nodesAtGeneration } = useMappingStore.getState();

  const nodeKind = 'static';

  return {
    id: genNodeId(nodesAtGeneration),
    type: nodeKind,
    data: { value: { nodeKind } },
    position: genNodePosition({ currentNodes: nodesAtGeneration, nodeKind }),
  };
}

export function getNodeConnectionStatus(nodeId: string, edges: Edge[]): boolean {
  return !!edges.find((edge) => edge.source === nodeId || edge.target === nodeId);
}

export function genEdgeId(source: string, target: string): string {
  return `xy-edge__${source}-${target}`;
}

type GenEdgesProps = {
  selectedNodes: Node[];
  targetNodeId: string;
};

export function autoGenEdges(props: GenEdgesProps): Edge[] {
  const { selectedNodes, targetNodeId } = props;

  return selectedNodes.map((node) => ({
    source: node.id,
    target: targetNodeId,
    id: genEdgeId(node.id, targetNodeId),
    type: edgeStyle.type,
    style: edgeStyle.style,
  }));
}

type TransformAlertProps = {
  transform: TransformConfig;
  sourceNodes: GeneralNode[];
};

type TransformAlertStatus = {
  minFieldAlertStatus: boolean;
  maxFieldAlertStatus: boolean;
};

export function getTransformAlertStatus(props: TransformAlertProps): TransformAlertStatus {
  const { transform, sourceNodes } = props;
  const { config } = transform;

  if (!config) {
    return { minFieldAlertStatus: false, maxFieldAlertStatus: false };
  }

  const { sourceFieldMin, sourceFieldMax } = config;

  const minFieldAlertStatus = !!sourceFieldMin && sourceNodes.length < sourceFieldMin;
  const maxFieldAlertStatus = !!sourceFieldMax && sourceNodes.length > sourceFieldMax;

  return { minFieldAlertStatus, maxFieldAlertStatus };
}

export function genDeliveryNode(node: GeneralNode): DeliveryNode {
  const {
    id,
    data: { value },
  } = node;

  return {
    id,
    nodeKind: value.nodeKind,
    data: pick(value, [
      'field',
      'sourceModelKey',
      'transformKey',
      'opts',
      'dataType',
      'value',
    ]) as DeliveryNodeData,
  };
}

export function genDeliveryEdge(edge: Edge): DeliveryEdge {
  return pick(edge, ['source', 'target', 'id']);
}

export function filterAutoMappings(autoMappings: MappingMatch[]): MappingMatch[] {
  const autoGroupedByTarget = groupBy(autoMappings, 'target_field');

  return flatten(
    map(autoGroupedByTarget, (mappings) => maxBy(mappings, 'confidence') || mappings[0]),
  );
}

type GenConnectionsProps = {
  autoMappings: MappingMatch[];
  sourceModelKey: string;
};

type ConnectionsResult = {
  nodes: MappingNode[];
  edges: Edge[];
};

export function genConnectionsFromAutoMappings(props: GenConnectionsProps): ConnectionsResult {
  const { autoMappings, sourceModelKey } = props;

  // AI mappings are one-to-one and contain multiple matches for each target field. This takes the highest confidence
  // for each target field.
  const filtered = filterAutoMappings(autoMappings);

  const nodes: MappingNode[] = [];
  const edges: Edge[] = [];

  filtered.forEach((mapping) => {
    const {
      source_model_name,
      source_model_id,
      source_model_dbt_name,
      target_model_name,
      target_model_id,
      target_field,
      source_field,
      source_type,
      target_type,
    } = mapping;

    const sourceField = {
      model_id: source_model_id,
      model_name: source_model_name,
      dbt_name: source_model_dbt_name,
      field_name: source_field,
      field_type: source_type,
      gen_field_type: getNormalizedFieldType(source_type),
      field_description: '',
    };

    const sourceNode = genMappingNode({
      nodesAtGeneration: nodes,
      nodeKind: 'source',
      newField: sourceField,
      sourceModelKey,
    });

    nodes.push(sourceNode);

    const targetField = {
      model_id: target_model_id,
      model_name: target_model_name,
      field_name: target_field,
      field_type: target_type,
      gen_field_type: getNormalizedFieldType(target_type),
      field_description: '',
    };

    const targetNode = genMappingNode({
      nodesAtGeneration: nodes,
      nodeKind: 'target',
      newField: targetField,
      sourceModelKey,
    });

    nodes.push(targetNode);

    const edge = {
      id: genEdgeId(sourceNode.id, targetNode.id),
      source: sourceNode.id,
      target: targetNode.id,
      type: edgeStyle.type,
      style: edgeStyle.style,
    };

    edges.push(edge);
  });

  return { nodes, edges };
}

export function getCompleteButtonStyle(
  canSubmit: boolean,
  currentModelCompleted: boolean,
): { button: string; icon: string } {
  const baseButtonClass = 'p-2 rounded-md border';

  if (currentModelCompleted) {
    if (!canSubmit) {
      return { button: `${baseButtonClass} bg-emerald-200`, icon: 'stroke-white' };
    }

    return { button: `${baseButtonClass} bg-emerald-600`, icon: 'stroke-white' };
  }

  if (!canSubmit) {
    return { button: `${baseButtonClass} bg-white border-zinc-200`, icon: 'stroke-zinc-200' };
  }

  return { button: `${baseButtonClass} bg-white border-zinc-200`, icon: 'stroke-emerald-700' };
}
