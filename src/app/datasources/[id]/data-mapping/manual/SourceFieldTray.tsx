import { useState, useCallback } from 'react';
import { InputText, DataFilter } from '@/components';
import { FilterValue } from '@/types';
import { MappingDictionaryField, FieldKind } from '@/types';
import SourceFieldList from './SourceFieldList';
import { genMappingNode } from './utils';
import { getFilterOptions } from '@/utils/data';
import { useMappingStore } from '@/stores';
import { useDataSourceStore } from '@/stores';

export const filterFields = {
  type: 'gen_field_type',
};

export default function FieldTray() {
  const { currentDataSource: dataSource } = useDataSourceStore();
  const { nodes, addNodes } = useMappingStore();

  const [searchTerm, setSearchTerm] = useState<string>();
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
  });

  const handleFieldSelect = useCallback(
    (nodeKind: FieldKind, field: MappingDictionaryField) => {
      addNodes([
        genMappingNode({
          nodesAtGeneration: nodes,
          nodeKind,
          newField: field,
          sourceModelKey: dataSource!.sourceModelKey,
        }),
      ]);
    },
    [addNodes, nodes, dataSource],
  );

  return (
    <div className="flex flex-col py-4 rounded-sm h-[calc(100vh-150px)] w-[550px] bg-white relative">
      <span className="mx-4 text-2xl">Source fields</span>
      <div className="flex flex-col px-4 overflow-auto">
        <div className="flex py-6 gap-x-2 sticky top-0 bg-white z-10">
          <InputText
            icon="SearchMd"
            placeholder="Name"
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <DataFilter
            filterConfig={filterConfig}
            onFilterConfigChange={setFilterConfig}
            filterOptions={getFilterOptions('file')}
            iconOnly
          />
        </div>
        <SourceFieldList
          onSelect={(field: MappingDictionaryField) => handleFieldSelect('source', field)}
          searchTerm={searchTerm}
          filterConfig={filterConfig}
        />
      </div>
    </div>
  );
}
