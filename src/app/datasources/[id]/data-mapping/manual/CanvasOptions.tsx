import { useCallback, useState, useEffect } from 'react';
import { SetStateFunction, MappingNode, TargetModel } from '@/types';
import { Select, CheckCircle, Button, Popup } from '@/components';
import { Check, Columns01, LayoutLeft, PackagePlus } from '@/components/icons';
import { useMappingStore, useDataSourceStore } from '@/stores';
import { genMappingNode, getCompleteButtonStyle } from './utils';

type TargetModelOption = {
  label: string;
  value: TargetModel;
};

type Props = {
  targetModelOptions: TargetModelOption[];
  currentModelCompleted: boolean;
  currentTargetModel: TargetModel;
  onCurrentTargetModelChange: SetStateFunction<TargetModel>;
  completedTargetModelIds: string[];
  handleCompleteModel: () => Promise<void>;
  displayCompletedContent: boolean;
  handleModelNext: () => void;
  dualTrays: boolean;
  onDualTrays: SetStateFunction<boolean>;
};

export default function CanvasOptions(props: Props) {
  const {
    targetModelOptions,
    currentModelCompleted,
    currentTargetModel,
    onCurrentTargetModelChange,
    completedTargetModelIds,
    handleCompleteModel,
    displayCompletedContent,
    handleModelNext,
    dualTrays,
    onDualTrays,
  } = props;

  const [canSubmit, setCanSubmit] = useState<boolean>(false);

  const { nodes, addNodes, modelJoins, setJoinModalOpen, reset } = useMappingStore();
  const { currentDataSource: dataSource } = useDataSourceStore();

  // subscribe to store changes to enable/disable submit action. If any store changes are made, enable the action.
  useEffect(() => {
    const unsubscribe = useMappingStore.subscribe(() => {
      setCanSubmit(true);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const addAllTargetFields = useCallback(() => {
    const nodes: MappingNode[] = [];

    currentTargetModel.dictionary.forEach((targetField) => {
      const node = genMappingNode({
        nodesAtGeneration: nodes,
        nodeKind: 'target',
        newField: {
          ...targetField,
          model_id: currentTargetModel.id,
          model_name: currentTargetModel.name,
        },
        sourceModelKey: dataSource!.sourceModelKey,
      });

      nodes.push(node);
    });

    addNodes(nodes);
  }, [currentTargetModel, dataSource, addNodes]);

  const { button: completeButtonClass, icon: completeIconClass } = getCompleteButtonStyle(
    canSubmit,
    currentModelCompleted,
  );

  const popupButtonClass =
    'flex items-center hover:bg-zinc-200 py-2 pl-2 gap-x-2 w-full text-sm text-zinc-900 stroke-zinc-900';

  const iconClass = `w-4 h-4`;

  return (
    <div className="flex flex-col gap-y-2 items-end">
      <div className="flex items-center gap-x-2">
        <Select
          options={targetModelOptions}
          value={targetModelOptions.find((opt) => opt.value.id === currentTargetModel?.id)}
          onChange={(opt) => {
            if (
              currentModelCompleted ||
              !nodes?.length ||
              window.confirm(
                'Changing models will cause your unsaved changes to be lost. Continue anyway?',
              )
            ) {
              onCurrentTargetModelChange(opt.value);
              reset();
            }
          }}
          formatOptionLabel={({ label, value }) => {
            const completed = !!completedTargetModelIds.find((modelId) => modelId === value.id);

            return (
              <div className="flex items-center justify-between gap-x-2">
                <span className="text-sm text-zinc-900">{label}</span>
                {completed && <Check className="stroke-emerald-700 w-5 h-5" />}
              </div>
            );
          }}
        />
        <Button
          type="secondary"
          label={modelJoins?.length ? 'Edit joins' : 'Create joins'}
          onClick={() => {
            setJoinModalOpen(true);
          }}
        />
        <Popup buttonIcon="DotsHorizontal" buttonType="secondary">
          <div className="flex flex-col items-start divide-y">
            <button className={popupButtonClass} onClick={addAllTargetFields}>
              <PackagePlus className={iconClass} />
              <span>Add all target fields</span>
            </button>
            <button
              className={popupButtonClass}
              onClick={() => {
                onDualTrays((prev) => !prev);
              }}
            >
              {dualTrays ? (
                <LayoutLeft className={iconClass} />
              ) : (
                <Columns01 className={iconClass} />
              )}
              <span>{dualTrays ? 'Single tray' : 'Dual trays'}</span>
            </button>
          </div>
        </Popup>
        <button
          className={completeButtonClass}
          title="Mark model as complete"
          onClick={async () => {
            await handleCompleteModel();
            setCanSubmit(false);
          }}
          id="rewardId"
          // disabled={!canSubmit}
        >
          <CheckCircle className={completeIconClass} />
        </button>
      </div>
      {displayCompletedContent && (
        <div className="flex gap-x-2 bg-purple-700 px-4 py-2 shadow-sm rounded">
          <span className="text-xs text-white">{currentTargetModel?.name} marked complete!</span>
          <span
            className="text-xs font-bold underline text-white hover:cursor-pointer"
            onClick={handleModelNext}
          >
            Move to the next model?
          </span>
        </div>
      )}
    </div>
  );
}
