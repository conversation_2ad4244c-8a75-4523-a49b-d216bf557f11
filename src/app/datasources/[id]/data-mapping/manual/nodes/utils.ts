import { getHandleConnectionStatus } from '../utils';

const baseHandleStyle: Record<string, any> = { width: '10px', height: '10px' };

type HandleType = 'target' | 'source';

type GetHandleStyleProps = {
  handleId: string;
};

export function getHandleStyle(props: GetHandleStyleProps): Record<string, any> {
  const { handleId } = props;

  const handleStyle = { ...baseHandleStyle };

  const connected = getHandleConnectionStatus(handleId);

  if (connected) {
    handleStyle.background = '#10B981';
  } else {
    handleStyle.background = '#ffffff';
    handleStyle.borderColor = '#E4E4E7';
  }

  return handleStyle;
}

type GetIfThenHandleStyleProps = {
  handleId: string;
  handleType: HandleType;
};

export function getIfThenHandleStyle(props: GetIfThenHandleStyleProps): Record<string, any> {
  const { handleId, handleType } = props;

  const handleStyle = { ...baseHandleStyle };

  const connected = getHandleConnectionStatus(handleId);

  if (connected) {
    handleStyle.background = '#10B981';
  } else {
    handleStyle.background = '#ffffff';
    handleStyle.borderColor = '#E4E4E7';
  }

  if (handleType === 'target') {
    handleStyle.top = 20;
  }

  return handleStyle;
}
