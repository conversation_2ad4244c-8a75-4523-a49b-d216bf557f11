import { NodeProps, Handle, Position, useNodes, useEdges } from '@xyflow/react';
import TransformBox from '../transform/TransformBox';
import { TransformNode, GeneralNode } from '@/types';
import { getNodeConnectionStatus, genShiftClickEdges } from '../utils';
import { useMappingStore } from '@/stores';

const baseHandleStyle: Record<string, any> = { width: '10px', height: '10px' };
const baseContainerClass = 'w-60 rounded-md hover:cursor-pointer';

export default function TransformNodeComponent(props: NodeProps<TransformNode>) {
  const { id, data, selected } = props;
  const { transformKey } = data.value;

  const { addEdges, setTransformModalOpen, setOpenNodeId } = useMappingStore();

  const handleStyle = { ...baseHandleStyle };

  const currentNodes = useNodes() as GeneralNode[];
  const currentEdges = useEdges();

  const connected = getNodeConnectionStatus(id, currentEdges);

  if (connected) {
    handleStyle.background = '#10B981';
  } else {
    handleStyle.background = '#ffffff';
    handleStyle.borderColor = '#E4E4E7';
  }

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleClick = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });
    }
  };

  const handleDoubleClick = () => {
    setTransformModalOpen(true);
    setOpenNodeId(id);
  };

  return (
    <div className={containerClass} onClick={handleClick} onDoubleClick={handleDoubleClick}>
      <Handle type="target" position={Position.Left} style={handleStyle} />
      <TransformBox transformKey={transformKey} height="h-12" />
      <Handle type="source" position={Position.Right} style={handleStyle} />
    </div>
  );
}
