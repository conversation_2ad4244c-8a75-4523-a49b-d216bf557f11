import { NodeProps, Handle, Position } from '@xyflow/react';
import TransformBox from '../transform/TransformBox';
import { TransformNode } from '@/types';
import { genShiftClickEdges, genNodeHandleId } from '../utils';
import { useMappingStore } from '@/stores';
import { getHandleStyle } from './utils';

const baseContainerClass = 'w-60 rounded-md hover:cursor-pointer';

export default function TransformNodeComponent(props: NodeProps<TransformNode>) {
  const { id, data, selected } = props;
  const { transformKey } = data.value;

  const { nodes: currentNodes, addEdges, setTransformModalOpen, setOpenNodeId } = useMappingStore();

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleClick = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });
    }
  };

  const handleDoubleClick = () => {
    setTransformModalOpen(true);
    setOpenNodeId(id);
  };

  const targetHandleId = genNodeHandleId(id, 'target');
  const sourceHandleId = genNodeHandleId(id, 'source');

  return (
    <div className={containerClass} onClick={handleClick} onDoubleClick={handleDoubleClick}>
      <Handle
        id={targetHandleId}
        type="target"
        position={Position.Left}
        style={getHandleStyle({ handleId: targetHandleId })}
      />
      <TransformBox transformKey={transformKey} height="h-12" />
      <Handle
        id={sourceHandleId}
        type="source"
        position={Position.Right}
        style={getHandleStyle({ handleId: sourceHandleId })}
      />
    </div>
  );
}
