import { NodeProps, Handle, Position } from '@xyflow/react';
import MappingFieldBox from '../MappingFieldBox';
import { MappingNode, FieldKind } from '@/types';
import { imgSrc } from 'lib/source';
import { getNodeConnectionStatus, genShiftClickEdges } from '../utils';
import { useMappingStore } from '@/stores';

const nodeTypeProps: Record<string, Record<string, any>> = {
  source: { position: Position.Right },
  target: { position: Position.Left },
};

const baseHandleStyle: Record<string, any> = { width: '10px', height: '10px' };
const baseContainerClass = 'rounded-md';

export default function MappingNodeComponent(props: NodeProps<MappingNode>) {
  const { id, data, selected } = props;
  const { field, sourceModelKey, nodeKind } = data.value;
  const { nodes: currentNodes, edges: currentEdges, addEdges } = useMappingStore();

  const handleStyle = { ...baseHandleStyle };

  const connected = getNodeConnectionStatus(id, currentEdges);

  if (connected) {
    handleStyle.background = '#10B981';
  } else {
    handleStyle.background = '#ffffff';
    handleStyle.borderColor = '#E4E4E7';
  }

  const imgKey = nodeKind === 'target' ? 'gestalt' : sourceModelKey;

  const imgProps = imgSrc.getOrDefault(imgKey);
  const { imgPath, imgAlt, dropdownStyle } = imgProps;

  const nodeProps = nodeTypeProps[nodeKind];

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleSelect = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });

      return;
    }
  };

  return (
    <div className={containerClass} title={field.dbt_name} onClick={handleSelect}>
      <Handle type={nodeKind as FieldKind} position={nodeProps.position} style={handleStyle} />
      <MappingFieldBox
        imgSrc={imgPath}
        imgAlt={imgAlt}
        imgStyle={dropdownStyle}
        field={field}
        subField={nodeKind === 'source' ? field.model_name : undefined}
        height="h-12"
      />
    </div>
  );
}
