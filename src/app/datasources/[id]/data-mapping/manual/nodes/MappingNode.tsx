import { NodeP<PERSON>, Handle, Position } from '@xyflow/react';
import MappingFieldBox from '../MappingFieldBox';
import { MappingNode } from '@/types';
import { imgSrc } from 'lib/source';
import { genShiftClickEdges, genNodeHandleId } from '../utils';
import { useMappingStore } from '@/stores';
import { getHandleStyle } from './utils';

const baseContainerClass = 'rounded-md';

export default function MappingNodeComponent(props: NodeProps<MappingNode>) {
  const { id, data, selected } = props;
  const { field, sourceModelKey, nodeKind } = data.value;
  const { nodes: currentNodes, addEdges } = useMappingStore();

  const imgKey = nodeKind === 'target' ? 'gestalt' : sourceModelKey;

  const imgProps = imgSrc.getOrDefault(imgKey);
  const { imgPath, alt, dropdownStyle } = imgProps;

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleSelect = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });

      return;
    }
  };

  const targetHandleId = genNodeHandleId(id, 'target');
  const sourceHandleId = genNodeHandleId(id, 'source');

  return (
    <div className={containerClass} title={field.warehouse_name} onClick={handleSelect}>
      <Handle
        id={targetHandleId}
        type="target"
        position={Position.Left}
        style={getHandleStyle({ handleId: targetHandleId })}
      />
      <MappingFieldBox
        imgSrc={imgPath}
        imgAlt={alt}
        imgStyle={dropdownStyle}
        field={field}
        subField={nodeKind === 'source' ? field.model_name : undefined}
        height="h-12"
      />
      <Handle
        id={sourceHandleId}
        type="source"
        position={Position.Right}
        style={getHandleStyle({ handleId: sourceHandleId })}
      />
    </div>
  );
}
