import { useEffect, useMemo } from 'react';
import { NodeProps, Handle, Position, useUpdateNodeInternals } from '@xyflow/react';
import { TransformNode } from '@/types';
import { genShiftClickEdges, genNodeHandleId } from '../utils';
import { useMappingStore } from '@/stores';
import { transforms } from '../../constants';
import { FileQuestion01 } from '@/components/icons';
import { genLetterTitle } from '@/utils/string';
import { getIfThenHandleStyle } from './utils';
import { orderConditionalPaths } from '../transform/utils';

const baseContainerClass = 'rounded-md hover:cursor-pointer';

export default function IfThenNode(props: NodeProps<TransformNode>) {
  const { id, data, selected } = props;
  const { conditionalPaths, conditionalPathOrder } = data.value.opts || {};

  const { addEdges, nodes: currentNodes, setTransformModalOpen, setOpenNodeId } = useMappingStore();

  const updateNodeInternals = useUpdateNodeInternals();

  // hook to update the library node internals when paths change (which creates new handles)
  useEffect(() => {
    if (!conditionalPaths?.length) {
      return;
    }

    updateNodeInternals(id);
  }, [conditionalPaths, updateNodeInternals, id]);

  const transform = transforms.find((transform) => transform.key === 'ifThen');
  const { label, Icon } = transform!;

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleClick = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });
    }
  };

  const handleDoubleClick = () => {
    setTransformModalOpen(true);
    setOpenNodeId(id);
  };

  const orderedPaths = useMemo(() => {
    if (!conditionalPaths?.length || !conditionalPathOrder) {
      return [];
    }

    return orderConditionalPaths(conditionalPaths, conditionalPathOrder);
  }, [conditionalPaths, conditionalPathOrder]);

  const targetHandleId = genNodeHandleId(id, 'target');

  return (
    <div className={containerClass} onClick={handleClick} onDoubleClick={handleDoubleClick}>
      <div
        className={`flex flex-row border-2 divide-x border-solid border-gray-100 rounded-lg bg-white`}
      >
        <div className="p-2">
          <Handle
            id={targetHandleId}
            type="target"
            position={Position.Left}
            style={getIfThenHandleStyle({ handleId: targetHandleId, handleType: 'target' })}
          />
          <FileQuestion01 className="h-5 w-5 bg-teal-200 stroke-teal-600" />
        </div>
        <div className="flex flex-col items-end py-2">
          <div className="flex gap-x-2 items-center py-1 px-4">
            <Icon className="h-5 w-5 stroke-zinc-900" />
            <span title={label} className="text-xs font-semibold">
              {label}
            </span>
          </div>
          {orderedPaths?.map((path) => {
            const { pathId, isElse } = path;

            return (
              <div key={pathId} className="relative">
                <span className="text-xs text-zinc-500 mr-4">
                  {isElse ? 'Else' : `Path ${genLetterTitle(parseInt(pathId), { is1Based: true })}`}
                </span>
                <Handle
                  id={pathId}
                  type="source"
                  position={Position.Right}
                  style={getIfThenHandleStyle({ handleId: pathId, handleType: 'source' })}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
