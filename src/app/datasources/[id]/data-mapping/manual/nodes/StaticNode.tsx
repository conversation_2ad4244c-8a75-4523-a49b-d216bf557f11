import { NodeProps, Handle, Position, useNodes, useEdges } from '@xyflow/react';
import StaticBox from '../StaticBox';
import { StaticNode, GeneralNode } from '@/types';
import { getNodeConnectionStatus, genShiftClickEdges } from '../utils';
import { useMappingStore } from '@/stores';

const baseHandleStyle: Record<string, any> = { width: '10px', height: '10px' };
const baseContainerClass = 'w-[180px] rounded-md hover:cursor-pointer';

export default function StaticNodeComponent(props: NodeProps<StaticNode>) {
  const { id, data, selected } = props;
  const { dataType, value } = data.value;
  const { addEdges, setTransformModalOpen, setOpenNodeId } = useMappingStore();

  const handleStyle = { ...baseHandleStyle };

  const currentNodes = useNodes() as GeneralNode[];
  const currentEdges = useEdges();

  const connected = getNodeConnectionStatus(id, currentEdges);

  if (connected) {
    handleStyle.background = '#10B981';
  } else {
    handleStyle.background = '#ffffff';
    handleStyle.borderColor = '#E4E4E7';
  }

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleSelect = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });

      return;
    }

    setTransformModalOpen(true);
    setOpenNodeId(id);
  };

  return (
    <div className={containerClass} onDoubleClick={handleSelect}>
      <StaticBox dataType={dataType} value={value} />
      <Handle type="source" position={Position.Right} style={handleStyle} />
    </div>
  );
}
