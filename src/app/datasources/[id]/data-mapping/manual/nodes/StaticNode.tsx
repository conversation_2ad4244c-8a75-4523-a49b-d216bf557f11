import { NodeProps, Handle, Position, useNodes } from '@xyflow/react';
import StaticBox from '../StaticBox';
import { StaticNode, GeneralNode } from '@/types';
import { genShiftClickEdges, genNodeHandleId } from '../utils';
import { getHandleStyle } from './utils';
import { useMappingStore } from '@/stores';

const baseContainerClass = 'w-[180px] rounded-md hover:cursor-pointer';

export default function StaticNodeComponent(props: NodeProps<StaticNode>) {
  const { id, data, selected } = props;
  const { dataType, value } = data.value;
  const { addEdges, setTransformModalOpen, setOpenNodeId } = useMappingStore();

  const currentNodes = useNodes() as GeneralNode[];

  let containerClass = baseContainerClass;

  if (selected) {
    containerClass = `${containerClass} border-2 border-dashed border-emerald-500`;
  }

  const handleSelect = (e) => {
    if (e.shiftKey) {
      e.stopPropagation();

      genShiftClickEdges({ nodeId: id, nodeSelected: selected, currentNodes, addEdges });

      return;
    }

    setTransformModalOpen(true);
    setOpenNodeId(id);
  };

  const targetHandleId = genNodeHandleId(id, 'target');
  const sourceHandleId = genNodeHandleId(id, 'source');

  return (
    <div className={containerClass} onDoubleClick={handleSelect}>
      <Handle
        id={targetHandleId}
        type="target"
        position={Position.Left}
        style={getHandleStyle({ handleId: targetHandleId })}
      />
      <StaticBox dataType={dataType} value={value} />
      <Handle
        id={sourceHandleId}
        type="source"
        position={Position.Right}
        style={getHandleStyle({ handleId: sourceHandleId })}
      />
    </div>
  );
}
