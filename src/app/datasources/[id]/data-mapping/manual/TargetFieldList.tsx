import { useMemo } from 'react';
import MappingFieldBox from './MappingFieldBox';
import {
  FilterValue,
  MappingDictionaryField,
  TargetModel,
  TargetModelDictionaryField,
} from '@/types';
import { imgSrc } from 'lib/source';
import { filterData } from '@/utils/data';
import { filterFields } from './FieldTray';

type Props = {
  targetModel: TargetModel;
  onSelect: (field: MappingDictionaryField) => void;
  filterConfig: Record<string, FilterValue>;
  searchTerm?: string;
};

const gestaltImgSrc = imgSrc.getOrDefault('gestalt').imgPath;

export default function TargetFieldList(props: Props) {
  const { targetModel, onSelect, searchTerm, filterConfig } = props;
  const { id, name, dictionary } = targetModel;

  const filteredFields = useMemo(
    () =>
      filterData<TargetModelDictionaryField>(dictionary, {
        searchTerm,
        nameField: 'field_name',
        filterConfig,
        filterFields,
      }),
    [dictionary, searchTerm, filterConfig],
  );

  return (
    <div className="flex flex-col gap-y-1">
      {filteredFields?.map((field, idx) => (
        <div className="hover:cursor-pointer" key={`${field.field_name}${idx}`}>
          <MappingFieldBox
            imgSrc={gestaltImgSrc}
            imgAlt={field.field_name}
            field={{ ...field, model_id: id, model_name: name }}
            onSelect={onSelect}
          />
        </div>
      ))}
    </div>
  );
}
