import { useMemo } from 'react';
import MappingFieldBox from './MappingFieldBox';
import { TargetField, BaseField, FilterValue } from '@/types';
import { imgSrc } from 'lib/source';
import { filterData } from '@/utils/data';
import { filterFields } from './FieldTray';

type Props = {
  targetFields: TargetField[];
  onSelect: (field: BaseField) => void;
  filterConfig: Record<string, FilterValue>;
  searchTerm?: string;
};

const gestaltImgSrc = imgSrc.getOrDefault('gestalt').imgPath;

export default function TargetFieldList(props: Props) {
  const { targetFields, onSelect, searchTerm, filterConfig } = props;

  const filteredFields = useMemo(
    () =>
      filterData<TargetField>(targetFields, {
        searchTerm,
        nameField: 'field_name',
        filterConfig,
        filterFields,
      }),
    [targetFields, searchTerm, filterConfig],
  );

  return (
    <div className="flex flex-col gap-y-1">
      {filteredFields?.map((field, idx) => (
        <div className="hover:cursor-pointer" key={`${field.field_name}${idx}`}>
          <MappingFieldBox
            imgSrc={gestaltImgSrc}
            imgAlt={field.field_name}
            field={field}
            onSelect={onSelect}
          />
        </div>
      ))}
    </div>
  );
}
