import { useState, useEffect, useMemo } from 'react';
import { GeneralNode, StaticNode } from '@/types';
import { Modal, Button } from '@/components';
import { getSourceNodes } from './utils';
import { useMappingStore } from '@/stores';
import StaticDataCreator from './StaticDataCreator';

export default function TransformModal() {
  const {
    nodes: currentNodes,
    openNodeId,
    edges: currentEdges,
    editNodeValue,
    transformModalOpen,
    setTransformModalOpen,
  } = useMappingStore();

  const [dataType, setDataType] = useState<string>();
  const [value, setValue] = useState<any>();

  const currentNode = currentNodes.find((node) => node.id === openNodeId) as StaticNode | undefined;

  const sourceNodes = useMemo(
    () =>
      getSourceNodes({
        nodeId: openNodeId || '',
        currentNodes,
        currentEdges,
      }) as GeneralNode[],
    [openNodeId, currentNodes, currentEdges],
  );

  useEffect(() => {
    if (!currentNode) {
      return;
    }

    const { dataType: initialDataType, value: initialValue } = currentNode.data.value;

    setDataType(initialDataType);
    setValue(initialValue);
  }, [currentNode, sourceNodes]);

  if (!currentNode) {
    return null;
  }

  const handleSubmit = () => {
    editNodeValue(currentNode.id, { dataType, value });

    setTransformModalOpen(false);
  };

  return (
    <Modal
      open={transformModalOpen}
      setIsOpen={setTransformModalOpen}
      title="Static data"
      titleClass="flex gap-2 text-lg text-zinc-900"
      className="w-[384px]"
    >
      <div className="flex flex-col gap-y-6 justify-between w-full">
        <StaticDataCreator
          dataType={dataType}
          onDataTypeChange={setDataType}
          value={value}
          onValueChange={setValue}
        />
        <div className="flex justify-end items-center gap-x-2">
          <Button
            type="secondary"
            label="Cancel"
            onClick={() => {
              setTransformModalOpen(false);
            }}
          />
          <Button label="save" onClick={handleSubmit} />
        </div>
      </div>
    </Modal>
  );
}
