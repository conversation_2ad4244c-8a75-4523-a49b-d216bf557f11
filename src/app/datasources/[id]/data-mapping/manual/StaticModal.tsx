import { useState, useEffect, useMemo } from 'react';
import { dataTypeOpts } from '../constants';
import { GeneralNode, StaticNode } from '@/types';
import { Modal, Select, Button, FieldTypeIcon, InputText } from '@/components';
import { getSourceNodes } from './utils';
import { findGeneralType } from '@/utils/data';
import { useMappingStore } from '@/stores';

export default function TransformModal() {
  const {
    nodes: currentNodes,
    openNodeId,
    edges: currentEdges,
    editNodeValue,
    transformModalOpen,
    setTransformModalOpen,
  } = useMappingStore();

  const [dataType, setDataType] = useState<string>();
  const [value, setValue] = useState<any>();

  const currentNode = currentNodes.find((node) => node.id === openNodeId) as StaticNode | undefined;

  const sourceNodes = useMemo(
    () =>
      getSourceNodes({
        nodeId: openNodeId || '',
        currentNodes,
        currentEdges,
      }) as GeneralNode[],
    [openNodeId, currentNodes, currentEdges],
  );

  useEffect(() => {
    if (!currentNode) {
      return;
    }

    const { dataType: initialDataType, value: initialValue } = currentNode.data.value;

    setDataType(initialDataType);
    setValue(initialValue);
  }, [currentNode, sourceNodes]);

  if (!currentNode) {
    return null;
  }

  const handleSubmit = () => {
    editNodeValue(currentNode.id, { dataType, value });

    setTransformModalOpen(false);
  };

  return (
    <Modal
      open={transformModalOpen}
      setIsOpen={setTransformModalOpen}
      title="Static data"
      titleClass="flex gap-2 text-lg text-zinc-900"
      className="w-[384px]"
    >
      <div className="flex flex-col gap-y-6 justify-between w-full">
        <div className="flex flex-col gap-y-8">
          <div className="flex flex-col">
            <span className="text-sm">Data type</span>
            <div className="flex flex-col">
              <Select
                options={dataTypeOpts}
                value={dataTypeOpts.find((opt) => opt.value === dataType)}
                onChange={(opt) => {
                  setDataType(opt.value);
                }}
                formatOptionLabel={({ label, value }) => {
                  return (
                    <div className="flex items-center gap-x-2">
                      <FieldTypeIcon type={findGeneralType(value)} />
                      <span className="text-sm text-zinc-900">{label}</span>
                    </div>
                  );
                }}
              />
            </div>
          </div>
          <div className="flex flex-col">
            <span className="text-sm">Static value</span>
            <InputText
              value={value}
              placeholder="Value"
              onChange={(e) => setValue(e.target.value)}
              noIcon
              className="w-[320px]"
            />
          </div>
        </div>
        <div className="flex justify-end items-center gap-x-2">
          <Button
            type="secondary"
            label="Cancel"
            onClick={() => {
              setTransformModalOpen(false);
            }}
          />
          <Button label="save" onClick={handleSubmit} />
        </div>
      </div>
    </Modal>
  );
}
