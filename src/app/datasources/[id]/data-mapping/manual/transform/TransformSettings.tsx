import { TransformOptions, SetStateFunction, GeneralNode } from '@/types';
import { transforms } from '../../constants';
import { getTransformAlertStatus } from '../utils';
import TransformAlert from './TransformAlert';
import TransformSettingsContent from './TransformSettingsContent';

type Props = {
  transformKey: string;
  sourceNodes: GeneralNode[];
  onOptsChange: SetStateFunction<TransformOptions>;
  opts: TransformOptions;
};

export default function TransformSettings(props: Props) {
  const { transformKey, sourceNodes, opts, onOptsChange } = props;

  const transform = transforms.find((transform) => transform.key === transformKey)!;

  const { minFieldAlertStatus, maxFieldAlertStatus } = getTransformAlertStatus({
    sourceNodes,
    transform,
  });

  const displayAlert = minFieldAlertStatus || maxFieldAlertStatus;

  if (displayAlert) {
    return <TransformAlert transform={transform} sourceNodes={sourceNodes} />;
  }

  return (
    <TransformSettingsContent
      transform={transform}
      sourceNodes={sourceNodes}
      opts={opts}
      onOptsChange={onOptsChange}
    />
  );
}
