import { useState } from 'react';
import { Modal } from '@/components';
import { ChevronLeft } from '@/components/icons';
import { useMappingStore } from '@/stores';
import IfThenSettingsContent from './IfThenSettingsContent';
import CreateStaticDataContent from './CreateStaticDataContent';
import { TransformOptions } from '@/types';

const initialTransformOpts = {
  conditionalPaths: [
    { pathId: '1', sourceNodeId: null, operator: null, comparisonNodeId: null, resultNodeId: null },
  ],
  conditionalPathOrder: ['1'],
};

export default function IfThenModal() {
  const { transformModalOpen, setTransformModalOpen } = useMappingStore();

  const [isCreatingData, setIsCreatingData] = useState<boolean>(false);
  const [transformOptions, setTransformOptions] = useState<TransformOptions>(initialTransformOpts);

  const modalTitle = isCreatingData ? 'Create static data' : 'If / Then';

  const modalTitleComponent = (
    <div className="flex items-center gap-x-2">
      {isCreatingData && (
        <button
          onClick={() => {
            setIsCreatingData(false);
          }}
        >
          <ChevronLeft />
        </button>
      )}
      <h2 className="flex gap-2 text-lg text-zinc-900">{modalTitle}</h2>
    </div>
  );

  return (
    <Modal
      open={transformModalOpen}
      setIsOpen={setTransformModalOpen}
      titleComponent={modalTitleComponent}
      className="w-[900px]"
    >
      {isCreatingData ? (
        <CreateStaticDataContent onCreatingData={setIsCreatingData} />
      ) : (
        <IfThenSettingsContent
          transformOptions={transformOptions}
          onTransformOptionsChange={setTransformOptions}
          onCreatingData={setIsCreatingData}
        />
      )}
    </Modal>
  );
}
