import { useState, useEffect, useCallback } from 'react';
import { Modal } from '@/components';
import { ChevronLeft } from '@/components/icons';
import { useMappingStore } from '@/stores';
import IfThenSettingsContent from './IfThenSettingsContent';
import CreateStaticDataContent from './CreateStaticDataContent';
import { TransformNode, ConditionalPath, TransformOptions } from '@/types';

const initialTransformOpts = {
  conditionalPaths: [
    { pathId: '1', sourceNodeId: null, operator: null, comparisonNodeId: null, resultNodeId: null },
    {
      pathId: 'else',
      isElse: true,
      sourceNodeId: null,
      operator: null,
      comparisonNodeId: null,
      resultNodeId: null,
    },
  ],
  conditionalPathOrder: ['1'],
};

export default function IfThenModal() {
  const { transformModalOpen, setTransformModalOpen, nodes, openNodeId, clearTemp } =
    useMappingStore();

  const currentNode = nodes.find((node) => node.id === openNodeId) as TransformNode;

  const [isCreatingData, setIsCreatingData] = useState<boolean>(false);
  const [transformOptions, setTransformOptions] = useState<TransformOptions | null>();
  // current path and option, used to automatically add newly created static data to an option (e.g. comparison value)
  const [currentPathId, setCurrentPathId] = useState<string | null>();
  // string value of the option to set
  const [currentPathOptKey, setCurrentPathOptKey] = useState<keyof ConditionalPath | null>();

  // set initial transform options to the node value or the default paths
  useEffect(() => {
    if (!transformModalOpen) {
      return;
    }

    setTransformOptions(currentNode?.data?.value?.opts || initialTransformOpts);
  }, [transformModalOpen, currentNode]);

  const modalTitle = isCreatingData ? 'Create static data' : 'If / Then';

  const modalTitleComponent = (
    <div className="flex items-center gap-x-2">
      {isCreatingData && (
        <button
          onClick={() => {
            setIsCreatingData(false);
          }}
        >
          <ChevronLeft className="stroke-zinc-900" />
        </button>
      )}
      <h2 className="flex gap-2 text-lg text-zinc-900">{modalTitle}</h2>
    </div>
  );

  const handleClose = useCallback(() => {
    setTransformOptions(null);
    clearTemp();
  }, [clearTemp]);

  if (!transformOptions) {
    return null;
  }

  return (
    <Modal
      open={transformModalOpen}
      setIsOpen={setTransformModalOpen}
      onClose={handleClose}
      titleComponent={modalTitleComponent}
      className="w-[900px] min-h-[400px] max-h-[800px] overflow-y-auto"
    >
      {isCreatingData ? (
        <CreateStaticDataContent
          onCreatingData={setIsCreatingData}
          onTransformOptsChange={setTransformOptions}
          currentPathId={currentPathId}
          onPathIdChange={setCurrentPathId}
          currentPathOptKey={currentPathOptKey}
          onPathOptKeyChange={setCurrentPathOptKey}
        />
      ) : (
        <IfThenSettingsContent
          transformOptions={transformOptions}
          onTransformOptsChange={setTransformOptions}
          onCreatingData={setIsCreatingData}
          onPathIdChange={setCurrentPathId}
          onPathOptKeyChange={setCurrentPathOptKey}
          onClose={handleClose}
        />
      )}
    </Modal>
  );
}
