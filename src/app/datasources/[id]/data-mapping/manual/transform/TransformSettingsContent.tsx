import { GeneralNode, TransformOptions, TransformConfig, SetStateFunction } from '@/types';
import NodeOrderList from './NodeOrderList';
import TransformDropdown from './TransformDropdown';
import { MenuPlacement } from 'react-select';

type Props = {
  transform: TransformConfig;
  sourceNodes: GeneralNode[];
  opts: TransformOptions;
  onOptsChange: SetStateFunction<TransformOptions>;
};

const baseContainerClass = 'flex flex-col justify-between p-6 gap-y-4 bg-zinc-100 rounded-md';

const orderTypeTransforms = ['concat', 'coalesce', 'math'];

export default function TransformSettings(props: Props) {
  const { transform, sourceNodes, opts, onOptsChange } = props;
  const { key, label, config } = transform;

  let containerClass = baseContainerClass;
  let dropdownMenuPlacement: MenuPlacement = 'auto';

  // set fixed height and top menu placement for draggable-type transforms to avoid container shifts when dragging items
  if (orderTypeTransforms.includes(key)) {
    containerClass = `${containerClass} h-[400px] overflow-scroll`;
    dropdownMenuPlacement = 'top';
  }

  if (!config) {
    return (
      <div className={containerClass}>
        <span className="text-sm text-zinc-500">No {label} settings required</span>
      </div>
    );
  }

  const { sourceFieldOpts, dropdownOpts } = config;

  return (
    <div className={containerClass}>
      <div>
        <span className="text-sm text-zinc-500">{label} settings</span>
        {sourceFieldOpts?.type === 'order' && (
          <NodeOrderList
            transform={transform}
            sourceNodes={sourceNodes}
            opts={opts}
            onOptsChange={onOptsChange}
          />
        )}
      </div>
      {dropdownOpts && (
        <TransformDropdown
          transform={transform}
          opts={opts}
          onOptsChange={onOptsChange}
          menuPlacement={dropdownMenuPlacement}
        />
      )}
    </div>
  );
}
