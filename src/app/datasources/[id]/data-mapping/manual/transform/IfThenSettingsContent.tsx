import { useMemo, useCallback } from 'react';
import { orderBy, maxBy } from 'lodash';
import { DraggableList } from '@/components';
import { PlusCircle } from '@/components/icons';
import { DropResult } from '@hello-pangea/dnd';
import { reorderArray } from '@/utils/data';
import IfThenPath from './IfThenPath';
import { TransformOptions, SetStateFunction } from '@/types';
import TransformModalFooter from './TransformModalFooter';

type Props = {
  transformOptions: TransformOptions;
  onTransformOptionsChange: SetStateFunction<TransformOptions>;
  onCreatingData: SetStateFunction<boolean>;
};

export default function IfThenSettingsContent(props: Props) {
  const { transformOptions, onTransformOptionsChange, onCreatingData } = props;
  const { conditionalPaths, conditionalPathOrder } = transformOptions;

  const draggableItems = useMemo(() => {
    const sortedPaths = orderBy(conditionalPaths, (path) =>
      conditionalPathOrder?.findIndex((pathId) => path.pathId === pathId),
    );

    return sortedPaths.map((path) => {
      return {
        key: path.pathId,
        item: (
          <IfThenPath
            path={path}
            onTransformOptionsChange={onTransformOptionsChange}
            onCreatingData={onCreatingData}
          />
        ),
      };
    });
  }, [conditionalPaths, conditionalPathOrder, onTransformOptionsChange, onCreatingData]);

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      const { source, destination } = result;

      if (!destination) {
        return;
      }

      if (source.index === destination.index && source.droppableId === destination.droppableId) {
        return;
      }

      const newOrder = reorderArray(conditionalPathOrder!, source.index, destination.index);

      onTransformOptionsChange((prev) => ({ ...prev, conditionalPathOrder: newOrder }));
    },
    [conditionalPathOrder, onTransformOptionsChange],
  );

  const addPath = useCallback(() => {
    const latestPath = maxBy(conditionalPaths, (path) => parseInt(path.pathId));
    const newPathId = (parseInt(latestPath!.pathId) + 1).toString();

    onTransformOptionsChange((prev) => ({
      ...prev,
      conditionalPaths: [
        ...prev.conditionalPaths!,
        {
          pathId: newPathId,
          sourceNodeId: null,
          operator: null,
          comparisonNodeId: null,
          resultNodeId: null,
        },
      ],
    }));

    onTransformOptionsChange((prev) => ({
      ...prev,
      conditionalPathOrder: [...prev.conditionalPathOrder!, newPathId],
    }));
  }, [conditionalPaths, onTransformOptionsChange]);

  return (
    <div className="flex flex-col gap-y-6 justify-between">
      <div className="flex flex-col gap-y-4">
        <DraggableList id="ifThen" items={draggableItems} onDragEnd={handleDragEnd} />
        <button onClick={addPath} className="flex gap-x-2 stroke-indigo-700 text-indigo-700">
          <PlusCircle className="w-6 h-6" />
          <span>Add Path</span>
        </button>
      </div>
      <TransformModalFooter transformOptions={transformOptions} />
    </div>
  );
}
