import { useMemo, useCallback } from 'react';
import { DropResult } from '@hello-pangea/dnd';
import { DraggableList, Button } from '@/components';
import { PlusCircle, Trash01 } from '@/components/icons';
import { reorderArray } from '@/utils/data';
import IfThenPath from './IfThenPath';
import { ConditionalPath, TransformOptions, SetStateFunction } from '@/types';
import { addConditionalPath, orderConditionalPaths } from './utils';
import { useMappingStore } from '@/stores';
import { getPathRelations } from '../utils';
import ElsePath from './ElsePath';

type Props = {
  transformOptions: TransformOptions;
  onTransformOptsChange: SetStateFunction<TransformOptions | null>;
  onCreatingData: SetStateFunction<boolean>;
  onPathIdChange: SetStateFunction<string | null>;
  onPathOptKeyChange: SetStateFunction<keyof ConditionalPath | null>;
  onClose: () => void;
};

export default function IfThenSettingsContent(props: Props) {
  const {
    transformOptions,
    onTransformOptsChange,
    onCreatingData,
    onPathIdChange,
    onPathOptKeyChange,
    onClose,
  } = props;
  const { conditionalPaths, conditionalPathOrder } = transformOptions;

  const {
    editNodeValue,
    openNodeId,
    setTransformModalOpen,
    deleteNode,
    commitTemp,
    layoutCanvas,
    deleteEdge,
  } = useMappingStore();

  const draggableItems = useMemo(() => {
    // remove else path from draggable list (it's always at the end)
    const withoutElse = conditionalPaths?.filter((path) => !path.isElse) || [];
    const sortedPaths = orderConditionalPaths(withoutElse, conditionalPathOrder!);

    return sortedPaths.map((path) => {
      return {
        key: path.pathId,
        item: (
          <IfThenPath
            path={path}
            onTransformOptsChange={onTransformOptsChange}
            onCreatingData={onCreatingData}
            onPathIdChange={onPathIdChange}
            onPathOptKeyChange={onPathOptKeyChange}
          />
        ),
      };
    });
  }, [
    conditionalPaths,
    conditionalPathOrder,
    onTransformOptsChange,
    onCreatingData,
    onPathIdChange,
    onPathOptKeyChange,
  ]);

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      const { source, destination } = result;

      if (!destination) {
        return;
      }

      if (source.index === destination.index && source.droppableId === destination.droppableId) {
        return;
      }

      const newOrder = reorderArray(conditionalPathOrder!, source.index, destination.index);

      onTransformOptsChange((prev) => ({ ...prev, conditionalPathOrder: newOrder }));
    },
    [conditionalPathOrder, onTransformOptsChange],
  );

  const handleSubmit = () => {
    // Commit the path options to the node value
    editNodeValue(openNodeId, { opts: transformOptions });

    // If you've changed the transform options in a way that affects the result nodes, we need to clean up – delete
    // nodes and edges that are no longer valid.
    conditionalPaths?.forEach((path) => {
      const { pathId, resultNodeId } = path;

      // Get all nodes and edges that are connected to this path
      const { nodes: linkedNodes, edges: linkedEdges } = getPathRelations({
        nodeId: openNodeId!,
        pathId,
      });

      // Remove invalid nodes and edges (not referenced by resultNodeId). Each path can only have one target node connected.
      linkedNodes.forEach((node) => {
        if (node.id !== resultNodeId) {
          const edgeToDelete = linkedEdges.find((edge) => edge.target === node.id);

          if (edgeToDelete) {
            deleteEdge(edgeToDelete.id);
          }

          deleteNode(node.id);
        }
      });
    });

    // Commit our temporary nodes and edges. These are from creating new static data in the modal options or
    // duplicating source nodes as results.
    commitTemp();

    // use auto-layout on save, otherwise the result nodes/edges get wonky
    layoutCanvas();
    setTransformModalOpen(false);
    onTransformOptsChange(null);
  };

  const handleDelete = () => {
    deleteNode(openNodeId!);

    setTransformModalOpen(false);
    onTransformOptsChange(null);
  };

  const elsePath = conditionalPaths?.find((path) => !!path.isElse);

  return (
    <div className="flex flex-col gap-y-8 justify-between">
      <div className="flex flex-col gap-y-4">
        <div className="flex flex-col gap-y-2">
          <DraggableList
            id="ifThen"
            items={draggableItems}
            onDragEnd={handleDragEnd}
            customDragHandle
          />
          {elsePath && (
            <ElsePath
              elsePath={elsePath}
              onCreatingData={onCreatingData}
              onTransformOptsChange={onTransformOptsChange}
              onPathIdChange={onPathIdChange}
              onPathOptKeyChange={onPathOptKeyChange}
            />
          )}
        </div>
        <div>
          <button
            onClick={() => addConditionalPath({ transformOptions, onTransformOptsChange })}
            className="flex px-2 gap-x-2 stroke-indigo-700 text-indigo-700"
          >
            <PlusCircle className="w-6 h-6" />
            <span>Add Path</span>
          </button>
        </div>
      </div>
      <div className="flex justify-between">
        <Button
          type="secondary"
          theme="critical"
          label="Delete transform"
          Icon={Trash01}
          onClick={handleDelete}
        />
        <div className="flex items-center gap-x-2">
          <Button type="secondary" label="Cancel" onClick={onClose} />
          <Button label="Save" onClick={handleSubmit} />
        </div>
      </div>
    </div>
  );
}
