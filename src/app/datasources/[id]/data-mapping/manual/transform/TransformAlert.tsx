import { AlertCircle } from '@/components/icons';
import { getTransformAlertStatus } from '../utils';
import { GeneralNode, TransformConfig } from '@/types';

type Props = {
  transform: TransformConfig;
  sourceNodes: GeneralNode[];
};

export default function TransformAlert(props: Props) {
  const { transform, sourceNodes } = props;

  const { config, label } = transform;
  const { sourceFieldMin, sourceFieldMax } = config!;

  const { minFieldAlertStatus, maxFieldAlertStatus } = getTransformAlertStatus({
    sourceNodes,
    transform,
  });

  let message = '';

  if (sourceFieldMin === 1 && sourceFieldMax === 1) {
    message = `A ${label} transform requires a single input field.`;
  } else if (minFieldAlertStatus) {
    message = `A ${label} transform requires at least ${sourceFieldMin} input fields.`;
  } else if (maxFieldAlertStatus) {
    message = `A ${label} transform can have at most ${sourceFieldMax} input fields.`;
  }

  return (
    <div className="flex flex-col p-6 gap-y-4 bg-zinc-100 rounded-md">
      <div className="flex gap-x-2 items-center">
        <AlertCircle className="w-4 h-4 stroke-red-700" />
        <span className="text-red-900">{message}</span>
      </div>
    </div>
  );
}
