import { ReactNode } from 'react';
import { maxBy, orderBy } from 'lodash';
import Image from 'next/image';
import {
  ConditionalPath,
  SetStateFunction,
  TransformOptions,
  GeneralNode,
  MappingNodeValue,
  StaticNodeValue,
  Option,
} from '@/types';
import { updateArrayItem } from '@/utils/data';
import { useMappingStore } from '@/stores';
import {
  getSourceNodes,
  getTargetNodes,
  getNodeDataEquality,
  duplicateNode,
  createEdge,
  genNodeHandleId,
} from '../utils';
import { imgSrc } from 'lib/source';
import { findGeneralType } from '@/utils/data';
import { FieldTypeIcon } from '@/components';

type UpdatePathProps = {
  pathId: string;
  newVals: Partial<ConditionalPath>;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
};

export function updateConditionalPath(props: UpdatePathProps): void {
  const { pathId, newVals, onTransformOptsChange } = props;

  onTransformOptsChange((prev) => {
    const { conditionalPaths } = prev;

    const currentPathIdx = conditionalPaths!.findIndex((path) => path.pathId === pathId);

    const newPaths = updateArrayItem({
      array: conditionalPaths!,
      itemIdx: currentPathIdx,
      newVals,
    });

    return {
      ...prev,
      conditionalPaths: newPaths,
    };
  });
}

type RemovePathProps = {
  pathId: string;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
};

export function removeConditionalPath(props: RemovePathProps) {
  const { pathId, onTransformOptsChange } = props;

  onTransformOptsChange((prev) => ({
    ...prev,
    conditionalPaths: prev.conditionalPaths!.filter((path) => path.pathId !== pathId),
    conditionalPathOrder: prev.conditionalPathOrder!.filter(
      (orderPathId) => orderPathId !== pathId,
    ),
  }));
}

type AddPathProps = {
  transformOptions: TransformOptions;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
};

export function addConditionalPath(props: AddPathProps) {
  const { transformOptions, onTransformOptsChange } = props;

  const { conditionalPaths } = transformOptions;

  const withoutElse = conditionalPaths?.filter((path) => !path.isElse);

  const latestPath = maxBy(withoutElse, (path) => parseInt(path.pathId));
  const newPathId = (parseInt(latestPath!.pathId) + 1).toString();

  onTransformOptsChange((prev) => ({
    ...prev,
    conditionalPaths: [
      ...prev.conditionalPaths!,
      {
        pathId: newPathId,
        sourceNodeId: null,
        operator: null,
        comparisonNodeId: null,
        resultNodeId: null,
      },
    ],
    conditionalPathOrder: [...prev.conditionalPathOrder!, newPathId],
  }));
}

function getOption(node: GeneralNode): Option {
  const { nodeKind } = node.data.value;

  let sourceImg: ReactNode;
  let label: string = '';

  if (nodeKind === 'source' || nodeKind === 'target') {
    const { field, sourceModelKey } = node.data.value as MappingNodeValue;

    label = field.field_name;

    if (nodeKind === 'source') {
      const { imgPath, alt, dropdownStyle } = imgSrc.getOrDefault(sourceModelKey);

      sourceImg = <Image src={imgPath} alt={alt} {...dropdownStyle} />;
    } else {
      const { imgPath, alt, dropdownStyle } = imgSrc.getOrDefault('gestalt');

      sourceImg = <Image src={imgPath} alt={alt} {...dropdownStyle} />;
    }
  } else {
    // static node value
    const { value, dataType } = node.data.value as StaticNodeValue;

    label = value;
    sourceImg = <FieldTypeIcon type={findGeneralType(dataType || '')} />;
  }

  return {
    label,
    icon: sourceImg,
    value: node,
  };
}

const createDataOption = { label: 'Create static data', value: { id: 'new' } };

export function getSourceNodeOpts(): Option[] {
  const { openNodeId, nodes, tempNodes, edges, tempEdges } = useMappingStore.getState();

  const activeNodes = nodes.concat(tempNodes);
  const activeEdges = edges.concat(tempEdges);

  const sourceNodes = getSourceNodes({
    nodeId: openNodeId!,
    currentNodes: activeNodes,
    currentEdges: activeEdges,
  }) as GeneralNode[];

  const sourceNodeOpts = sourceNodes.map((node) => getOption(node));

  return [...sourceNodeOpts, createDataOption];
}

export function getResultNodeOpts(): Option[] {
  const { openNodeId, nodes, tempNodes, edges, tempEdges } = useMappingStore.getState();

  const activeNodes = nodes.concat(tempNodes);
  const activeEdges = edges.concat(tempEdges);

  const sourceNodes = getSourceNodes({
    nodeId: openNodeId!,
    currentNodes: activeNodes,
    currentEdges: activeEdges,
  }) as GeneralNode[];

  const targetNodes = getTargetNodes({
    nodeId: openNodeId!,
    currentNodes: activeNodes,
    currentEdges: activeEdges,
  }) as GeneralNode[];

  // Exclude source nodes that are already present on the result side to avoid duplicates. E.g.
  // if field "payment_amount" is connected on the source side and the result side, only include the result-side node.
  // This avoid re-duplicating the "payment_amount" node again (creating anothe result-side node).
  const filteredSourceNodes = sourceNodes.filter((sourceNode) => {
    const resultNodeValueExists = !!targetNodes.find((targetNode) =>
      getNodeDataEquality(sourceNode, targetNode),
    );

    return !resultNodeValueExists;
  });

  const allNodes = [...filteredSourceNodes, ...targetNodes];

  const nodeOpts = allNodes.map((node) => getOption(node));

  return [...nodeOpts, createDataOption];
}

export function selectFormatOptLabel(opt: Option) {
  const { label, value, icon } = opt;

  if (value.id === 'new') {
    return <span className="text-indigo-700 text-sm">{label}</span>;
  }

  return (
    <div className="flex items-center gap-x-2">
      {icon}
      {label}
    </div>
  );
}

export function orderConditionalPaths(
  paths: ConditionalPath[],
  pathOrder: string[],
): ConditionalPath[] {
  return orderBy(paths, (path) => {
    const idx = pathOrder?.findIndex((pathId) => path!.pathId === pathId);

    // else path is not included in path order, so explicitly order it last
    return idx >= 0 ? idx : null;
  });
}

type OptionSelectFunction = (opt: Option, pathOptKey: keyof ConditionalPath) => void;

type GenOptSelectProps = {
  pathId: string;
  onPathIdChange: SetStateFunction<string>;
  onPathOptKeyChange: SetStateFunction<keyof ConditionalPath>;
  onCreatingData: SetStateFunction<boolean>;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
};

// returns the function used as the select handler in the path dropdowns
export function genNodeOptSelect(props: GenOptSelectProps): OptionSelectFunction {
  const { pathId, onPathIdChange, onPathOptKeyChange, onCreatingData, onTransformOptsChange } =
    props;

  const { openNodeId, nodes, tempNodes, edges, tempEdges, addTempNodes, addTempEdges } =
    useMappingStore.getState();

  const activeNodes = nodes.concat(tempNodes);
  const activeEdges = edges.concat(tempEdges);

  return (opt: Option, pathOptKey: keyof ConditionalPath) => {
    // For creating static data, raise the current path and option key to state and direct to the creating data modal step
    if (opt.value.id === 'new') {
      onPathIdChange(pathId);
      onPathOptKeyChange(pathOptKey);
      onCreatingData(true);

      return;
    }

    // In most cases, the node referenced by the option is the one we want to save in transformOptions. If we
    // have to duplicate the node below, we use the new duplicated node id.
    let nodeIdToUpdate = opt.value.id;

    // If you're selecting an option for the result of a path, we need to add a new node and edge
    // on the result side of the transform (if one does not yet exist).
    if (pathOptKey === 'resultNodeId') {
      const isExistingTargetNode =
        activeNodes.find((node) => node.id === opt.value.id) &&
        activeEdges.find(
          (edge) =>
            edge.source === openNodeId &&
            edge.sourceHandle === pathId &&
            edge.target === opt.value.id,
        );

      if (!isExistingTargetNode) {
        const newNode = duplicateNode({
          nodeId: opt.value.id,
          nodeKindPlacement: 'target',
          includeTempNodes: true,
        });

        const newEdge = createEdge({
          sourceNodeId: openNodeId!,
          sourceHandleId: pathId,
          targetNodeId: newNode.id,
          targetHandleId: genNodeHandleId(newNode.id, 'target'),
        });

        addTempNodes([newNode]);
        addTempEdges([newEdge]);

        nodeIdToUpdate = newNode.id;
      }
    }

    // Update the specified option path with the node id
    updateConditionalPath({
      pathId,
      newVals: { [pathOptKey]: nodeIdToUpdate },
      onTransformOptsChange,
    });
  };
}
