import { useState, useCallback } from 'react';
import { Edge } from '@xyflow/react';
import { Button } from '@/components';
import StaticDataCreator from '../StaticDataCreator';
import { SetStateFunction, TransformOptions, ConditionalPath, StaticNode } from '@/types';
import { genStaticNode, createEdge, genNodeHandleId } from '../utils';
import { useMappingStore } from '@/stores';
import { updateConditionalPath } from './utils';

type Props = {
  onCreatingData: SetStateFunction<boolean>;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
  currentPathId?: string | null;
  onPathIdChange: SetStateFunction<string | null>;
  currentPathOptKey?: keyof ConditionalPath | null;
  onPathOptKeyChange: SetStateFunction<keyof ConditionalPath | null>;
};

export default function CreateStaticDataContent(props: Props) {
  const {
    onCreatingData,
    onTransformOptsChange,
    currentPathId,
    onPathIdChange,
    currentPathOptKey,
    onPathOptKeyChange,
  } = props;

  const [dataType, setDataType] = useState<string>();
  const [value, setValue] = useState<string>();

  const { openNodeId, addTempNodes, addTempEdges } = useMappingStore();

  const handleAddStaticData = useCallback(() => {
    let node: StaticNode | null = null;
    let edge: Edge | null = null;

    // If adding a static result, only add the node to the result side. Otherwise,
    // add it as a source node that feeds into the transform.
    if (currentPathOptKey === 'resultNodeId') {
      // if it's the result of a conditional path, place it on the right
      node = genStaticNode({
        dataType: dataType!,
        value: value!,
        nodeKindPlacement: 'target',
        includeTempNodes: true,
      });

      edge = createEdge({
        sourceNodeId: openNodeId!,
        sourceHandleId: currentPathId!,
        targetNodeId: node.id,
        targetHandleId: genNodeHandleId(node.id, 'target'),
      });
    } else {
      node = genStaticNode({ dataType: dataType!, value: value!, includeTempNodes: true });

      edge = createEdge({
        sourceNodeId: node.id,
        sourceHandleId: genNodeHandleId(node.id, 'source'),
        targetNodeId: openNodeId!,
        targetHandleId: genNodeHandleId(openNodeId!, 'target'),
      });
    }

    addTempNodes([node]);
    addTempEdges([edge]);

    // if path and option key are passed, update the option
    if (currentPathId && currentPathOptKey) {
      updateConditionalPath({
        pathId: currentPathId,
        newVals: { [currentPathOptKey]: node.id },
        onTransformOptsChange,
      });

      // reset current path and opt key
      onPathIdChange(null);
      onPathOptKeyChange(null);
    }

    onCreatingData(false);
  }, [
    dataType,
    value,
    openNodeId,
    addTempNodes,
    addTempEdges,
    onCreatingData,
    currentPathId,
    currentPathOptKey,
    onTransformOptsChange,
    onPathIdChange,
    onPathOptKeyChange,
  ]);

  return (
    <div className="flex flex-col justify-between">
      <StaticDataCreator
        dataType={dataType}
        onDataTypeChange={setDataType}
        value={value}
        onValueChange={setValue}
      />
      <div className="flex justify-end items-center gap-x-2">
        <Button
          type="secondary"
          label="Cancel"
          onClick={() => {
            onCreatingData(false);
          }}
        />
        <Button
          label="Save and go back to if/then"
          onClick={handleAddStaticData}
          disabled={!dataType || !value}
        />
      </div>
    </div>
  );
}
