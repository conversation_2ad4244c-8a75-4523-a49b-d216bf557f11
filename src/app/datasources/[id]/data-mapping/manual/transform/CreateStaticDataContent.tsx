import { useState, useCallback } from 'react';
import { Button } from '@/components';
import StaticDataCreator from '../StaticDataCreator';
import { SetStateFunction } from '@/types';
import { genStaticNode, autoGenEdges } from '../utils';
import { useMappingStore } from '@/stores';

type Props = {
  onCreatingData: SetStateFunction<boolean>;
};

export default function CreateStaticDataContent(props: Props) {
  const { onCreatingData } = props;

  const [dataType, setDataType] = useState<string>();
  const [value, setValue] = useState<string>();

  const { openNodeId, addNodes, addEdges } = useMappingStore();

  const handleCreateNode = useCallback(() => {
    const node = genStaticNode({ dataType: dataType!, value: value! });
    const edges = autoGenEdges({ selectedNodes: [node], targetNodeId: openNodeId! });

    addNodes([node]);
    addEdges(edges);
    onCreatingData(false);
  }, [dataType, value, openNodeId, addNodes, addEdges, onCreatingData]);

  return (
    <div className="flex flex-col gap-y-6 justify-between">
      <StaticDataCreator
        dataType={dataType}
        onDataTypeChange={setDataType}
        value={value}
        onValueChange={setValue}
      />
      <div className="flex justify-end items-center">
        <Button
          type="secondary"
          label="Cancel"
          onClick={() => {
            onCreatingData(false);
          }}
        />
        <Button
          label="Save and go back to if/then"
          onClick={handleCreateNode}
          disabled={!dataType || !value}
        />
      </div>
    </div>
  );
}
