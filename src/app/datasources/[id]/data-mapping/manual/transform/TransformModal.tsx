import { useState, useEffect, useMemo } from 'react';
import { transforms } from '../../constants';
import { GeneralNode, TransformNode, TransformOptions } from '@/types';
import { Modal, Select } from '@/components';
import TransformSettings from './TransformSettings';
import { getSourceNodes } from '../utils';
import { useMappingStore } from '@/stores';
import TransformModalFooter from './TransformModalFooter';

export default function TransformModal() {
  const {
    nodes: currentNodes,
    edges: currentEdges,
    transformModalOpen,
    setTransformModalOpen,
    openNodeId,
  } = useMappingStore();

  const [transformKey, setTransformKey] = useState<string>();
  const [transformOptions, setTransformOptions] = useState<TransformOptions | null>();

  const currentNode = currentNodes.find((node) => node.id === openNodeId) as
    | TransformNode
    | undefined;

  const sourceNodes = useMemo(
    () =>
      getSourceNodes({
        nodeId: openNodeId || '',
        currentNodes,
        currentEdges,
      }) as GeneralNode[],
    [openNodeId, currentNodes, currentEdges],
  );

  useEffect(() => {
    if (!currentNode) {
      return;
    }

    const { transformKey: initialTransformKey, opts: initialOpts = {} } = currentNode.data.value;

    const defaultNodeOrder = sourceNodes.map((node) => node.id);

    const optsToSet = {
      ...initialOpts,
      sourceNodeOrder: initialOpts.sourceNodeOrder || defaultNodeOrder,
    };

    setTransformKey(initialTransformKey);
    setTransformOptions(optsToSet);
  }, [currentNode, sourceNodes]);

  if (!currentNode || !transformKey || !transformOptions) {
    return null;
  }

  const typeOptions = transforms.map((transform) => ({
    label: transform.label,
    value: transform.key,
  }));

  return (
    <Modal
      open={transformModalOpen}
      setIsOpen={setTransformModalOpen}
      onClose={() => {
        setTransformOptions(null);
      }}
      title="Data transform"
      titleClass="flex gap-2 text-lg text-zinc-900"
      className="w-[650px]"
    >
      <div className="flex flex-col gap-y-6 justify-between">
        <div className="flex flex-col gap-y-4">
          <div className="flex flex-col">
            <span className="text-sm text-zinc-900">Transform type</span>
            <Select
              options={typeOptions}
              value={typeOptions.find((opt) => opt.value === transformKey)}
              onChange={(opt) => {
                setTransformKey(opt.value);
              }}
            />
          </div>
          <TransformSettings
            transformKey={transformKey}
            sourceNodes={sourceNodes}
            opts={transformOptions}
            onOptsChange={setTransformOptions}
          />
        </div>
        <TransformModalFooter
          transformOptions={transformOptions}
          onTransformOptsChange={setTransformOptions}
        />
      </div>
    </Modal>
  );
}
