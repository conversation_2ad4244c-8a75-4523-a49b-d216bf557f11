import { Drag } from '@/components/icons';

type Props = {
  label: string;
  idx: number;
};

export default function OrderListBox(props: Props) {
  const { label, idx } = props;

  return (
    <div className="flex gap-x-4 items-center">
      <span className="text-sm">{idx + 1}.</span>
      <div className="w-full flex items-center p-2 gap-x-4 bg-white rounded-md">
        <Drag className="stroke-zinc-400" />
        <span className="text-zinc-900">{label}</span>
      </div>
    </div>
  );
}
