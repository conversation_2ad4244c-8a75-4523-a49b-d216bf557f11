import { Select } from '@/components';
import { ConditionalPath, SetStateFunction, TransformOptions } from '@/types';
import { getResultNodeOpts, selectFormatOptLabel, genNodeOptSelect } from './utils';

type Props = {
  elsePath: ConditionalPath;
  onPathIdChange: SetStateFunction<string>;
  onPathOptKeyChange: SetStateFunction<keyof ConditionalPath>;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
  onCreatingData: SetStateFunction<boolean>;
};

export default function ElsePath(props: Props) {
  const { elsePath, onPathIdChange, onPathOptKeyChange, onTransformOptsChange, onCreatingData } =
    props;
  const { pathId, resultNodeId } = elsePath;

  const resultOpts = getResultNodeOpts();
  const handleNodeOptSelect = genNodeOptSelect({
    pathId,
    onPathIdChange,
    onPathOptKeyChange,
    onCreatingData,
    onTransformOptsChange,
  });

  return (
    <div className="flex flex-col justify-between p-6 gap-y-4 bg-zinc-100 rounded-md">
      <div className="flex flex-col divide-y-2 divide-zinc-300 divide-dashed">
        <div className="flex items-center gap-x-2 py-4">
          <span>Else:</span>
          <Select
            options={resultOpts}
            value={resultOpts.find((opt) => opt.value.id === resultNodeId)}
            placeholder="Value (Optional)"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'resultNodeId');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
        </div>
      </div>
    </div>
  );
}
