import { useMemo } from 'react';
import { sortBy } from 'lodash';
import { DropResult } from '@hello-pangea/dnd';
import {
  TransformConfig,
  TransformOptions,
  TransformNode,
  MappingNode,
  GeneralNode,
  StaticNode,
  SetStateFunction,
} from '@/types';
import { reorderArray } from '@/utils/data';
import { DraggableList } from '@/components';
import OrderListBox from './OrderListBox';

type Props = {
  transform: TransformConfig;
  sourceNodes: GeneralNode[];
  opts: TransformOptions;
  onOptsChange: SetStateFunction<TransformOptions>;
};

export default function NodeOrderList(props: Props) {
  const { transform, sourceNodes, opts, onOptsChange } = props;
  const { key, config } = transform;
  const { sourceFieldOpts } = config!;
  const { sourceNodeOrder } = opts;

  const items = useMemo(() => {
    const sortedNodes = sortBy(sourceNodes, (node) =>
      sourceNodeOrder?.findIndex((nodeId) => node.id === nodeId),
    );

    return sortedNodes.map((node, idx) => {
      let nodeLabel = '';

      const { nodeKind } = node.data.value;

      if (nodeKind === 'source') {
        const typedNode = node as MappingNode;

        nodeLabel = typedNode.data.value.field.field_name;
      } else if (nodeKind === 'static') {
        const typedNode = node as StaticNode;

        nodeLabel = typedNode.data.value.value;
      } else if (nodeKind === 'transform') {
        const typedNode = node as TransformNode;
        nodeLabel = `${typedNode.data.value.transformKey} output`;
      }

      return {
        key: node.id,
        item: <OrderListBox label={nodeLabel} idx={idx} />,
      };
    });
  }, [sourceNodes, sourceNodeOrder]);

  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    if (!destination) {
      return;
    }

    if (source.index === destination.index && source.droppableId === destination.droppableId) {
      return;
    }

    const newOrder = reorderArray(sourceNodeOrder!, source.index, destination.index);

    onOptsChange((prev) => ({ ...prev, sourceNodeOrder: newOrder }));
  };

  return (
    <div className="flex flex-col gap-y-2">
      <span className="text-sm">{sourceFieldOpts!.label}</span>
      <DraggableList id={key} items={items} onDragEnd={handleDragEnd} />
    </div>
  );
}
