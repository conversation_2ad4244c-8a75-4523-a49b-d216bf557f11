import { useState, useEffect } from 'react';
import { Select } from '@/components';
import { TransformConfig, TransformOptions, SetStateFunction, Option } from '@/types';
import { MenuPlacement } from 'react-select';

type Props = {
  transform: TransformConfig;
  opts: TransformOptions;
  onOptsChange: SetStateFunction<TransformOptions>;
  menuPlacement: MenuPlacement;
};

export default function TransformDropdown(props: Props) {
  const { transform, opts: transformOpts, onOptsChange, menuPlacement } = props;
  const { label, opts: configOpts, optKey } = transform.config!.dropdownOpts!;

  const [value, setValue] = useState<Option>();

  useEffect(() => {
    setValue(configOpts.find((opt) => opt.value === transformOpts[optKey]));
  }, [configOpts, transformOpts, optKey]);

  const handleChange = (opt) => {
    onOptsChange((prev) => ({ ...prev, [optKey]: opt.value }));
  };

  return (
    <div className="flex flex-col gap-y-2">
      <span className="text-sm">{label}</span>
      <Select
        options={configOpts}
        value={value}
        onChange={handleChange}
        menuPlacement={menuPlacement}
      />
    </div>
  );
}
