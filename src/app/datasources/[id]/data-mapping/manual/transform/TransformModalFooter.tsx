import { Button } from '@/components';
import { Trash01 } from '@/components/icons';
import { useMappingStore } from '@/stores';
import { TransformNode, TransformOptions, SetStateFunction } from '@/types';

type Props = {
  transformOptions: TransformOptions;
  onTransformOptsChange: SetStateFunction<TransformOptions | null>;
};

export default function TransformModalFooter(props: Props) {
  const { transformOptions, onTransformOptsChange } = props;
  const {
    editNodeValue,
    deleteNode,
    openNodeId,
    setTransformModalOpen,
    nodes: currentNodes,
  } = useMappingStore();
  const currentNode = currentNodes.find((node) => node.id === openNodeId) as TransformNode;
  const { transformKey } = currentNode.data.value;

  const handleSubmit = () => {
    editNodeValue(openNodeId, { transformKey, opts: transformOptions });

    setTransformModalOpen(false);
    onTransformOptsChange(null);
  };

  const handleDelete = () => {
    deleteNode(currentNode.id);

    setTransformModalOpen(false);
    onTransformOptsChange(null);
  };

  return (
    <div className="flex justify-between">
      <Button
        type="secondary"
        theme="critical"
        label="Delete transform"
        Icon={Trash01}
        onClick={handleDelete}
      />
      <div className="flex items-center gap-x-2">
        <Button
          type="secondary"
          label="Cancel"
          onClick={() => {
            setTransformModalOpen(false);
          }}
        />
        <Button label="Save" onClick={handleSubmit} />
      </div>
    </div>
  );
}
