import { transforms } from '../../constants';

type Props = {
  transformKey: string;
  height?: string;
};

export default function TransformBox(props: Props) {
  const { transformKey, height } = props;

  const transform = transforms.find((transform) => transform.key === transformKey);

  if (!transform) {
    throw new Error(`No transform found for ${transformKey}`);
  }

  const { label, Icon } = transform;

  return (
    <div
      className={`flex flex-row w-full items-center border-2 border-solid border-gray-100 rounded-lg bg-white ${height}`}
    >
      <div className="flex h-full min-w-0 items-center w-full divide-x">
        <div className="w-1/5 h-full" />
        <div className="flex gap-x-2 px-2 items-center w-full h-full">
          <Icon className="h-5 w-5 stroke-zinc-900" />
          <span title={label} className="text-xs font-semibold truncate">
            {label}
          </span>
        </div>
        <div className="w-1/5 h-full" />
      </div>
    </div>
  );
}
