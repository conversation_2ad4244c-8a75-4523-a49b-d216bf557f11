import { DraggableProvidedDragHandleProps } from '@hello-pangea/dnd';
import { Select } from '@/components';
import { DotsDrag, Trash01 } from '@/components/icons';
import { ifThenOperatorOpts } from '../../constants';
import { SetStateFunction, ConditionalPath, TransformOptions } from '@/types';
import {
  updateConditionalPath,
  removeConditionalPath,
  getSourceNodeOpts,
  getResultNodeOpts,
  selectFormatOptLabel,
  genNodeOptSelect,
} from './utils';
import { genLetterTitle } from '@/utils/string';

type Props = {
  path: ConditionalPath;
  onTransformOptsChange: SetStateFunction<TransformOptions>;
  onCreatingData: SetStateFunction<boolean>;
  onPathIdChange: SetStateFunction<string | null>;
  onPathOptKeyChange: SetStateFunction<keyof ConditionalPath | null>;
  dragHandleProps?: DraggableProvidedDragHandleProps;
};

export default function IfThenPath(props: Props) {
  const {
    path,
    onTransformOptsChange,
    onCreatingData,
    onPathIdChange,
    onPathOptKeyChange,
    dragHandleProps,
  } = props;
  const { pathId, sourceNodeId, operator, comparisonNodeId, resultNodeId } = path;

  const sourceOpts = getSourceNodeOpts();
  const resultOpts = getResultNodeOpts();

  const handleNodeOptSelect = genNodeOptSelect({
    pathId,
    onPathIdChange,
    onPathOptKeyChange,
    onCreatingData,
    onTransformOptsChange,
  });

  return (
    <div className="flex flex-col justify-between p-6 gap-y-4 bg-zinc-100 rounded-md">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-x-2">
          <div className="hover:cursor-grab" {...dragHandleProps}>
            <DotsDrag className="stroke-zinc-400" />
          </div>
          <span>Path {genLetterTitle(parseInt(pathId), { is1Based: true })}</span>
        </div>
        <button
          className="flex items-center gap-x-2"
          onClick={() => {
            removeConditionalPath({ pathId, onTransformOptsChange });
          }}
        >
          <Trash01 className="stroke-red-700 w-4 h-4" />
          <span className="text-red-900 text-sm">Delete path</span>
        </button>
      </div>
      <div className="flex flex-col divide-y-2 divide-zinc-300 divide-dashed">
        <div className="flex items-center gap-x-2 py-4">
          <span>If</span>
          <Select
            options={sourceOpts}
            value={sourceOpts.find((opt) => opt.value.id === sourceNodeId)}
            placeholder="Value"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'sourceNodeId');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
          <span>is</span>
          <Select
            options={ifThenOperatorOpts}
            value={ifThenOperatorOpts.find((opt) => opt.value === operator)}
            placeholder="Condition"
            onChange={(opt) => {
              updateConditionalPath({
                pathId,
                newVals: { operator: opt.value },
                onTransformOptsChange,
              });
            }}
            className="w-40"
          />
          <Select
            options={sourceOpts}
            value={sourceOpts.find((opt) => opt.value.id === comparisonNodeId)}
            placeholder="Value"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'comparisonNodeId');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
        </div>
        <div className="flex items-center gap-x-2 py-4">
          <span>Then:</span>
          <Select
            options={resultOpts}
            value={resultOpts.find((opt) => opt.value.id === resultNodeId)}
            placeholder="Value (Optional)"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'resultNodeId');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
        </div>
      </div>
    </div>
  );
}
