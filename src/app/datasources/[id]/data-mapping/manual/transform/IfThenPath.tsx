import { useMemo, use<PERSON>allback, ReactNode } from 'react';
import Image from 'next/image';
import { Select } from '@/components';
import { DotsDrag, Variable } from '@/components/icons';
import { ifThenOperatorOpts } from '../../constants';
import { useMappingStore } from '@/stores';
import { getSourceNodes } from '../utils';
import {
  MappingNode,
  StaticNode,
  MappingNodeValue,
  StaticNodeValue,
  SetStateFunction,
  Option,
  ConditionalPath,
  TransformOptions,
} from '@/types';
import { imgSrc } from 'lib/source';
import { updateArrayItem } from '@/utils/data';

type Props = {
  path: ConditionalPath;
  onTransformOptionsChange: SetStateFunction<TransformOptions>;
  onCreatingData: SetStateFunction<boolean>;
};

const selectFormatOptLabel = (opt) => {
  const { label, value, icon } = opt;

  if (value.id === 'new') {
    return <span className="text-indigo-700 text-sm">{label}</span>;
  }

  return (
    <div className="flex items-center gap-x-2">
      {icon}
      {label}
    </div>
  );
};

export default function IfThenPath(props: Props) {
  const { path, onTransformOptionsChange, onCreatingData } = props;
  const { pathId, sourceNodeId, operator, comparisonNodeId, resultNodeId } = path;

  const { nodes: currentNodes, edges: currentEdges, openNodeId } = useMappingStore();

  const sourceOpts = useMemo(() => {
    const sourceNodes = getSourceNodes({ nodeId: openNodeId!, currentNodes, currentEdges });

    const sourceNodeOpts = sourceNodes.map((node: MappingNode | StaticNode) => {
      const { nodeKind } = node.data.value;

      let sourceImg: ReactNode;
      let label: string = '';

      if (nodeKind === 'source' || nodeKind === 'target') {
        const { field, sourceModelKey } = node.data.value as MappingNodeValue;

        label = field.field_name;

        if (nodeKind === 'source') {
          const { imgPath, imgAlt, dropdownStyle } = imgSrc.getOrDefault(sourceModelKey);

          sourceImg = <Image src={imgPath} alt={imgAlt} {...dropdownStyle} />;
        } else {
          const { imgPath, imgAlt, dropdownStyle } = imgSrc.getOrDefault('gestalt');

          sourceImg = <Image src={imgPath} alt={imgAlt} {...dropdownStyle} />;
        }
      } else {
        // static node value
        const { value } = node.data.value as StaticNodeValue;

        label = value;
        sourceImg = <Variable className="stroke-black w-5 h-5" />;
      }

      return {
        label,
        icon: sourceImg,
        value: node,
      };
    });

    return [...sourceNodeOpts, { label: 'Create static data', value: { id: 'new' } }];
  }, [openNodeId, currentNodes, currentEdges]);

  const updatePath = useCallback(
    (newVals: Partial<ConditionalPath>) => {
      onTransformOptionsChange((prev) => {
        const { conditionalPaths } = prev;

        const currentPathIdx = conditionalPaths!.findIndex((path) => path.pathId === pathId);

        const newPaths = updateArrayItem({
          array: conditionalPaths!,
          itemIdx: currentPathIdx,
          newVals,
        });

        return {
          ...prev,
          conditionalPaths: newPaths,
        };
      });
    },
    [onTransformOptionsChange, pathId],
  );

  const handleNodeOptSelect = useCallback(
    (opt: Option, kind: 'source' | 'comparison' | 'result') => {
      if (opt.value.id === 'new') {
        onCreatingData(true);

        return;
      }

      switch (kind) {
        case 'source':
          updatePath({ sourceNodeId: opt.value.id });
          return;
        case 'comparison':
          updatePath({ comparisonNodeId: opt.value.id });
          return;
        case 'result':
          updatePath({ resultNodeId: opt.value.id });
          return;
      }
    },
    [updatePath, onCreatingData],
  );

  return (
    <div key={pathId} className="flex flex-col justify-between p-6 gap-y-4 bg-zinc-100 rounded-md">
      <div className="flex items-center gap-x-2">
        <DotsDrag className="stroke-zinc-400" />
        <span>Path {String.fromCharCode(96 + parseInt(pathId)).toUpperCase()}</span>
      </div>
      <div className="flex flex-col divide-y-2 divide-zinc-300 divide-dashed">
        <div className="flex items-center gap-x-2 py-4">
          <span>If</span>
          <Select
            options={sourceOpts}
            value={sourceOpts.find((opt) => opt.value.id === sourceNodeId)}
            placeholder="Value"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'source');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
          <span>is</span>
          <Select
            options={ifThenOperatorOpts}
            value={ifThenOperatorOpts.find((opt) => opt.value === operator)}
            placeholder="Condition"
            onChange={(opt) => {
              updatePath({ operator: opt.value });
            }}
            className="w-40"
          />
          <Select
            options={sourceOpts}
            value={sourceOpts.find((opt) => opt.value.id === comparisonNodeId)}
            placeholder="Value"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'comparison');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
        </div>
        <div className="flex items-center gap-x-2 py-4">
          <span>Then:</span>
          <Select
            options={sourceOpts}
            value={sourceOpts.find((opt) => opt.value.id === resultNodeId)}
            placeholder="Value (Optional)"
            onChange={(opt) => {
              handleNodeOptSelect(opt, 'result');
            }}
            className="w-52"
            formatOptionLabel={selectFormatOptLabel}
          />
        </div>
      </div>
    </div>
  );
}
