import { GeneralNode, NodeKind } from '@/types';
import { transforms } from '../../constants';
import { Variable } from '@/components/icons';
import { genTransformNode, genStaticNode } from '../utils';
import { useMappingStore } from '@/stores';

export default function TransformList() {
  const { addNodes } = useMappingStore();

  const handleSelect = (nodeKind: NodeKind, key?: string) => {
    let newNode: GeneralNode;

    switch (nodeKind) {
      case 'transform':
        newNode = genTransformNode({ transformKey: key! });
        break;

      case 'static':
        newNode = genStaticNode();
        break;

      default:
        throw new Error(`No path for ${nodeKind}`);
    }

    addNodes([newNode]);
  };

  return (
    <div className="flex flex-col gap-y-2">
      <div className="bg-white rounded-md border border-zinc-200 divide-y divide-zinc-200">
        {transforms.map(({ key, label, Icon }) => (
          <div
            className="flex px-2 py-1 gap-x-2 hover:cursor-pointer"
            onClick={() => {
              handleSelect('transform', key);
            }}
            key={key}
          >
            <Icon className="stroke-zinc-900 w-4 h-4" />
            <span className="text-zinc-900 text-sm">{label}</span>
          </div>
        ))}
      </div>
      <div className="bg-white rounded-md border border-zinc-200 divide-y divide-zinc-200">
        <div
          className="flex px-2 py-1 gap-x-2 hover:cursor-pointer"
          onClick={() => {
            handleSelect('static');
          }}
        >
          <Variable className="stroke-zinc-900 w-4 h-4" />
          <span className="text-zinc-900 text-sm">Static data</span>
        </div>
      </div>
    </div>
  );
}
