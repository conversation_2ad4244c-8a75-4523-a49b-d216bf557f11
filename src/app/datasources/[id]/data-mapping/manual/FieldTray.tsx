import { useState, useCallback } from 'react';
import { HeaderTabs, InputText, DataFilter } from '@/components';
import { FilterValue } from '@/types';
import { FieldKind, TargetModel, MappingDictionaryField } from '@/types';
import TargetFieldList from './TargetFieldList';
import SourceFieldList from './SourceFieldList';
import { genMappingNode } from './utils';
import { getFilterOptions } from '@/utils/data';
import { useMappingStore, useDataSourceStore } from '@/stores';

type Props = {
  targetModel: TargetModel;
};

export const filterFields = {
  type: 'gen_field_type',
};

export default function FieldTray(props: Props) {
  const { targetModel } = props;

  const { currentDataSource: dataSource } = useDataSourceStore();
  const { nodes, addNodes } = useMappingStore();

  const [selectedHeader, setSelectedHeader] = useState<string>('gestalt');
  const [searchTerm, setSearchTerm] = useState<string>();
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
  });

  const handleFieldSelect = useCallback(
    (nodeKind: FieldKind, field: MappingDictionaryField) => {
      addNodes([
        genMappingNode({
          nodesAtGeneration: nodes,
          nodeKind,
          newField: field,
          sourceModelKey: dataSource!.sourceModelKey,
        }),
      ]);
    },
    [addNodes, nodes, dataSource],
  );

  const headerOpts = [
    { label: 'Gestalt', value: 'gestalt' },
    { label: dataSource!.displayName, value: dataSource!.sourceModelKey },
  ];

  const content =
    selectedHeader === 'gestalt' ? (
      <TargetFieldList
        targetModel={targetModel}
        onSelect={(field: MappingDictionaryField) => handleFieldSelect('target', field)}
        searchTerm={searchTerm}
        filterConfig={filterConfig}
      />
    ) : (
      <SourceFieldList
        onSelect={(field: MappingDictionaryField) => handleFieldSelect('source', field)}
        searchTerm={searchTerm}
        filterConfig={filterConfig}
      />
    );

  return (
    <div className="flex flex-col py-4 rounded-sm h-[calc(100vh-150px)] w-[350px] bg-white relative">
      <span className="mx-4 text-2xl">Fields</span>
      <HeaderTabs opts={headerOpts} selected={selectedHeader} onSelect={setSelectedHeader} />
      <div className="flex flex-col px-4 overflow-auto">
        <div className="flex py-6 gap-x-2 sticky top-0 bg-white z-10">
          <InputText
            icon="SearchMd"
            placeholder="Name"
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <DataFilter
            filterConfig={filterConfig}
            onFilterConfigChange={setFilterConfig}
            filterOptions={getFilterOptions('file')}
            iconOnly
          />
        </div>
        {content}
      </div>
    </div>
  );
}
