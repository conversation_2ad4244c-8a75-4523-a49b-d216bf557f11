import Image from 'next/image';
import { MappingDictionaryField } from '@/types';
import { FieldTypeIcon } from '@/components';

type Props = {
  imgSrc: string;
  imgAlt: string;
  imgStyle?: Record<string, any>;
  field: MappingDictionaryField;
  subField?: string;
  height?: string;
  onSelect?: (field: MappingDictionaryField) => void;
  selected?: boolean;
  title?: string;
};

const baseClass = 'flex flex-row w-full items-center border border-solid rounded-lg bg-white';

export default function MappingFieldBox(props: Props) {
  const {
    imgSrc,
    imgAlt = '',
    imgStyle = {},
    field,
    subField,
    height,
    selected,
    onSelect,
    title,
  } = props;
  const { field_name, gen_field_type } = field;

  let className = `${baseClass} ${height}`;

  if (selected) {
    className = `${className} border-indigo-600`;
  } else {
    className = `${className} border-gray-100`;
  }

  return (
    <div
      className={className}
      onClick={() => {
        if (onSelect) {
          onSelect(field);
        }
      }}
    >
      <div className="flex items-center h-full bg-white shrink-0 border-r rounded-s-xl">
        <div className="p-2">
          <Image src={imgSrc} alt={imgAlt} width={20} height={20} {...imgStyle} />
        </div>
      </div>
      <div className="flex h-full min-w-0 items-center px-2 justify-between w-full">
        <div className="flex flex-col min-w-0 py-2">
          <span title={field_name} className="text-xs font-semibold truncate">
            {field_name}
          </span>
          {subField && (
            <span className="text-[10px] text-gray-400" title={title}>
              {subField}
            </span>
          )}
        </div>
        <div>
          <FieldTypeIcon type={gen_field_type} />
        </div>
      </div>
    </div>
  );
}
