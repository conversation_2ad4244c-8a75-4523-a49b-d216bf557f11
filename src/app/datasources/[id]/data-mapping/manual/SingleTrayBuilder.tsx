import { TargetModel } from '@/types';
import { NodeCanvas } from '@/components';
import FieldTray from './FieldTray';
import TransformList from './transform/TransformList';
import NodeModal from './NodeModal';
import JoinModal from './join/JoinModal';
import { nodeTypes, edgeStyle } from './MappingBuilder';

type Props = {
  currentTargetModel: TargetModel;
};

export default function SingleTrayBuilder(props: Props) {
  const { currentTargetModel } = props;

  return (
    <>
      <div className="flex gap-x-6 h-full">
        <FieldTray targetModel={currentTargetModel} />
        <div className="relative w-full h-full">
          <NodeCanvas nodeTypes={nodeTypes} edgeStyle={edgeStyle} />
          <div className="absolute top-0 left-0">
            <TransformList />
          </div>
        </div>
      </div>
      <NodeModal />
      <JoinModal targetModelId={currentTargetModel?.id} />
    </>
  );
}
