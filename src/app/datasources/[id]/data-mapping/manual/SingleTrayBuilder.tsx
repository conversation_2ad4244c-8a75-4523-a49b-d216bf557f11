import { DataSource, TargetField, SourceFieldsByModel, ListModel } from '@/types';
import { NodeCanvas } from '@/components';
import FieldTray from './FieldTray';
import TransformList from './transform/TransformList';
import NodeModal from './NodeModal';
import JoinModal from './join/JoinModal';
import { nodeTypes, edgeStyle } from './MappingBuilder';

type Props = {
  dataSource: DataSource;
  targetFields: TargetField[];
  sourceFieldsByModel: SourceFieldsByModel;
  currentTargetModel?: ListModel;
};

export default function SingleTrayBuilder(props: Props) {
  const { dataSource, targetFields, sourceFieldsByModel, currentTargetModel } = props;

  return (
    <>
      <div className="flex gap-x-6 h-full">
        <FieldTray
          dataSource={dataSource}
          targetFields={targetFields}
          sourceFieldsByModel={sourceFieldsByModel}
        />
        <div className="relative w-full h-full">
          <NodeCanvas nodeTypes={nodeTypes} edgeStyle={edgeStyle} />
          <div className="absolute top-0 left-0">
            <TransformList />
          </div>
        </div>
      </div>
      <NodeModal />
      <JoinModal
        sourceFieldsByModel={sourceFieldsByModel}
        sourceModelKey={dataSource.sourceModelKey}
        targetModelId={currentTargetModel?.id}
      />
    </>
  );
}
