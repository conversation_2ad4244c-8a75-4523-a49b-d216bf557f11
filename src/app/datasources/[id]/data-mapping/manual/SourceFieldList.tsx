import { SourceFieldsByModel, BaseField, FilterValue } from '@/types';
import SourceFieldModelList from './SourceFieldModelList';
import { orderBy } from 'lodash';

type Props = {
  sourceFieldsByModel: SourceFieldsByModel;
  onSelect: (field: BaseField) => void;
  sourceModelKey: string;
  searchTerm?: string;
  filterConfig: Record<string, FilterValue>;
};

export default function SourceFieldList(props: Props) {
  const { sourceFieldsByModel, onSelect, sourceModelKey, searchTerm, filterConfig } = props;

  const orderedSourceModels = orderBy(sourceFieldsByModel, 'modelName', 'asc');

  return (
    <div className="flex flex-col gap-y-6">
      {orderedSourceModels?.map(({ modelName, dbtName, fields }, idx) => (
        <SourceFieldModelList
          modelName={modelName}
          dbtName={dbtName}
          fields={fields}
          onSelect={onSelect}
          sourceModelKey={sourceModelKey}
          searchTerm={searchTerm}
          filterConfig={filterConfig}
          key={`${dbtName}${idx}`}
        />
      ))}
    </div>
  );
}
