import { MappingDictionaryField, FilterValue } from '@/types';
import SourceFieldModelList from './SourceFieldModelList';
import { useDataSourceStore, useDictionaryStore } from '@/stores';

type Props = {
  onSelect: (field: MappingDictionaryField) => void;
  searchTerm?: string;
  filterConfig: Record<string, FilterValue>;
};

export default function SourceFieldList(props: Props) {
  const { onSelect, searchTerm, filterConfig } = props;
  const { currentDataSource: dataSource } = useDataSourceStore();
  const { sourceModels } = useDictionaryStore();

  if (!dataSource) {
    return null;
  }

  return (
    <div className="flex flex-col gap-y-6">
      {sourceModels?.map(({ id, name, dbtName, dictionary }, idx) => (
        <SourceFieldModelList
          modelId={id}
          modelName={name}
          dbtName={dbtName}
          fields={dictionary}
          onSelect={onSelect}
          sourceModelKey={dataSource.sourceModelKey}
          searchTerm={searchTerm}
          filterConfig={filterConfig}
          key={`${dbtName}${idx}`}
        />
      ))}
    </div>
  );
}
