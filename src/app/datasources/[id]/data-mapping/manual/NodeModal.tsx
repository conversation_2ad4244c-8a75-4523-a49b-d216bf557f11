import IfThenModal from './transform/IfThenModal';
import TransformModal from './transform/TransformModal';
import StaticModal from './StaticModal';
import { useMappingStore } from '@/stores';

export default function NodeModal() {
  const { nodes: currentNodes, openNodeId } = useMappingStore();

  const currentNode = currentNodes.find((node) => node.id === openNodeId);

  if (!currentNode) {
    return null;
  }

  const nodeValue = currentNode.data.value;
  const { nodeKind } = nodeValue;

  switch (nodeKind) {
    case 'static':
      return <StaticModal />;
    case 'transform':
      return <TransformModal />;
    case 'ifThen':
      return <IfThenModal />;
  }
}
