import IfThenModal from './transform/IfThenModal';
import TransformModal from './transform/TransformModal';
import StaticModal from './StaticModal';
import { useMappingStore } from '@/stores';
import { TransformNodeValue } from '@/types';

export default function NodeModal() {
  const { nodes: currentNodes, openNodeId } = useMappingStore();

  const currentNode = currentNodes.find((node) => node.id === openNodeId);

  if (!currentNode) {
    return;
  }

  const nodeValue = currentNode.data.value;
  const { nodeKind } = nodeValue;

  if (nodeKind === 'transform') {
    const { transformKey } = nodeValue as TransformNodeValue;

    if (transformKey === 'ifThen') {
      return <IfThenModal />;
    }

    return <TransformModal />;
  }

  return <StaticModal />;
}
