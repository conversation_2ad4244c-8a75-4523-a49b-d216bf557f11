import TransformModal from './transform/TransformModal';
import StaticModal from './StaticModal';
import { useMappingStore } from '@/stores';

export default function NodeModal() {
  const { nodes: currentNodes, openNodeId } = useMappingStore();

  const currentNode = currentNodes.find((node) => node.id === openNodeId);

  if (!currentNode) {
    return;
  }

  const { nodeKind } = currentNode.data.value;

  switch (nodeKind) {
    case 'transform':
      return <TransformModal />;
    case 'static':
      return <StaticModal />;
  }
}
