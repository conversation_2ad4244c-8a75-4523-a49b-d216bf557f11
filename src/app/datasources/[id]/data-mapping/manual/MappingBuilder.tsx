import { useState, useEffect, useC<PERSON>back, useMemo, ReactNode } from 'react';
import { findIndex } from 'lodash';
import { useReward } from 'react-rewards';
import { useParams } from 'next/navigation';
import {
  DataSource,
  TargetField,
  SourceFieldsByModel,
  EdgeStyleProps,
  GenerateMappingContractProps,
  ListModel,
  MappingMatch,
} from '@/types';
import { MapperGetTargetFields, MapperGetSourceFields, GetListTargetModels } from '@/types/routes';
import { Loading, PageTitle } from '@/components';
import { MappingNode, TransformNode, StaticNode } from './nodes';
import { api } from '@/helpers/web';
import { useCustomer, usePage } from '@/hooks';
import { genDeliveryNode, genDeliveryEdge, genConnectionsFromAutoMappings } from './utils';
import SingleTrayBuilder from './SingleTrayBuilder';
import DualTrayBuilder from './DualTrayBuilder';
import CanvasOptions from './CanvasOptions';
import { useMappingStore } from '@/stores';
import { orderBy } from 'lodash';

export type Props = {
  dataSource: DataSource;
  autoMappings: MappingMatch[];
};

type Params = {
  id: string;
};

export const SOURCE_NODE_X_POS = 0;
export const TARGET_NODE_X_POS = 600;
export const TRANSFORM_NODE_X_POS = 300;
export const VERT_NODE_SPACE = 75;
export const START_Y_POS = 225;

export const nodeTypes = {
  mapping: MappingNode,
  transform: TransformNode,
  static: StaticNode,
};

export const edgeStyle: EdgeStyleProps = {
  type: 'step',
  style: { stroke: '#10B981' },
};

export default function MappingBuilder(props: Props) {
  const { dataSource, autoMappings } = props;
  const { sourceModelKey } = dataSource;

  const [listTargetModels, setListTargetModels] = useState<ListModel[]>([]);
  const [currentTargetModel, setCurrentTargetModel] = useState<ListModel>();
  const [targetFields, setTargetFields] = useState<TargetField[]>();
  const [sourceFieldsByModel, setSourceFieldsByModel] = useState<SourceFieldsByModel>();
  const [completedTargetModels, setCompletedTargetModels] = useState<ListModel[]>([]);
  const [displayCompletedContent, setDisplayCompletedContent] = useState<boolean>(false);
  const [dualTrays, setDualTrays] = useState<boolean>(false);

  const { modelJoins, reset, nodes, edges, addNodes, addEdges } = useMappingStore();

  const { setPageProps } = usePage();
  const { customerId, customerKey } = useCustomer();
  const { id: dataSourceId } = useParams() as Params;
  const { reward } = useReward('rewardId', 'confetti', {
    fps: 30,
    startVelocity: 25,
    spread: 75,
    elementCount: 100,
    angle: 210,
  });

  // reset store mapping data
  useEffect(() => {
    reset();
  }, [reset]);

  // get list of target models
  useEffect(() => {
    const queryProps: GetListTargetModels = { customerId };

    const getModelNames = async () => {
      const listModels = await api.get('/api/dataDictionary/getListTargetModels', queryProps);

      const ordered = orderBy(listModels, 'name');

      setListTargetModels(ordered);
      setCurrentTargetModel(ordered[0]);
    };

    getModelNames();
  }, [customerId]);

  useEffect(() => {
    setPageProps({ sidebarOpen: false });
  }, [setPageProps]);

  const getTargetFields = useCallback(async () => {
    if (!currentTargetModel) {
      return;
    }

    const targetFieldsProps: MapperGetTargetFields = {
      modelId: currentTargetModel.id,
    };

    const fields = await api.get('/api/mapper/getTargetFields', targetFieldsProps);

    setTargetFields(fields);
  }, [currentTargetModel]);

  const getSourceFields = useCallback(async () => {
    const sourceFieldsProps: MapperGetSourceFields = {
      origin: sourceModelKey,
    };

    const fields = await api.get('/api/mapper/getSourceFields', sourceFieldsProps);

    setSourceFieldsByModel(fields);
  }, [sourceModelKey]);

  useEffect(() => {
    getTargetFields();
  }, [getTargetFields]);

  useEffect(() => {
    getSourceFields();
  }, [getSourceFields]);

  const modelAutoMappings = useMemo(
    () =>
      autoMappings.filter(
        (autoMapping) =>
          autoMapping.target_model_name.toLowerCase() === currentTargetModel?.name?.toLowerCase(),
      ),
    [autoMappings, currentTargetModel],
  );

  useEffect(() => {
    const acceptedModelAutoMappings = modelAutoMappings.filter(
      (autoMapping) => autoMapping.review_status === 'accepted',
    );

    if (acceptedModelAutoMappings.length) {
      const { nodes: autoNodes, edges: autoEdges } = genConnectionsFromAutoMappings({
        autoMappings: acceptedModelAutoMappings,
        sourceModelKey: sourceModelKey,
      });

      addNodes(autoNodes);
      addEdges(autoEdges);
    }
  }, [modelAutoMappings, sourceModelKey, addNodes, addEdges]);

  const targetModelOptions = listTargetModels?.map((model) => ({
    label: model.name,
    value: model.id,
  }));

  const currentModelCompleted = !!completedTargetModels.find(
    (model) => model.id === currentTargetModel?.id,
  );

  const handleCompleteModel = async () => {
    const body: GenerateMappingContractProps = {
      targetModelName: currentTargetModel!.name,
      targetModelId: currentTargetModel!.id,
      dataSourceKey: sourceModelKey,
      customerKey,
      customerDataSourceId: dataSourceId,
      nodes: nodes.map((node) => genDeliveryNode(node)),
      edges: edges.map((edge) => genDeliveryEdge(edge)),
      joins: modelJoins,
    };

    await api.workerPost({ service: 'mapper:generateMappingContract', body, customerKey });

    setCompletedTargetModels((prev) => [...prev, currentTargetModel!]);
    setDisplayCompletedContent(true);
    reward();

    // once all target models have been mapped and saved, we update the data source status to complete.
    // Still an open question how to handle intermediate states.
    const allModelsComplete = listTargetModels.every(
      (model) =>
        completedTargetModels.find((completedModel) => model.id === completedModel.id) ||
        model.id === currentTargetModel!.id,
    );

    if (allModelsComplete) {
      await api.post('/api/mapper/updateDataSourceMappingStatus', {
        dataSourceId: dataSource.id,
        mappingStatus: 'complete',
      });
    }
  };

  const handleModelNext = () => {
    const currentModelIdx = findIndex(
      listTargetModels,
      (model) => model.id === currentTargetModel!.id,
    );

    const nextModelRef = listTargetModels[currentModelIdx + 1];

    if (!nextModelRef) {
      return;
    }

    setCurrentTargetModel({ ...nextModelRef });
    reset();
    setDisplayCompletedContent(false);
  };

  if (!targetFields?.length || !sourceFieldsByModel?.length) {
    return <Loading />;
  }

  let content: ReactNode | null = null;

  if (dualTrays) {
    content = (
      <DualTrayBuilder
        dataSource={dataSource}
        targetFields={targetFields}
        sourceFieldsByModel={sourceFieldsByModel}
        currentTargetModel={currentTargetModel}
      />
    );
  } else {
    content = (
      <SingleTrayBuilder
        dataSource={dataSource}
        targetFields={targetFields}
        sourceFieldsByModel={sourceFieldsByModel}
        currentTargetModel={currentTargetModel}
      />
    );
  }

  return (
    <>
      <PageTitle
        title={dataSource.displayName}
        subpage
        breadcrumbs={[
          { label: 'Data sources', url: '/' },
          {
            label: `${dataSource.displayName}`,
            url: `/datasources/${dataSource.id}/data-mapping`,
          },
          {
            label: `Data Mapping`,
            url: `/datasources/${dataSource.id}/data-mapping`,
          },
        ]}
      >
        <CanvasOptions
          targetModelOptions={targetModelOptions}
          currentModelCompleted={currentModelCompleted}
          currentTargetModel={currentTargetModel}
          onCurrentTargetModelChange={setCurrentTargetModel}
          completedTargetModels={completedTargetModels}
          handleCompleteModel={handleCompleteModel}
          displayCompletedContent={displayCompletedContent}
          handleModelNext={handleModelNext}
          dualTrays={dualTrays}
          onDualTrays={setDualTrays}
          dataSource={dataSource}
          targetFields={targetFields}
        />
      </PageTitle>
      {content}
    </>
  );
}
