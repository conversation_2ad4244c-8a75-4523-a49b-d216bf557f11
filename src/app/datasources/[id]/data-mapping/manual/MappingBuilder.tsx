import { useState, useEffect, useMemo, ReactNode } from 'react';
import { findIndex } from 'lodash';
import { useReward } from 'react-rewards';
import { useParams } from 'next/navigation';
import { TargetModel, EdgeStyleProps, GenerateMappingContractProps, MappingMatch } from '@/types';
import { Loading, PageTitle } from '@/components';
import { MappingNode, TransformNode, StaticNode, IfThenNode } from './nodes';
import { api } from '@/helpers/web';
import { useCustomer, usePage } from '@/hooks';
import { genDeliveryNode, genDeliveryEdge, genConnectionsFromAutoMappings } from './utils';
import SingleTrayBuilder from './SingleTrayBuilder';
import DualTrayBuilder from './DualTrayBuilder';
import CanvasOptions from './CanvasOptions';
import { useMappingStore, useDictionaryStore, useDataSourceStore } from '@/stores';

export type Props = {
  autoMappings: MappingMatch[];
};

type Params = {
  id: string;
};

export const SOURCE_NODE_X_POS = 0;
export const TARGET_NODE_X_POS = 600;
export const TRANSFORM_NODE_X_POS = 300;
export const VERT_NODE_SPACE = 75;
export const START_Y_POS = 225;

export const nodeTypes = {
  mapping: MappingNode,
  transform: TransformNode,
  ifThen: IfThenNode,
  static: StaticNode,
};

export const edgeStyle: EdgeStyleProps = {
  type: 'step',
  style: { stroke: '#10B981' },
};

export default function MappingBuilder(props: Props) {
  const { autoMappings } = props;

  const [completedTargetModelIds, setCompletedTargetModelIds] = useState<string[]>([]);
  const [displayCompletedContent, setDisplayCompletedContent] = useState<boolean>(false);
  const [dualTrays, setDualTrays] = useState<boolean>(false);
  const [currentTargetModel, setCurrentTargetModel] = useState<TargetModel>();

  const { modelJoins, reset, nodes, edges, addNodes, addEdges } = useMappingStore();
  const { targetModels } = useDictionaryStore();
  const { currentDataSource: dataSource } = useDataSourceStore();

  const { setPageProps } = usePage();
  const { customerKey } = useCustomer();
  const { id: dataSourceId } = useParams() as Params;
  const { reward } = useReward('rewardId', 'confetti', {
    fps: 30,
    startVelocity: 25,
    spread: 75,
    elementCount: 100,
    angle: 210,
  });

  // reset store mapping data
  useEffect(() => {
    reset();
  }, [reset]);

  useEffect(() => {
    setPageProps({ sidebarOpen: false });
  }, [setPageProps]);

  useEffect(() => {
    if (!targetModels) {
      return;
    }

    setCurrentTargetModel(targetModels[0]);
  }, [targetModels]);

  const modelAutoMappings = useMemo(
    () =>
      autoMappings.filter(
        (autoMapping) =>
          autoMapping.target_model_name.toLowerCase() === currentTargetModel?.name?.toLowerCase(),
      ),
    [autoMappings, currentTargetModel],
  );

  useEffect(() => {
    if (!dataSource) {
      return;
    }

    const acceptedModelAutoMappings = modelAutoMappings.filter(
      (autoMapping) => autoMapping.review_status === 'accepted',
    );

    if (acceptedModelAutoMappings.length) {
      const { nodes: autoNodes, edges: autoEdges } = genConnectionsFromAutoMappings({
        autoMappings: acceptedModelAutoMappings,
        sourceModelKey: dataSource.sourceModelKey,
      });

      addNodes(autoNodes);
      addEdges(autoEdges);
    }
  }, [modelAutoMappings, dataSource, addNodes, addEdges]);

  const targetModelOptions =
    targetModels?.map((model) => ({
      label: model.name,
      value: model,
    })) || [];

  const currentModelCompleted = !!completedTargetModelIds.find(
    (id) => id === currentTargetModel?.id,
  );

  const handleCompleteModel = async () => {
    const body: GenerateMappingContractProps = {
      targetModelName: currentTargetModel!.name,
      targetModelId: currentTargetModel!.id,
      dataSourceKey: dataSource!.sourceModelKey,
      customerKey,
      customerDataSourceId: dataSourceId,
      nodes: nodes.map((node) => genDeliveryNode(node)),
      edges: edges.map((edge) => genDeliveryEdge(edge)),
      joins: modelJoins,
    };

    await api.workerPost({ service: 'mapper:generateMappingContract', body, customerKey });

    setCompletedTargetModelIds((prev) => [...prev, currentTargetModel!.id]);
    setDisplayCompletedContent(true);
    reward();

    // once all target models have been mapped and saved, we update the data source status to complete.
    // Still an open question how to handle intermediate states.
    const allModelsComplete = targetModels?.every(
      (model) =>
        completedTargetModelIds.find((completedModelId) => model.id === completedModelId) ||
        model.id === currentTargetModel!.id,
    );

    if (allModelsComplete) {
      await api.post('/api/mapper/updateDataSourceMappingStatus', {
        dataSourceId: dataSource!.id,
        mappingStatus: 'complete',
      });
    }
  };

  const handleModelNext = () => {
    const currentModelIdx = findIndex(targetModels, (model) => model.id === currentTargetModel!.id);

    const nextModelRef = targetModels?.[currentModelIdx + 1];

    if (!nextModelRef) {
      return;
    }

    setCurrentTargetModel({ ...nextModelRef });
    reset();
    setDisplayCompletedContent(false);
  };

  if (!dataSource || !currentTargetModel) {
    return <Loading />;
  }

  let content: ReactNode | null = null;

  if (dualTrays) {
    content = <DualTrayBuilder currentTargetModel={currentTargetModel} />;
  } else {
    content = <SingleTrayBuilder currentTargetModel={currentTargetModel} />;
  }

  return (
    <>
      <PageTitle
        title={dataSource.displayName}
        subpage
        breadcrumbs={[
          { label: 'Data sources', url: '/' },
          {
            label: `${dataSource.displayName}`,
            url: `/datasources/${dataSource.id}/data-mapping`,
          },
          {
            label: `Data Mapping`,
            url: `/datasources/${dataSource.id}/data-mapping`,
          },
        ]}
      >
        <CanvasOptions
          targetModelOptions={targetModelOptions}
          currentModelCompleted={currentModelCompleted}
          currentTargetModel={currentTargetModel}
          onCurrentTargetModelChange={setCurrentTargetModel}
          completedTargetModelIds={completedTargetModelIds}
          handleCompleteModel={handleCompleteModel}
          displayCompletedContent={displayCompletedContent}
          handleModelNext={handleModelNext}
          dualTrays={dualTrays}
          onDualTrays={setDualTrays}
        />
      </PageTitle>
      {content}
    </>
  );
}
