import Image from 'next/image';
import { BaseField } from '@/types';
import { Check } from '@/components/icons';

type Props = {
  imgSrc: string;
  imgAlt: string;
  field: BaseField;
  subField?: string;
  height?: string;
  selected?: boolean;
};

const baseClass = 'flex flex-row w-full items-center rounded-lg bg-white';

export default function JoinFieldBox(props: Props) {
  const { imgSrc, imgAlt = '', field, subField, height, selected } = props;
  const { field_name } = field;

  let className = `${baseClass} ${height}`;

  if (selected) {
    className = `${className} border-2 border-indigo-600`;
  } else {
    className = `${className} border border-gray-100`;
  }

  return (
    <div className={className}>
      <div className="flex items-center h-full bg-white shrink-0 border-r rounded-s-xl">
        <div className="p-2">
          <Image src={imgSrc} alt={imgAlt} width={20} height={20} />
        </div>
      </div>
      <div className="flex h-full min-w-0 items-center px-2 justify-between gap-x-4 w-full">
        <div className="flex flex-col min-w-0 py-2">
          <span title={field_name} className="text-xs font-semibold truncate">
            {field_name}
          </span>
          {subField && <span className="text-[10px] text-gray-400">{subField}</span>}
        </div>
        {selected && <Check className="stroke-4 stroke-indigo-600" />}
      </div>
    </div>
  );
}
