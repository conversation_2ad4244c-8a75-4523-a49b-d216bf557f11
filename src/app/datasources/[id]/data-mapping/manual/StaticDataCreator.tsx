import { dataTypeOpts } from '../constants';
import { Select, FieldTypeIcon, InputText } from '@/components';
import { findGeneralType } from '@/utils/data';
import { SetStateFunction } from '@/types';

type Props = {
  dataType?: string | null;
  onDataTypeChange: SetStateFunction<string>;
  value?: string | null;
  onValueChange: SetStateFunction<string>;
};

export default function StaticDataCreator(props: Props) {
  const { dataType, onDataTypeChange, value, onValueChange } = props;

  return (
    <div className="flex flex-col gap-y-8">
      <div className="flex flex-col">
        <span className="text-sm">Data type</span>
        <div className="flex flex-col">
          <Select
            options={dataTypeOpts}
            value={dataTypeOpts.find((opt) => opt.value === dataType)}
            placeholder="Data type"
            onChange={(opt) => {
              onDataTypeChange(opt.value);
            }}
            formatOptionLabel={({ label, value }) => {
              return (
                <div className="flex items-center gap-x-2">
                  <FieldTypeIcon type={findGeneralType(value)} />
                  <span className="text-sm text-zinc-900">{label}</span>
                </div>
              );
            }}
          />
        </div>
      </div>
      <div className="flex flex-col">
        <span className="text-sm">Static value</span>
        <InputText
          value={value}
          placeholder="Value"
          onChange={(e) => onValueChange(e.target.value)}
          noIcon
          className="w-[320px]"
        />
      </div>
    </div>
  );
}
