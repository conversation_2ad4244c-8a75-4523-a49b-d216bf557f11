import { FieldTypeIcon } from '@/components';
import { findGeneralType } from '@/utils/data';

type Props = {
  dataType?: string;
  value?: any;
};

export default function StaticBox(props: Props) {
  const { dataType, value } = props;

  return (
    <div
      className={`flex justify-between w-full h-12 items-center border-2 border-solid border-gray-100 rounded-lg bg-white`}
    >
      <div className="flex h-full min-w-0 items-center justify-between gap-x-4 w-full">
        <div className="flex flex-col min-w-0 px-4 py-2">
          <span title={value} className="text-xs font-semibold truncate">
            {value}
          </span>
        </div>
        <div className="flex justify-center items-center w-1/4 h-12 border-l p-2">
          <FieldTypeIcon type={findGeneralType(dataType || '')} />
        </div>
      </div>
    </div>
  );
}
