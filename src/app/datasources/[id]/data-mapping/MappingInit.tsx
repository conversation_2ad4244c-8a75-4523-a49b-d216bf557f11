import { useState, ReactNode } from 'react';
import { map } from 'lodash';
import {
  Card,
  Button,
  LottiePlayer,
  ToggleOptions as ToggleOptionsComponent,
  PageTitle,
} from '@/components';
import { ThumbsUp, QuestionCircle, XCircle } from '@/components/icons';
import { MappingMatch, SetStateFunction, ToggleOptions, DataSource } from '@/types';
import searchingJson from 'public/lottie/searching.json';

const confidenceContent = {
  confident: {
    Icon: ThumbsUp,
    iconBg: 'bg-emerald-300',
    iconColor: 'stroke-emerald-700',
    message: 'confident mappings',
  },
  possible: {
    Icon: QuestionCircle,
    iconBg: 'bg-yellow-300',
    iconColor: 'stroke-yellow-700',
    message: 'possible mappings',
  },
  unmatched: {
    Icon: XCircle,
    iconBg: 'bg-zinc-300',
    iconColor: 'stroke-zinc-500',
    message: 'with no mapping',
  },
};

export type Props = {
  dataSource: DataSource;
  onNext: () => void;
  confidentMappings: MappingMatch[];
  possibleMappings: MappingMatch[];
  mappingsOptions: ToggleOptions;
  onMappingsOptionsChange: SetStateFunction<ToggleOptions>;
};

export default function MappingInit(props: Props) {
  const {
    dataSource,
    onNext,
    confidentMappings,
    possibleMappings,
    mappingsOptions,
    onMappingsOptionsChange,
  } = props;

  const { displayName } = dataSource;

  const [mapperProcessing] = useState<boolean>(false);

  const matchCounts: Record<string, number> = {
    confident: confidentMappings.length,
    possible: possibleMappings.length,
  };

  let content: ReactNode | null = null;

  if (mapperProcessing) {
    content = (
      <Card>
        <div className="w-1/2 h-1/2 mb-12 flex justify-between items-center">
          <LottiePlayer src={searchingJson} />
        </div>
        <div className="flex flex-col w-2/3 items-center gap-y-2">
          <span className="text-zinc-700 text-2xl text-center">
            Let’s start by having our smart mapper automatically map the bulk of {displayName}{' '}
            fields to Gestalt’s data model.
          </span>
          <span className="text-zinc-500">This should only take a moment.</span>
        </div>
      </Card>
    );
  } else {
    content = (
      <Card>
        <div className="relative w-full h-full justify-center items-center">
          <div className="absolute top-0 right-0">
            <ToggleOptionsComponent
              options={mappingsOptions}
              onOptionsChange={onMappingsOptionsChange}
            />
          </div>
          <div className="flex h-full flex-col items-center justify-center">
            <div className="space-y-2 mb-8 text-center w-1/2">
              <p className="text-2xl text-zinc-700">
                Great news! We were able to successfully map the bulk of the {displayName} data.
              </p>
              <p className="text-zinc-500">
                On the next step we’ll comb through the less confident mappings. You’ll have a
                chance to review all of the mappings before completion.
              </p>
            </div>
            <div className="flex flex-col space-y-4 mb-6">
              {map(matchCounts, (count, confidence) => {
                const { Icon, iconBg, iconColor, message } = confidenceContent[confidence];

                return (
                  <div
                    className="flex bg-zinc-50 w-80 h-20 rounded-xl items-center px-4 gap-x-4"
                    key={confidence}
                  >
                    <div
                      className={`w-10 h-10 flex rounded-full justify-center items-center p-2 ${iconBg} mr-4`}
                    >
                      <Icon className={`w-8 h-8 ${iconColor}`} />
                    </div>
                    <span>
                      {count} {message}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="flex justify-end gap-x-4 absolute bottom-0 right-0">
            <Button label="Next" type="primary" onClick={onNext} />
          </div>
        </div>
      </Card>
    );
  }

  return (
    <>
      <PageTitle
        title={dataSource.displayName}
        subpage
        breadcrumbs={[
          { label: 'Data sources', url: '/' },
          {
            label: `${dataSource.displayName}`,
            url: `/datasources/${dataSource.id}/data-mapping`,
          },
          {
            label: `Data Mapping`,
            url: `/datasources/${dataSource.id}/data-mapping`,
          },
        ]}
      />
      {content}
    </>
  );
}
