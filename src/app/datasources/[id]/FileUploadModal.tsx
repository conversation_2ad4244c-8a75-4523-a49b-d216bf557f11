import { Modal, FileUpload, Button, Progress } from '@/components';
import { SetStateFunction } from '@/types';

type Props = {
  allowedTypes?: string[];
  submitting: boolean;
  uploadCompletion: number;
  isOpen: boolean;
  onOpenChange: SetStateFunction<boolean>;
  files: File[];
  onFilesChange: SetStateFunction<any[]>;
  multiple?: boolean;
  handleSubmit: () => Promise<void>;
};

export default function FileUploadModal(props: Props) {
  const {
    allowedTypes,
    submitting,
    uploadCompletion,
    isOpen,
    onOpenChange,
    multiple,
    files,
    onFilesChange,
    handleSubmit,
  } = props;

  return (
    <Modal
      title={
        <div className="flex gap-2 items-center text-lg">
          <h2>Upload file</h2>
        </div>
      }
      open={isOpen}
      setIsOpen={onOpenChange}
      titleUnderline={true}
      className="w-[800px] h-[500px] overflow-scroll"
    >
      <div className="flex flex-col justify-center items-center w-full h-full gap-y-6">
        <FileUpload
          allowedTypes={allowedTypes}
          multiple={multiple}
          files={files}
          onFilesChange={onFilesChange}
        />
        <Button
          onClick={handleSubmit}
          label={submitting ? 'Loading data...' : 'Submit'}
          disabled={!files.length || submitting}
        />
        {submitting && <Progress completed={uploadCompletion} />}
      </div>
    </Modal>
  );
}
