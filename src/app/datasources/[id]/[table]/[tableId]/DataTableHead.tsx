import { SortArrow } from '@/components';
import { SortConfig, SetStateFunction } from '@/types';
import { findGeneralType, handleSort } from '@/utils/data';

type Props = {
  onSort: SetStateFunction<SortConfig>;
  connectorDisplayName: string;
  sortConfig: SortConfig | null;
};

export default function DataTableHead(props: Props) {
  const { onSort, connectorDisplayName, sortConfig } = props;

  return (
    <thead className="bg-zinc-50 border-b border-zinc-200">
      <tr>
        <th
          onClick={() =>
            handleSort(
              { colName: 'sourceCol', keyAccessor: (col) => col.field_name || col.source_field },
              onSort,
            )
          }
          className="px-6 py-3 flex justify-start items-center gap-0.5 text-xs font-semibold text-zinc-500 hover:bg-zinc-100 cursor-pointer stroke-zinc-900 whitespace-nowrap"
        >
          {connectorDisplayName} field
          <SortArrow colName="sourceCol" sortConfig={sortConfig} />
        </th>
        <th className="px-6 py-3 text-xs font-semibold text-zinc-500"></th>
        <th
          onClick={() =>
            handleSort(
              {
                colName: 'gestaltCol',
                keyAccessor: (col) => col.target_field || col.mappings?.[0]?.target_field,
                asc: false,
              },
              onSort,
            )
          }
          className="p-6 py-3 flex justify-start items-center gap-0.5 text-xs font-semibold text-zinc-500 hover:bg-zinc-100 cursor-pointer stroke-zinc-900 whitespace-nowrap"
        >
          Gestalt field
          <SortArrow colName="gestaltCol" sortConfig={sortConfig} />
        </th>
        <th
          onClick={() =>
            handleSort(
              { colName: 'type', keyAccessor: (col) => findGeneralType(col.field_type) },
              onSort,
            )
          }
          className="align-middle px-6 py-3 text-xs font-semibold text-zinc-500 hover:bg-zinc-100 cursor-pointer stroke-zinc-900 whitespace-nowrap"
        >
          Type
          <SortArrow colName="type" sortConfig={sortConfig} />
        </th>
        <th
          onClick={() => {}}
          className="px-6 py-3 flex justify-start items-center gap-0.5 text-xs font-semibold text-zinc-500 hover:bg-zinc-100 cursor-pointer stroke-zinc-900 whitespace-nowrap"
        >
          Last changed
          <SortArrow colName="lastChanged" sortConfig={sortConfig} />
        </th>
        <th className="px-6 py-3 text-xs font-semibold text-zinc-500">
          {/* Purposefully empty */}
        </th>
      </tr>
    </thead>
  );
}
