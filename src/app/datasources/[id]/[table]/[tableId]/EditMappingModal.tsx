// src/app/datasources/[id]/[name]/[table]/EditMappingModal.tsx
import { imgSrc } from '@lib/source';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Modal, SegmentedControl, DataLabel, Spinner, Button } from '@/components';
import SearchIcon from '@components/icons/SearchMd';
import CheckIcon from '@components/icons/Check';
import { useCustomer } from '@/hooks';
import { SetStateFunction, TargetField, Mapping, ModelSummary } from '@/types';
import { EditedMapping } from './page';

type Props = {
  openColumn?: Mapping;
  isEditModalOpen: boolean;
  setIsEditModalOpen: (isOpen: boolean) => void;
  connectorType: string;
  connector: Record<string, any>;
  allFields: TargetField[];
  setAllFields: SetStateFunction<TargetField[]>;
  onEditedFieldsChange: SetStateFunction<EditedMapping[]>;
  targetFieldsLoading: boolean;
  currentTable?: ModelSummary;
};

export default function EditMappingModal(props: Props) {
  const {
    openColumn,
    isEditModalOpen,
    setIsEditModalOpen,
    connectorType,
    connector,
    allFields,
    setAllFields,
    onEditedFieldsChange,
    targetFieldsLoading,
    currentTable,
  } = props;

  const { customerKey } = useCustomer();
  const [filteredFields, setFilteredFields] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedField, setSelectedField] = useState<{
    field_name: string;
    model_name: string;
    model_id: string;
    sources?: any[];
  } | null>(null);
  const [selectedSegment, setSelectedSegment] = useState('Direct mapping');
  const [openColTargetFieldIdx, setOpenColTargetFieldIdx] = useState(-1);
  const [openColTargetField, setOpenColTargetField] = useState<any>();

  useEffect(() => {
    if (openColumn?.target_field && openColumn?.target_model) {
      const targetFieldIdx = allFields.findIndex(
        (field) =>
          field.field_name.toLowerCase() === openColumn.target_field.toLowerCase() &&
          field.model_name.toLowerCase() === openColumn.target_model.toLowerCase(),
      );

      setOpenColTargetFieldIdx(targetFieldIdx);
      setOpenColTargetField(allFields[targetFieldIdx]);
    }
  }, [openColumn, allFields]);

  useEffect(() => {
    // break early if no customer key loaded yet
    if (!customerKey || !openColumn || Object.keys(openColumn).length === 0) {
      return;
    }

    // If the openColumn has a value for target_field and target_model
    if (openColumn && openColumn.target_field && openColumn.target_model) {
      if (openColumn.operation !== 'COPY') {
        setSelectedSegment('Data transform');
      } else {
        setSelectedSegment('Direct mapping');
      }

      // If a match is found, bring it to the beginning of the array, if match has multiple sources set the selectedSegment to Data transform
      if (openColTargetField) {
        setAllFields((prev) => [
          openColTargetField,
          ...prev.filter((_, idx) => idx !== openColTargetFieldIdx),
        ]);

        if (openColTargetField.sources && openColTargetField.sources.length > 1) {
          setSelectedSegment('Data transform');
        }
      }
    }
  }, [customerKey, openColumn, openColTargetField, openColTargetFieldIdx, setAllFields]);

  useEffect(() => {
    const filtered = allFields.filter((field) =>
      field.field_name.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    setFilteredFields(filtered);
  }, [searchTerm, allFields]);

  const handleSaveMapping = () => {
    if (selectedField && openColumn && currentTable) {
      // add the edited field to list of edited mappings
      onEditedFieldsChange((prev) => {
        const oldMapping = {
          source_model_id: currentTable.id,
          source_field: openColumn.source_field,
          target_model_id: openColumn.target_model_id,
          target_field: openColumn.target_field,
          target_model: openColumn.target_model,
          source_name: connectorType,
          source_model: currentTable.name,
        };

        const newEditedMapping = {
          source_model_id: currentTable.id,
          source_field: openColumn.source_field,
          target_model_id: selectedField.model_id,
          target_field: selectedField.field_name,
          target_model: selectedField.model_name,
          source_name: connectorType,
          source_model: currentTable.name,
        };

        const mappingEditObj = { prev: oldMapping, current: newEditedMapping };

        if (!prev?.length) {
          return [mappingEditObj];
        }

        // merge on source field if you edit the same source field multiple times
        return [
          ...prev.filter(({ current }) => current.source_field !== newEditedMapping.source_field),
          mappingEditObj,
        ];
      });

      setIsEditModalOpen(false);
    }
  };

  return (
    <Modal
      title={
        <div className="flex gap-2 items-center text-lg">
          <h2>Mapping for: </h2>
          <DataLabel
            label={openColumn?.source_field}
            image={imgSrc[connectorType.replace(/\s/g, '').toLowerCase()]?.imgPath}
            largeText
          />
        </div>
      }
      open={isEditModalOpen}
      setIsOpen={setIsEditModalOpen}
      titleUnderline={true}
      className={'w-[754px]'}
    >
      <div className="w-full flex flex-col gap-6">
        <>
          <SegmentedControl
            segments={['Direct mapping', 'Data transform']}
            defaultSegment={selectedSegment}
            onSegmentChange={(segment) => setSelectedSegment(segment)}
          />

          {selectedSegment === 'Direct mapping' ? (
            <div className="w-full border border-zinc-200 rounded-xl">
              {/* Search bar */}
              <div className="relative border-b border-b-zinc-200">
                <input
                  type="text"
                  placeholder="Search Gestalt fields"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full h-[72px] px-14 border-none rounded-t-xl text-zinc-900 placeholder:text-zinc-400 focus:ring-0"
                />
                <SearchIcon className="absolute top-0 bottom-0 left-6 mx-0 my-auto stroke-zinc-400 w-5 h-5" />
              </div>

              {/* target field list */}
              <div className="h-72 p-4 pb-0 flex flex-col gap-2 overflow-y-scroll overflow-x-hidden">
                {targetFieldsLoading ? (
                  // If Gestalt fields are loading, show spinner
                  <div className="w-full h-full flex justify-center items-center">
                    <Spinner />
                  </div>
                ) : (
                  // If Gestalt fields are loaded, show list of fields using DataLabels
                  <div className="w-full flex flex-col justify-center items-start gap-2 pb-6">
                    {filteredFields &&
                      filteredFields.length > 0 &&
                      filteredFields.map((field, index) => (
                        <button
                          className="w-full rounded ring-offset-0 ring-transparent transition-all hover:ring-indigo-400 hover:ring-offset-2 hover:ring-2"
                          onClick={() => setSelectedField(field)}
                          key={`${field.model_name}-${field.field_name}--${index}`}
                        >
                          <DataLabel
                            label={field.field_name}
                            description={`Model: ${field.model_name.toLowerCase()}`}
                            image="/img/Gestalt_Cube.svg"
                            className={`
                          w-full text-left
                          ${
                            field.field_name === openColumn?.target_field &&
                            field.model_name.toLowerCase() ===
                              openColumn?.target_model.toLowerCase() &&
                            !(field.sources && field.sources.length > 1) &&
                            !selectedField
                              ? 'ring-2 ring-indigo-600 ring-offset-2 mb-1'
                              : ''
                          }
                          ${
                            field.field_name === selectedField?.field_name &&
                            field.model_name.toLowerCase() ===
                              selectedField?.model_name.toLowerCase()
                              ? 'ring-2 ring-indigo-600 ring-offset-2'
                              : ''
                          }
                        `}
                            rightAlignedContent={
                              // If the field is the current target field, show checkmark
                              field.field_name === openColumn?.target_field &&
                              field.model_name.toLowerCase() ===
                                openColumn?.target_model.toLowerCase() &&
                              !(field.sources && field.sources.length > 1) ? (
                                <div className="flex justify-end items-center gap-2.5">
                                  <p className="text-zinc-900 text-sm font-medium">Current</p>
                                  <CheckIcon className="w-6 h-6 stroke-indigo-600" />
                                </div>
                              ) : // If the field is NOT the current target field and it's mapped with one source, display the mapping
                              field.sources && field.sources.length == 1 ? (
                                <div className="flex justify-end items-center shrink-0 gap-1.5 text-zinc-500 text-sm whitespace-nowrap">
                                  <p>mapped to</p>
                                  <div className="flex shrink-0 justify-center items-center">
                                    <Image
                                      src={
                                        imgSrc[field.sources[0].connectorName.split('_')[0]]
                                          ?.imgPath
                                      }
                                      alt={field.sources[0].connectorName}
                                      width={16}
                                      height={16}
                                    />
                                    <p className="text-zinc-900 font-medium">
                                      {field.sources[0].mappings[0].source_field}
                                    </p>
                                  </div>
                                </div>
                              ) : // If the field is NOT the current target field and it's mapped with more than one source, display the mapping
                              field.sources && field.sources.length > 1 ? (
                                <div className="flex justify-end items-center shrink-0 gap-1.5 text-zinc-500 text-sm whitespace-nowrap">
                                  <p>mapped to</p>
                                  <div className="flex shrink-0 justify-center items-center">
                                    <Image
                                      src={
                                        imgSrc[field.sources[0].connectorName.split('_')[0]]
                                          ?.imgPath
                                      }
                                      alt={field.sources[0].connectorName}
                                      width={16}
                                      height={16}
                                    />
                                    <p className="text-zinc-900 font-medium">
                                      {field.sources.length} {connector.display_name}
                                    </p>
                                    <p>&nbsp;fields with transform</p>
                                  </div>
                                </div>
                              ) : (
                                // If the field is NOT the current target field and it's NOT mapped, show "Unmapped"
                                <p className="text-zinc-500 text-sm font-medium">Unmapped</p>
                              )
                            }
                          />
                        </button>
                      ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="w-full h-[372px] flex justify-center items-center border border-zinc-200 rounded-xl">
              <p className="text-zinc-600">Editing data transforms is coming soon</p>
            </div>
          )}
        </>

        {/* Actions */}
        <div className="w-full flex justify-end gap-4">
          <Button type="primary" label="Save mapping" onClick={handleSaveMapping} />
        </div>
      </div>
    </Modal>
  );
}
