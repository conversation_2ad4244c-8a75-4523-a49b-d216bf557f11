import { DataLabel, FieldTypeIcon, LinkAnimation, DisconnectedIcon } from '@/components';
import { imgSrc } from 'lib/source';
import { SourceModelDictionaryField } from '@/types';

type Props = {
  column: SourceModelDictionaryField;
  connectorType: string;
};

export default function DataTableRow(props: Props) {
  const { column, connectorType } = props;
  const { field_name, gen_field_type, mappings } = column;

  const hasMappings = !!mappings?.length;

  return (
    <tr>
      {/* Source column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        <DataLabel label={field_name} image={imgSrc.getOrDefault(connectorType).imgPath} />
      </td>
      <td className="min-w-[100px] w-20">
        {hasMappings ? <LinkAnimation direction="inbound" /> : <DisconnectedIcon />}
      </td>
      {/* Gestalt column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        {hasMappings ? (
          <DataLabel
            label={mappings.map((mapping) => mapping.contract_mapping.target_field).join(', ')}
            description={`Model: ${mappings
              .map((mapping) => mapping.target_model_name)
              .join(', ')}`}
            image="/img/Gestalt_Cube.svg"
          />
        ) : (
          <DataLabel label="Unmapped" subdueLabel={true} showLogo={false} />
        )}
      </td>
      {/* Column type */}
      <td className="px-6 py-4">
        <div className="h-full flex flex-row justify-start items-center gap-2 text-zinc-600 capitalize">
          <FieldTypeIcon type={gen_field_type} />
          {gen_field_type}
        </div>
      </td>
      <td className="px-6 py-4 text-zinc-400 italic whitespace-nowrap">Coming soon</td>
    </tr>
  );
}
