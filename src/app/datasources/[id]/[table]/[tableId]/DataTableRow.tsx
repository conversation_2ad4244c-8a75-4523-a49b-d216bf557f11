import { DataLabel, FieldTypeIcon, LinkAnimation, DisconnectedIcon } from '@/components';
import { imgSrc } from 'lib/source';
import { findGeneralType } from '@/utils/data';
import { SourceField } from '@/types';

type Props = {
  column: SourceField;
  connectorType: string;
};

export default function DataTableRow(props: Props) {
  const { column, connectorType } = props;
  const { field_name, field_type, mappings } = column;

  const genFieldType = findGeneralType(field_type);

  const hasMappings = !!mappings?.length;

  return (
    <tr>
      {/* Source column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        <DataLabel label={field_name} image={imgSrc.getOrDefault(connectorType).imgPath} />
      </td>
      <td className="min-w-[100px] w-20">
        {hasMappings ? <LinkAnimation direction="inbound" /> : <DisconnectedIcon />}
      </td>
      {/* Gestalt column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        {hasMappings ? (
          <DataLabel
            label={mappings.map((mapping) => mapping.target_field).join(', ')}
            description={`Model: ${mappings.map((mapping) => mapping.target_model).join(', ')}`}
            image="/img/Gestalt_Cube.svg"
          />
        ) : (
          <DataLabel label="Unmapped" subdueLabel={true} showLogo={false} />
        )}
      </td>
      {/* Column type */}
      <td className="px-6 py-4">
        <div className="h-full flex flex-row justify-start items-center gap-2 text-zinc-600 capitalize">
          <FieldTypeIcon type={genFieldType} />
          {genFieldType}
        </div>
      </td>
      {/* TODO: Add last changed time
        <td className="px-6 py-4 text-zinc-600">{Dayjs(column.lastChanged).format('MMMM D, YYYY')}</td>
      */}
      <td className="px-6 py-4 text-zinc-400 italic whitespace-nowrap">Coming soon</td>
      {/* TODO: Add edit action that opens modal
        <td className="px-6 py-4 text-right">
          <button className="text-indigo-600 text-sm text-right" onClick={() => (setIsEditModalOpen(true), setOpenColumn(item))}>
            Edit
          </button>
        </td>
      */}
    </tr>
  );
}
