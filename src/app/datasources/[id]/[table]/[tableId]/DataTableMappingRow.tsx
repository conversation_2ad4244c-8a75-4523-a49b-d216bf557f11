import { imgSrc } from 'lib/source';
import { DataLabel, FieldTypeIcon, LibTooltip } from '@/components';
import { Shuffle01 } from '@/components/icons';
import { findGeneralType } from '@/utils/data';
import { useFeatureFlags } from '@/hooks';
import { Mapping } from '@/types';

type Props = {
  mapping: Mapping;
  connectorType: string;
  onEditClick: (mapping: Record<string, any>) => void;
  mappingId: string;
};

export default function DataTableMappingRow(props: Props) {
  const { mapping, connectorType, onEditClick, mappingId } = props;
  const { source_field, target_field, target_model, source_field_type, on_sql } = mapping;

  const { featureFlags } = useFeatureFlags();

  const mappingEditEnabled = featureFlags['can-edit-mappings']?.enabled;

  const genSourceType = findGeneralType(source_field_type || '');

  return (
    <tr>
      {/* Source column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        <DataLabel label={source_field} image={imgSrc.getOrDefault(connectorType).imgPath} />
      </td>
      {/* Animated connector line */}
      <td className="min-w-[100px] w-20">
        {mapping.operation === 'TRANSFORM' ? (
          <>
            <div className="transformOperation" data-tooltip-id={mappingId}>
              <div className="-ml-4 w-[calc(100%+32px)] h-[4.5px] flex items-center gap-2">
                <div className="sourceLinkOnline h-full w-full bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms]"></div>
                <Shuffle01 className="stroke-purple-600 shrink-0" />
                <div className="sourceLinkOnline h-full w-full bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms]"></div>
              </div>
            </div>
            {on_sql && <LibTooltip id={mappingId} content={on_sql} />}
          </>
        ) : (
          <div className="-ml-4 w-[calc(100%+32px)] h-[4.5px]">
            <div className="sourceLinkOnline h-full w-full bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms]"></div>
          </div>
        )}
      </td>
      {/* Gestalt column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        {target_field ? (
          <DataLabel
            label={target_field}
            description={`Model: ${target_model}`}
            image="/img/Gestalt_Cube.svg"
          />
        ) : (
          <DataLabel label="Unmapped" subdueLabel={true} showLogo={false} />
        )}
      </td>
      {/* Column type */}
      <td className="px-6 py-4">
        <div className="h-full flex flex-row justify-start items-center gap-2 text-zinc-600 capitalize">
          <FieldTypeIcon type={genSourceType} />
          {genSourceType}
        </div>
      </td>
      {/* TODO: Add last changed time
        <td className="px-6 py-4 text-zinc-600">{Dayjs(column.lastChanged).format('MMMM D, YYYY')}</td>
      */}
      <td className="px-6 py-4 text-zinc-400 italic whitespace-nowrap">Coming soon</td>
      <td className="px-6 py-4 text-right">
        {mappingEditEnabled && (
          <button className="text-indigo-600 text-sm" onClick={() => onEditClick(mapping)}>
            Edit
          </button>
        )}
      </td>
    </tr>
  );
}
