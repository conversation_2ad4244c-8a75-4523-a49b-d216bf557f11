import { imgSrc } from 'lib/source';
import { DataLabel, FieldTypeIcon, LibTooltip } from '@/components';
import { Shuffle01 } from '@/components/icons';
import { findGeneralType } from '@/utils/data';
import { SourceDictionaryFieldFlatMapping } from '../../types';

type Props = {
  mapping: SourceDictionaryFieldFlatMapping;
  connectorType: string;
  mappingId: string;
};

export default function DataTableMappingRow(props: Props) {
  const { mapping, connectorType, mappingId } = props;
  const { field_name, field_type, contract_mapping, target_model_name } = mapping;
  const { target_field, on_sql, mapping_type } = contract_mapping;

  const genSourceType = findGeneralType(field_type || '', field_name);

  const imgConfig = imgSrc.getOrDefault(connectorType);

  return (
    <tr>
      {/* Source column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        <DataLabel
          label={field_name}
          image={imgConfig.imgPath}
          imgStyle={{ style: imgConfig.dropdownStyle?.style }}
        />
      </td>
      {/* Animated connector line */}
      <td className="min-w-[100px] w-20">
        {mapping_type === 'transform' ? (
          <>
            <div className="transformOperation" data-tooltip-id={mappingId}>
              <div className="-ml-4 w-[calc(100%+32px)] h-[4.5px] flex items-center gap-2">
                <div className="sourceLinkOnline h-full w-full bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms]"></div>
                <Shuffle01 className="stroke-purple-600 shrink-0" />
                <div className="sourceLinkOnline h-full w-full bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms]"></div>
              </div>
            </div>
            {on_sql && <LibTooltip id={mappingId} content={on_sql} />}
          </>
        ) : (
          <div className="-ml-4 w-[calc(100%+32px)] h-[4.5px]">
            <div className="sourceLinkOnline h-full w-full bg-left-bottom bg-repeat-x rounded-full transition-all duration-[2000ms]"></div>
          </div>
        )}
      </td>
      {/* Gestalt column name */}
      <td className="px-6 py-4 w-80 max-w-xs">
        {target_field ? (
          <DataLabel
            label={target_field}
            description={`Model: ${target_model_name}`}
            image="/img/Gestalt_Cube.svg"
          />
        ) : (
          <DataLabel label="Unmapped" subdueLabel={true} showLogo={false} />
        )}
      </td>
      {/* Column type */}
      <td className="px-6 py-4">
        <div className="h-full flex flex-row justify-start items-center gap-2 text-zinc-600 capitalize">
          <FieldTypeIcon type={genSourceType} />
          {genSourceType}
        </div>
      </td>
      {/* TODO: Add last changed time
        <td className="px-6 py-4 text-zinc-600">{Dayjs(column.lastChanged).format('MMMM D, YYYY')}</td>
      */}
      <td className="px-6 py-4 text-zinc-400 italic whitespace-nowrap">Coming soon</td>
    </tr>
  );
}
