import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Button, Loading } from '@/components';
import { imgSrc } from 'lib/source';
import { useParams, usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { DataSource, SourceModel } from '@/types';
import { getDisplayConnectorType } from '@/utils/dataSources';

type Params = {
  id: string;
  table: string;
  tableId: string;
};

type Props = {
  loading: boolean;
  dataSource: DataSource;
  sourceModels: SourceModel[] | null;
  messageContextHolder?: any;
};

export default function TablesHeader(props: Props) {
  const { loading, dataSource, sourceModels, messageContextHolder } = props;

  const { id: dataSourceId, table, tableId } = useParams() as Params;

  const pathname = usePathname();
  const router = useRouter();

  if (loading || !sourceModels || !dataSource) {
    return (
      <div className="flex flex-col justify-between w-full px-8 mb-10">
        <Loading />
      </div>
    );
  }

  const { displayName } = dataSource;

  const displayConnectorType = getDisplayConnectorType(dataSource);

  const tableOptions = sourceModels.map((model) => ({
    label: model.name.toLowerCase(),
    value: model,
  }));

  const tableIdx = tableOptions.findIndex((opt) => opt.value.id === tableId);

  const nextTable = tableOptions[tableIdx + 1]?.value;
  const prevTable = tableOptions[tableIdx - 1]?.value;

  return (
    <PageTitle
      title={table}
      titleIcon={
        <Image
          src={imgSrc.getOrDefault(displayConnectorType).imgPath}
          width={30}
          height={30}
          alt={`${displayConnectorType} Logo`}
        />
      }
      subpage
      breadcrumbs={[
        { label: 'Data sources', url: '/' },
        {
          label: displayName,
          url: `/datasources/${dataSourceId}`,
        },
        { label: table, url: pathname },
      ]}
    >
      {messageContextHolder}
      <div className="flex gap-3 sm:mt-0 sm:flex-row sm:items-center">
        <Select
          options={tableOptions}
          className="w-80 ring-indigo-600"
          value={tableOptions.find((option) => option.value.id === tableId)}
          image={
            <Image
              src={imgSrc.getOrDefault(displayConnectorType).imgPath}
              width={20}
              height={20}
              alt={`${displayConnectorType} Logo`}
            />
          }
          onChange={({ value }) => {
            router.push(`/datasources/${dataSourceId}/${value.name.toLowerCase()}/${value.id}`);
          }}
        />
        <Button
          iconOnly
          type="secondary"
          icon="ArrowLeft"
          disabled={!prevTable}
          href={`/datasources/${dataSourceId}/${prevTable?.name?.toLowerCase()}/${prevTable?.id}`}
        />
        <Button
          iconOnly
          type="secondary"
          icon="ArrowRight"
          disabled={!nextTable}
          href={`/datasources/${dataSourceId}/${nextTable?.name?.toLowerCase()}/${nextTable?.id}`}
        />
      </div>
    </PageTitle>
  );
}
