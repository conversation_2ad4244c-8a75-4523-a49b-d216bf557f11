'use client';

import { useState, useMemo } from 'react';
import { InputText, DataFilter, Loading, PageContent } from '@/components';
import { filterData } from '@/utils/data';
import ChangeHistory from './ChangeHistory';
import { standardFilterOptions } from '@/constants/data';
import { FilterValue, SortConfig, SourceModelDictionaryField } from '@/types';
import DataTableHead from './DataTableHead';
import DataTableBody from './DataTableBody';
import TablesHeader from './TablesHeader';
import { useDictionaryStore, useDataSourceStore } from '@/stores';

// accessors for filtering based on filter type
const filterFields = {
  type: (col: SourceModelDictionaryField, val: string) => col.gen_field_type === val,
  mapping: (col: SourceModelDictionaryField, val: string) => {
    if (val === 'mapped') {
      return !!col.mappings?.length;
    }

    return !col.mappings?.length;
  },
};

type Params = {
  id: string;
  name: string;
  table: string;
  tableId: string;
};

type Props = {
  params: Params;
};

export default function Page(props: Props) {
  const { params } = props;
  const { tableId: modelId } = params;

  const { sourceModels } = useDictionaryStore();
  const { currentDataSource } = useDataSourceStore();

  const [tableDataView, setTableDataView] = useState<'mappings' | 'columns'>('mappings');
  const [searchTerm, setSearchTerm] = useState(''); // Setting table search field
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null); // Setting table grid sort order
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
    mapping: { label: 'All', value: 'all-mapped', filter: false },
  });

  const currentSourceModel = sourceModels?.find((model) => model.id === modelId);

  const filteredDictionary = useMemo(() => {
    return filterData<SourceModelDictionaryField>(currentSourceModel?.dictionary || [], {
      searchTerm,
      searchAccessor: (col: SourceModelDictionaryField, searchTerm: string) =>
        col.field_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        !!col?.mappings?.some((mapping) =>
          mapping.contract_mapping.target_field.toLowerCase().includes(searchTerm.toLowerCase()),
        ),
      filterConfig,
      filterFields,
      sortConfig,
    });
  }, [currentSourceModel, filterConfig, sortConfig, searchTerm]);

  if (!currentDataSource || !currentSourceModel) {
    return <Loading />;
  }

  return (
    <PageContent>
      {currentDataSource && (
        <>
          <TablesHeader
            loading={!currentDataSource}
            dataSource={currentDataSource}
            sourceModels={sourceModels}
          />
          {/* Page content */}
          <div className="flex flex-col px-8 pb-28">
            <div className="w-full flex flex-col gap-10">
              <ChangeHistory />
              {/* Table Data */}
              <div className="w-7/12 w-full min-w-full flex flex-col space-y-3">
                <h2 className="text-2xl font-medium text-zinc-900">Model data</h2>
                <div className="w-full flex justify-between items-center gap-8">
                  <div className="w-full flex gap-4 border-b border-zinc-200">
                    <button
                      className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent hover:text-indigo-700 focus:outline-none ${
                        tableDataView == 'mappings' && 'activeTab'
                      }`}
                      onClick={() => setTableDataView('mappings')}
                    >
                      Mappings
                    </button>
                    <button
                      className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent hover:text-indigo-700 focus:outline-none ${
                        tableDataView == 'columns' && 'activeTab'
                      }`}
                      onClick={() => setTableDataView('columns')}
                    >
                      Fields
                    </button>
                  </div>
                  <div className="flex gap-3">
                    <InputText
                      className="w-72"
                      icon="SearchMd"
                      placeholder="Search"
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <DataFilter
                      filterConfig={filterConfig}
                      onFilterConfigChange={setFilterConfig}
                      filterOptions={standardFilterOptions}
                    />
                  </div>
                </div>

                <div className="rounded-md border border-zinc-200 overflow-hidden">
                  <table className="w-full border-collapse bg-white text-left text-sm text-zinc-900">
                    <DataTableHead
                      sortConfig={sortConfig}
                      onSort={setSortConfig}
                      connectorDisplayName={currentDataSource.displayName}
                    />
                    <DataTableBody
                      filteredDictionary={filteredDictionary}
                      tableDataView={tableDataView}
                      connectorType={currentDataSource.connector.connector_type}
                    />
                  </table>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </PageContent>
  );
}
