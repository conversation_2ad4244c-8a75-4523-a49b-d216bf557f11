'use client';

import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { message } from 'antd';
import { InputText, Button, DataFilter, Loading, PageContent } from '@/components';
import { filterData, findGeneralType } from '@/utils/data';
import { sleep } from '@/utils/async';
import { useFeatureFlags, useCustomer } from '@/hooks';
import ChangeHistory from './ChangeHistory';
import { standardFilterOptions } from '@/constants/data';
import {
  FilterValue,
  SortConfig,
  SourceField,
  TargetField,
  Mapping,
  ModelSummary,
  DataSource,
} from '@/types';
import DataTableHead from './DataTableHead';
import DataTableBody from './DataTableBody';
import TablesHeader from './TablesHeader';
import { getSourceModelSummaries } from '../../utils';
import { api } from '@/helpers/web';

// accessors for filtering based on filter type
const filterFields = {
  type: (col, val) =>
    findGeneralType(col.field_type) === val || findGeneralType(col.source_field_type) === val,
  mapping: (col, val) => {
    if (val === 'mapped') {
      return !!col.target_field || !!col.mappings.length;
    }

    return !col.target_field && !col.mappings.length;
  },
};

const tableDataUniqFields = ['target_field', 'target_model_id', 'source_model_id', 'source_field'];

const findExistingMapping = (mappings: Mapping[], newMapping: Mapping) =>
  mappings.find((mapping) =>
    tableDataUniqFields.every((field) => mapping[field] === newMapping[field]),
  );

export type TableData = {
  columns: SourceField[] | null;
  mappings: Mapping[] | null;
};

export type EditedMapping = { prev: Mapping; current: Mapping };

type Params = {
  id: string;
  name: string;
  table: string;
  tableId: string;
};

type Props = {
  params: Params;
};

export default function Page(props: Props) {
  const { params } = props;
  const { id: dataSourceId, table, tableId } = params;

  const { featureFlags } = useFeatureFlags();
  const { customerId, customerKey } = useCustomer();
  const [messageApi, contextHolder] = message.useMessage();

  const mappingEditEnabled = featureFlags['can-edit-mappings']?.enabled;

  const [dataSources, setDataSources] = useState<DataSource[]>();
  const [dataSourcesLoading, setDataSourcesLoading] = useState<boolean>(true);
  const [tableData, setTableData] = useState<TableData>({ columns: null, mappings: null }); // Getting source of truth for current table
  const [allTables, setAllTables] = useState<ModelSummary[]>(); // Getting all tables for switcher
  const [tableDataView, setTableDataView] = useState<'mappings' | 'columns'>('mappings');
  const [searchTerm, setSearchTerm] = useState(''); // Setting table search field
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null); // Setting table grid sort order
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
    mapping: { label: 'All', value: 'all-mapped', filter: false },
  });
  // const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  // const [isCreateModalOpen, setCreateModalOpen] = useState(false);
  // const [openColumn, setOpenColumn] = useState<Mapping>(); // will be used to determine which field is open in the edit modal
  const [isLoading, setIsLoading] = useState(false); // New state variable to track loading state
  const [_allFields, setAllFields] = useState<TargetField[]>([]); // New state variable to store all fields
  const [editedFields, setEditedFields] = useState<EditedMapping[] | null>();
  const [_targetFieldsLoading, setTargetFieldsLoading] = useState(false); // New state variable to track target fields loading state
  const [editsSaving, setEditsSaving] = useState(false);
  const [newMappings, _setNewMappings] = useState<Mapping[]>();

  const hasFetchedRef = useRef(false);

  const currentDataSource = dataSources?.find((dataSource) => dataSource.id === dataSourceId);

  // update tableData based on edited fields – they won't be committed to snowflake until you click the "Save mappings"
  // button beneath the mapping table
  useEffect(() => {
    if (!editedFields?.length) {
      return;
    }

    setTableData((prev) => {
      const newTableData = { ...prev };

      editedFields.forEach(({ prev, current }) => {
        const currentMapping = findExistingMapping(newTableData.mappings || [], prev);

        if (currentMapping) {
          currentMapping.target_field = current.target_field;
          currentMapping.target_model = current.target_model;
          currentMapping.operation = 'COPY';
          currentMapping.source_fields = null;
        }

        const currentCol = newTableData.columns?.find(
          (col) => col.field_name === current.source_field,
        );

        if (currentCol) {
          const currentColMapping = findExistingMapping(currentCol.mappings || [], prev);

          if (currentColMapping) {
            currentColMapping.target_field = current.target_field;
            currentColMapping.target_model = current.target_model;
            currentColMapping.operation = 'COPY';
            currentColMapping.source_fields = null;
          }
        }
      });

      return newTableData;
    });
  }, [editedFields]);

  // update tableData based on new mappings
  useEffect(() => {
    if (!newMappings?.length) {
      return;
    }

    setTableData((prev) => {
      const newTableData = {
        mappings: [...(prev.mappings || [])],
        columns: [...(prev.columns || [])],
      };

      newMappings.forEach((newMapping) => {
        // only add new mappings if they don't already exist
        if (!findExistingMapping(newTableData.mappings || [], newMapping)) {
          newTableData.mappings.push(newMapping);
        }

        const currentCol = newTableData.columns?.find(
          (col) => col.field_name === newMapping.source_field,
        );

        if (currentCol) {
          if (!currentCol.mappings?.length) {
            currentCol.mappings = [newMapping];
          } else {
            if (!findExistingMapping(currentCol.mappings, newMapping)) {
              currentCol.mappings.push(newMapping);
            }
          }
        }
      });

      return newTableData;
    });
  }, [newMappings]);

  // get source and connector info
  useEffect(() => {
    if (!customerId) {
      return;
    }
    async function getDataSources() {
      setDataSourcesLoading(true);

      const res = await fetch(`/api/dataSources/getCustomerDataSources?customerId=${customerId}`);

      const dataSources = await res.json();

      setDataSources(dataSources);
      setDataSourcesLoading(false);
    }

    getDataSources();
  }, [customerId]);

  // New useEffect to fetch all fields
  useEffect(() => {
    if (!customerKey || !customerId || hasFetchedRef.current) {
      return;
    }

    hasFetchedRef.current = true;

    // TO DO: update these queries to use one api call and only fetch the data needed (and ideally use model ids instead of table names)
    const getDictionary = async () => {
      setTargetFieldsLoading(true);

      // Getting each Gestalt table (tablename to use to get fields)
      const res = await fetch(
        `/api/dataDictionary/tables?customerId=${customerId}&customerKey=${customerKey}`,
      );

      const { tables } = await res.json();

      // Iterate over the table's previously fetched and get the fields
      let allFields: any[] = [];

      await Promise.all(
        tables.map(async (table: { table_name: string }) => {
          const tableInfo = await api.get(`/api/dataDictionary/${table.table_name}/tableInfo`, {
            customerKey,
            customerId,
          });

          allFields = [...allFields, ...tableInfo.columns];
        }),
      );

      allFields.sort((a, b) => a.field_name.localeCompare(b.field_name));

      setAllFields(allFields);
      setTargetFieldsLoading(false);
    };

    getDictionary();
  }, [customerKey, customerId]);

  // refetch source table summaries for routing
  useEffect(() => {
    if (!customerId || !currentDataSource) {
      return;
    }

    const getTables = async () => {
      const sourceModels = await getSourceModelSummaries({
        sourceModelKey: currentDataSource.sourceModelKey,
      });

      setAllTables(sourceModels);
    };

    getTables();
  }, [customerId, currentDataSource, tableId]);

  const getSourceModelData = useCallback(async () => {
    setIsLoading(true);

    const { connector, sourceModelKey } = currentDataSource!;
    const { connector_id } = connector;

    try {
      const { sourceFields, sourceMappings } = await api.get(
        `/api/connector/${connector_id}/${table}/getSourceModelData`,
        { customerId, modelId: tableId, sourceModelKey },
      );

      setTableData({ columns: sourceFields, mappings: sourceMappings });

      setIsLoading(false);
    } catch (error: any) {
      console.error({ error, stack: error.stack });
      setIsLoading(false);
    }
  }, [currentDataSource, table, customerId, tableId]);

  // fetch model fields and mappings based on model id
  useEffect(() => {
    if (!customerId || !currentDataSource) {
      return;
    }

    getSourceModelData();
  }, [customerId, currentDataSource, getSourceModelData]);

  // const handleEditClick = (selectedMapping: Mapping) => {
  //   setOpenColumn(selectedMapping);
  //   setIsEditModalOpen(true);
  // };

  const handleSaveEdits = async () => {
    setEditsSaving(true);

    const body = {
      editedMappings: editedFields,
      newMappings,
      connectorKey: currentDataSource!.sourceModelKey,
      customerId,
    };

    try {
      await api.workerPost({ service: 'dataSources:editMappings', body, customerKey });

      // This is a stand-in – we need a better way to handle loading states and updating the UI
      // when a client request is processed by the worker. For now, we're just arbitrarily waiting to give the worker
      // time to finish the task. It's not guaranteed to be finished, so refresh may be required.
      await sleep(8000);

      messageApi.success('Mappings saved successfully');

      setEditedFields(null);

      await getSourceModelData();
    } catch (error: any) {
      messageApi.error('Error saving mappings');
    }

    setEditsSaving(false);
  };

  const filteredTableData = useMemo(
    () => ({
      columns: filterData<SourceField>(tableData.columns || [], {
        searchTerm,
        searchAccessor: (col, searchTerm) =>
          col.field_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          col.mappings.some((mapping) =>
            mapping.target_field.toLowerCase().includes(searchTerm.toLowerCase()),
          ),
        filterConfig,
        filterFields,
        sortConfig,
      }),
      mappings: filterData<Mapping>(tableData.mappings || [], {
        searchTerm,
        searchAccessor: (col, searchTerm) =>
          (col.source_field && col.source_field.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (col.source_fields &&
            col.source_fields.some((field) =>
              field.toLowerCase().includes(searchTerm.toLowerCase()),
            )) ||
          col.target_field.toLowerCase().includes(searchTerm.toLowerCase()),
        filterConfig,
        filterFields,
        sortConfig,
      }),
    }),
    [tableData, searchTerm, filterConfig, sortConfig],
  );

  // const currentTable = useMemo(
  //   () => allTables?.find((table) => table.id === tableId),
  //   [allTables, tableId],
  // );

  if (dataSourcesLoading) {
    return <Loading />;
  }

  return (
    <PageContent>
      {currentDataSource && (
        <>
          <TablesHeader
            loading={dataSourcesLoading}
            dataSource={currentDataSource}
            sourceTables={allTables}
            messageContextHolder={contextHolder}
          />

          {/* Page content */}
          <div className="flex flex-col px-8 pb-28">
            <div className="w-full flex flex-col gap-10">
              <ChangeHistory />
              {/* Table Data */}
              <div className="w-7/12 w-full min-w-full flex flex-col space-y-3">
                <h2 className="text-2xl font-medium text-zinc-900">Model data</h2>
                <div className="w-full flex justify-between items-center gap-8">
                  <div className="w-full flex gap-4 shrin border-b border-zinc-200">
                    <button
                      className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent hover:text-indigo-700 focus:outline-none ${
                        tableDataView == 'mappings' && 'activeTab'
                      }`}
                      onClick={() => setTableDataView('mappings')}
                    >
                      Mappings
                    </button>
                    <button
                      className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent hover:text-indigo-700 focus:outline-none ${
                        tableDataView == 'columns' && 'activeTab'
                      }`}
                      onClick={() => setTableDataView('columns')}
                    >
                      Fields
                    </button>
                  </div>
                  <div className="flex gap-3">
                    <InputText
                      className="w-72"
                      icon="SearchMd"
                      placeholder="Search"
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <DataFilter
                      filterConfig={filterConfig}
                      onFilterConfigChange={setFilterConfig}
                      filterOptions={standardFilterOptions}
                    />
                    {mappingEditEnabled && (
                      <Button
                        label="Add mapping"
                        type="primary"
                        onClick={() => {
                          // setCreateModalOpen(true);
                        }}
                      />
                    )}
                  </div>
                </div>

                <div className="rounded-md border border-zinc-200 overflow-hidden">
                  <table className="w-full border-collapse bg-white text-left text-sm text-zinc-900">
                    <DataTableHead
                      sortConfig={sortConfig}
                      onSort={setSortConfig}
                      connectorDisplayName={currentDataSource.displayName}
                    />
                    <DataTableBody
                      filteredTableData={filteredTableData}
                      sourceLoading={isLoading}
                      tableDataView={tableDataView}
                      connectorType={currentDataSource.connector.connector_type}
                      onEditClick={() => {}}
                    />
                  </table>
                </div>
                {!!(editedFields?.length || newMappings?.length) && (
                  <div className="w-full flex justify-end">
                    <div>
                      <Button
                        label={editsSaving ? 'Saving...' : 'Save mappings'}
                        onClick={handleSaveEdits}
                        disabled={editsSaving}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Commented out 2/12/25 – we need to revamp mapping edits based on smart mapper flow changes */}
          {/* {mappingEditEnabled && (
            <>
              <EditMappingModal
                allFields={allFields}
                setAllFields={setAllFields}
                onEditedFieldsChange={setEditedFields}
                openColumn={openColumn}
                isEditModalOpen={isEditModalOpen}
                setIsEditModalOpen={setIsEditModalOpen}
                connectorType={currentDataSource.connector.connector_type}
                connector={currentDataSource.connector}
                targetFieldsLoading={targetFieldsLoading}
                currentTable={currentTable}
              />
              <CreateMappingModal
                isOpen={isCreateModalOpen}
                onOpenChange={setCreateModalOpen}
                connectorKey={currentDataSource.connector.connector_type}
                sourceFields={tableData.columns || []}
                targetFields={allFields}
                onMappingsChange={setNewMappings}
                targetFieldsLoading={targetFieldsLoading}
              />
            </>
          )} */}
        </>
      )}
    </PageContent>
  );
}
