'use client';

import { useEffect, useState, useMemo } from 'react';
import { InputText, DataFilter, Loading, PageContent } from '@/components';
import { filterData, findGeneralType } from '@/utils/data';
import { useCustomer } from '@/hooks';
import ChangeHistory from './ChangeHistory';
import { standardFilterOptions } from '@/constants/data';
import { FilterValue, SortConfig, SourceModelDictionaryField, DataSource } from '@/types';
import DataTableHead from './DataTableHead';
import DataTableBody from './DataTableBody';
import TablesHeader from './TablesHeader';
import { api } from '@/helpers/web';
import { useDictionaryStore } from '@/stores';

// accessors for filtering based on filter type
const filterFields = {
  type: (col, val) =>
    findGeneralType(col.field_type) === val || findGeneralType(col.source_field_type) === val,
  mapping: (col, val) => {
    if (val === 'mapped') {
      return !!col.target_field || !!col.mappings.length;
    }

    return !col.target_field && !col.mappings.length;
  },
};

type Params = {
  id: string;
  name: string;
  table: string;
  tableId: string;
};

type Props = {
  params: Params;
};

export default function Page(props: Props) {
  const { params } = props;
  const { id: dataSourceId, tableId: modelId } = params;

  const { customerId } = useCustomer();
  const { sourceModels } = useDictionaryStore();

  const [dataSources, setDataSources] = useState<DataSource[]>();
  const [dataSourcesLoading, setDataSourcesLoading] = useState<boolean>(true);
  const [tableDataView, setTableDataView] = useState<'mappings' | 'columns'>('mappings');
  const [searchTerm, setSearchTerm] = useState(''); // Setting table search field
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null); // Setting table grid sort order
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
    mapping: { label: 'All', value: 'all-mapped', filter: false },
  });

  const currentDataSource = dataSources?.find((dataSource) => dataSource.id === dataSourceId);

  const currentSourceModel = sourceModels?.find((model) => model.id === modelId);

  // get source and connector info
  useEffect(() => {
    if (!customerId) {
      return;
    }
    async function getDataSources() {
      setDataSourcesLoading(true);

      const dataSources = await api.get('/api/dataSources/getCustomerDataSources', { customerId });

      setDataSources(dataSources);
      setDataSourcesLoading(false);
    }

    getDataSources();
  }, [customerId]);

  const filteredDictionary = useMemo(() => {
    return filterData<SourceModelDictionaryField>(currentSourceModel?.dictionary || [], {
      searchTerm,
      searchAccessor: (col: SourceModelDictionaryField, searchTerm: string) =>
        col.field_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        col.mappings.some((mapping) =>
          mapping.contract_mapping.target_field.toLowerCase().includes(searchTerm.toLowerCase()),
        ),
      filterConfig,
      filterFields,
      sortConfig,
    });
  }, [currentSourceModel, filterConfig, sortConfig, searchTerm]);

  if (dataSourcesLoading || !currentSourceModel) {
    return <Loading />;
  }

  return (
    <PageContent>
      {currentDataSource && (
        <>
          <TablesHeader
            loading={dataSourcesLoading}
            dataSource={currentDataSource}
            sourceModels={sourceModels}
          />
          {/* Page content */}
          <div className="flex flex-col px-8 pb-28">
            <div className="w-full flex flex-col gap-10">
              <ChangeHistory />
              {/* Table Data */}
              <div className="w-7/12 w-full min-w-full flex flex-col space-y-3">
                <h2 className="text-2xl font-medium text-zinc-900">Model data</h2>
                <div className="w-full flex justify-between items-center gap-8">
                  <div className="w-full flex gap-4 shrink-0 border-b border-zinc-200">
                    <button
                      className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent hover:text-indigo-700 focus:outline-none ${
                        tableDataView == 'mappings' && 'activeTab'
                      }`}
                      onClick={() => setTableDataView('mappings')}
                    >
                      Mappings
                    </button>
                    <button
                      className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent hover:text-indigo-700 focus:outline-none ${
                        tableDataView == 'columns' && 'activeTab'
                      }`}
                      onClick={() => setTableDataView('columns')}
                    >
                      Fields
                    </button>
                  </div>
                  <div className="flex gap-3">
                    <InputText
                      className="w-72"
                      icon="SearchMd"
                      placeholder="Search"
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <DataFilter
                      filterConfig={filterConfig}
                      onFilterConfigChange={setFilterConfig}
                      filterOptions={standardFilterOptions}
                    />
                  </div>
                </div>

                <div className="rounded-md border border-zinc-200 overflow-hidden">
                  <table className="w-full border-collapse bg-white text-left text-sm text-zinc-900">
                    <DataTableHead
                      sortConfig={sortConfig}
                      onSort={setSortConfig}
                      connectorDisplayName={currentDataSource.displayName}
                    />
                    <DataTableBody
                      filteredDictionary={filteredDictionary}
                      tableDataView={tableDataView}
                      connectorType={currentDataSource.connector.connector_type}
                    />
                  </table>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </PageContent>
  );
}
