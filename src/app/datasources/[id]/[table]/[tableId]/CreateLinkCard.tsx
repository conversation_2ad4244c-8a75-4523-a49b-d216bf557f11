import { useMemo, useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ArcherElement } from 'react-archer';
import { InputText, DataFilter, DataLabel, Button, Loading } from '@/components';
import { imgSrc } from 'lib/source';
import { usePage } from '@/hooks';
import { standardFilterOptions } from '@/constants/data';
import { filterData, findGeneralType } from '@/utils/data';
import { SourceField, TargetField, FilterValue, Mapping, SetStateFunction } from '@/types';

type Props = {
  connectorKey?: string;
  sourceFields: SourceField[];
  targetFields: TargetField[];
  newMappings?: Mapping[];
  onMappingsChange: SetStateFunction<Mapping[]>;
  colGapWidth?: string;
  onDone?: Function;
  targetFieldsLoading?: boolean;
};

const filterFields = {
  type: (row, val) => findGeneralType(row.field_type) === val,
};

// just filter on type for now
const filterOptions = [standardFilterOptions[0]];

export default function CreateLinkCard(props: Props) {
  const {
    connectorKey,
    sourceFields,
    targetFields,
    newMappings,
    onMappingsChange,
    colGapWidth = 'w-36',
    onDone,
    targetFieldsLoading,
  } = props;

  const [selectedSource, setSelectedSource] = useState<SourceField | null>();
  const [selectedTarget, setSelectedTarget] = useState<TargetField | null>();
  const [sourceSearchTerm, setSourceSearchTerm] = useState('');
  const [targetSearchTerm, setTargetSearchTerm] = useState('');
  const [sourceFilterConfig, setSourceFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
    match: { value: 'excludeReviewed' },
    mapping: { value: 'excludeNew' },
  });
  const [targetFilterConfig, setTargetFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
  });

  const { pageProps } = usePage();
  const { reviewedMatches } = pageProps;

  // archer library ref for refreshing on scroll
  const archerRef = useRef(null);

  const archerRefreshOnScroll = () => {
    const archerInstance = archerRef?.current as any;

    if (archerInstance) {
      archerInstance.refreshScreen();
    }
  };

  const filteredData = useMemo(
    () => ({
      sourceFields: filterData<SourceField>(sourceFields, {
        searchTerm: sourceSearchTerm,
        nameField: 'field_name',
        filterConfig: sourceFilterConfig,
        filterFields: {
          ...filterFields,
          // additional filters for removing matches that have been approved and new added mappings (shown below).
          // Adding it to the filterData function avoids excessive filter looping.
          // These filters won't appear in the DataFilter component (it's not configurable right now)
          match: (row, val) =>
            val === 'excludeReviewed' &&
            !reviewedMatches?.find(
              (match) =>
                match.review_status === 'accepted' && row.field_name === match.source_field,
            ),
          mapping: (row, val) =>
            val === 'excludeNew' &&
            !newMappings?.find((mapping) => row.field_name === mapping.source_field),
        },
      }),
      targetFields: filterData<TargetField>(targetFields, {
        searchTerm: targetSearchTerm,
        nameField: 'field_name',
        filterConfig: targetFilterConfig,
        filterFields,
      }),
    }),
    [
      sourceFields,
      sourceSearchTerm,
      sourceFilterConfig,
      targetFields,
      targetSearchTerm,
      targetFilterConfig,
      reviewedMatches,
      newMappings,
    ],
  );

  const handleNewMapping = () => {
    onMappingsChange((prev) => [
      ...(prev || []),
      {
        source_model_id: selectedSource!.model_id,
        target_model_id: selectedTarget!.model_id,
        source_field: selectedSource!.field_name,
        source_field_type: selectedSource!.field_type,
        target_field: selectedTarget!.field_name,
        target_field_type: selectedTarget!.field_type,
        target_field_format: selectedTarget!.field_format,
        target_gen_field_type: selectedTarget!.gen_field_type,
        source_model: selectedSource!.model_name,
        target_model: selectedTarget!.model_name,
        source_name: selectedSource!.name,
        // include previous target mappings in case we need to overwrite or combine
        prev_target_sources: selectedTarget!.sources,
      },
    ]);

    setSelectedSource(null);
    setSelectedTarget(null);

    if (onDone) {
      onDone();
    }
  };

  const hasSelection = selectedSource && selectedTarget;

  if (targetFieldsLoading) {
    return (
      <div className="h-72">
        <Loading />
      </div>
    );
  }

  return (
    <ArcherContainer
      ref={archerRef}
      strokeColor="#10b981"
      lineStyle="angle"
      strokeWidth={3}
      endMarker={false}
    >
      <div className="rounded-xl bg-white px-4 py-6 overflow-clip">
        <div className="flex w-full h-96 mb-4">
          <div
            className="flex flex-col w-full gap-y-2 overflow-scroll pt-1 pl-1"
            onScroll={archerRefreshOnScroll}
          >
            <div className="flex gap-x-2">
              <div className="w-full">
                <InputText
                  icon="SearchMd"
                  placeholder={`Search ${connectorKey?.toLowerCase()} fields`}
                  onChange={(e) => setSourceSearchTerm(e.target.value)}
                />
              </div>
              <DataFilter
                filterConfig={sourceFilterConfig}
                onFilterConfigChange={setSourceFilterConfig}
                filterOptions={filterOptions}
              />
            </div>
            {filteredData.sourceFields.map((field, idx) => {
              const selected =
                field.field_name === selectedSource?.field_name &&
                field.model_id === selectedSource?.model_id;

              let dataLabel = (
                <DataLabel
                  label={field.field_name}
                  image={imgSrc.getOrDefault(connectorKey).imgPath}
                />
              );

              if (selected && hasSelection) {
                dataLabel = (
                  <ArcherElement
                    id="sourceField"
                    relations={[
                      {
                        targetId: 'targetField',
                        targetAnchor: 'left',
                        sourceAnchor: 'right',
                        style: { strokeDasharray: '5,5' },
                        className: 'archer-animation',
                      },
                    ]}
                  >
                    <div>{dataLabel}</div>
                  </ArcherElement>
                );
              }

              return (
                <div
                  key={`${field.field_name}${field.model_id}${idx}`}
                  className={`hover:cursor-pointer rounded ${
                    selected && 'border-2 border-emerald-500 sticky -top-1 bottom-0'
                  }`}
                  onClick={() => {
                    setSelectedSource(field);
                  }}
                >
                  {dataLabel}
                </div>
              );
            })}
          </div>
          <canvas className={`${colGapWidth} h-full`}></canvas>
          <div
            className="flex flex-col w-full gap-y-2 overflow-scroll pt-1 pl-1"
            onScroll={archerRefreshOnScroll}
          >
            <div className="flex gap-x-2">
              <div className="w-full">
                <InputText
                  icon="SearchMd"
                  placeholder="Search Gestalt fields"
                  onChange={(e) => setTargetSearchTerm(e.target.value)}
                />
              </div>
              <DataFilter
                filterConfig={targetFilterConfig}
                onFilterConfigChange={setTargetFilterConfig}
                filterOptions={filterOptions}
              />
            </div>
            {filteredData.targetFields.map((field, idx) => {
              const selected =
                field.field_name === selectedTarget?.field_name &&
                field.model_id === selectedTarget?.model_id;

              let dataLabel = (
                <DataLabel
                  label={field.field_name}
                  description={field.model_name}
                  image={imgSrc.gestalt.imgPath}
                />
              );

              if (selected && hasSelection) {
                dataLabel = (
                  <ArcherElement id="targetField">
                    <div>{dataLabel}</div>
                  </ArcherElement>
                );
              }

              return (
                <div
                  key={`${field.field_name}${field.model_id}${idx}`}
                  className={`hover:cursor-pointer rounded ${
                    selected && 'border-2 border-emerald-500 sticky -top-1 bottom-0'
                  }`}
                  onClick={() => {
                    setSelectedTarget(field);
                  }}
                >
                  {dataLabel}
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex justify-between items-center">
          <Button
            label="Reset"
            type="secondary"
            onClick={() => {
              setSelectedSource(null);
              setSelectedTarget(null);
            }}
          />
          <Button
            label="Save mapping"
            disabled={!hasSelection}
            onClick={() => {
              handleNewMapping();
            }}
          />
        </div>
      </div>
    </ArcherContainer>
  );
}
