import { Spinner } from '@/components';
import DataTableRow from './DataTableRow';
import DataTableMappingRow from './DataTableMappingRow';
import { TableData } from './page';
import { genMappingId } from '../../utils';

type Props = {
  filteredTableData: TableData;
  sourceLoading: boolean;
  tableDataView: 'columns' | 'mappings';
  connectorType: string;
  onEditClick: (selectedMapping: any) => void;
};

const tBodyClass = 'divide-y divide-gray-100 border-t border-gray-100';

const noDataMessages = {
  columns: 'no fields',
  mappings: 'no mappings',
};

export default function DataTableBody(props: Props) {
  const { filteredTableData, sourceLoading, tableDataView, connectorType, onEditClick } = props;

  if (!filteredTableData || sourceLoading) {
    return (
      <tbody>
        <tr>
          {/* @ts-ignore */}
          <td colSpan="100%">
            <div className="w-full flex justify-center items-center py-40">
              <Spinner />
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  const noData =
    (tableDataView === 'columns' && !filteredTableData?.columns?.length) ||
    (tableDataView === 'mappings' && !filteredTableData?.mappings?.length);

  if (noData) {
    return (
      <tbody className={tBodyClass}>
        <tr>
          {/* @ts-ignore */}
          <td colSpan="100%">
            <div className="w-full flex justify-center items-center py-40">
              <p className="text-zinc-400">{noDataMessages[tableDataView]}</p>
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  if (tableDataView === 'columns') {
    return (
      <tbody className={tBodyClass}>
        {filteredTableData?.columns?.map((column) => (
          <DataTableRow column={column} connectorType={connectorType} key={column.field_name} />
        ))}
      </tbody>
    );
  }

  return (
    <tbody className={tBodyClass}>
      {filteredTableData?.mappings?.map((mapping) => (
        <DataTableMappingRow
          mapping={mapping}
          connectorType={connectorType}
          key={genMappingId(mapping)}
          mappingId={genMappingId(mapping)}
          onEditClick={onEditClick}
        />
      ))}
    </tbody>
  );
}
