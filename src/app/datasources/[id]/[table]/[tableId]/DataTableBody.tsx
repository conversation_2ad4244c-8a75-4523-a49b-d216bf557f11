import { useMemo } from 'react';
import { flatten } from 'lodash';
import { Spinner } from '@/components';
import DataTableRow from './DataTableRow';
import DataTableMappingRow from './DataTableMappingRow';
import { genMappingId } from '../../utils';
import { SourceModelDictionaryField } from '@/types';
import { SourceDictionaryFieldFlatMapping } from '../../types';

type Props = {
  filteredDictionary: SourceModelDictionaryField[];
  tableDataView: 'columns' | 'mappings';
  connectorType: string;
};

const tBodyClass = 'divide-y divide-gray-100 border-t border-gray-100';

const noDataMessages = {
  columns: 'no fields',
  mappings: 'no mappings',
};

export default function DataTableBody(props: Props) {
  const { filteredDictionary, tableDataView, connectorType } = props;

  const flatMappings: SourceDictionaryFieldFlatMapping[] = useMemo(() => {
    const mappings = filteredDictionary
      .filter((field) => !!field.mappings)
      .map((field) =>
        field.mappings!.map((mapping) => ({
          ...field,
          ...mapping,
        })),
      );

    return flatten(mappings);
  }, [filteredDictionary]);

  if (!filteredDictionary) {
    return (
      <tbody>
        <tr>
          {/* @ts-ignore */}
          <td colSpan="100%">
            <div className="w-full flex justify-center items-center py-40">
              <Spinner />
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  const noFields = !filteredDictionary?.length;
  const noMappings = !flatMappings.length;

  if (tableDataView === 'columns') {
    if (noFields) {
      return (
        <tbody className={tBodyClass}>
          <tr>
            {/* @ts-ignore */}
            <td colSpan="100%">
              <div className="w-full flex justify-center items-center py-40">
                <p className="text-zinc-400">{noDataMessages.columns}</p>
              </div>
            </td>
          </tr>
        </tbody>
      );
    }

    return (
      <tbody className={tBodyClass}>
        {filteredDictionary?.map((column) => (
          <DataTableRow column={column} connectorType={connectorType} key={column.field_name} />
        ))}
      </tbody>
    );
  }

  if (noMappings) {
    return (
      <tbody className={tBodyClass}>
        <tr>
          {/* @ts-ignore */}
          <td colSpan="100%">
            <div className="w-full flex justify-center items-center py-40">
              <p className="text-zinc-400">{noDataMessages.mappings}</p>
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  return (
    <tbody className={tBodyClass}>
      {flatMappings?.map((mapping) => (
        <DataTableMappingRow
          mapping={mapping}
          connectorType={connectorType}
          key={genMappingId(mapping)}
          mappingId={genMappingId(mapping)}
        />
      ))}
    </tbody>
  );
}
