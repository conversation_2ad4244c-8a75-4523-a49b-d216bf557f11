import { Modal } from '@/components';
import { SetStateFunction, SourceField, TargetField, Mapping } from '@/types';
import CreateLinkCard from './CreateLinkCard';

type Props = {
  isOpen: boolean;
  onOpenChange: SetStateFunction<boolean>;
  connectorKey?: string;
  sourceFields: SourceField[];
  targetFields: TargetField[];
  targetFieldsLoading?: boolean;
  onMappingsChange: SetStateFunction<Mapping[]>;
};

export default function CreateMappingModal(props: Props) {
  const {
    isOpen,
    onOpenChange,
    connectorKey,
    sourceFields,
    targetFields,
    onMappingsChange,
    targetFieldsLoading,
  } = props;

  return (
    <Modal
      title="Create new mapping"
      open={isOpen}
      setIsOpen={onOpenChange}
      titleUnderline={true}
      width={'w-[1024px]'}
    >
      <CreateLinkCard
        connectorKey={connectorKey}
        sourceFields={sourceFields}
        targetFields={targetFields}
        targetFieldsLoading={targetFieldsLoading}
        onMappingsChange={onMappingsChange}
        onDone={() => {
          onOpenChange(false);
        }}
      />
    </Modal>
  );
}
