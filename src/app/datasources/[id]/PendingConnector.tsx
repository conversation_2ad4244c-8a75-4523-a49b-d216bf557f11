import Image from 'next/image';
import { Card } from '@/components';

type Props = {
  sourceName: string;
};

export default function PendingConnector(props: Props) {
  const { sourceName } = props;

  return (
    <Card>
      <div className="py-16">
        <div className="flex flex-col justify-center items-center gap-y-8">
          <Image
            src="/img/pending-custom-connector.svg"
            width={500}
            height={500}
            alt="Pending connector"
          />
          <div className="flex flex-col justify-center items-center gap-y-1">
            <span className="text-2xl">{sourceName} has not been implemented yet.</span>
            <span className="text-zinc-500">Your data will be available after the next scheduled sync.</span>
          </div>
        </div>
      </div>
    </Card>
  );
}
