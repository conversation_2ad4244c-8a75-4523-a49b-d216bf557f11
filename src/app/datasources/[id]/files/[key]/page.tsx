'use client';

import { useEffect, useState, useMemo } from 'react';
import { InputText, DataFilter, Loading, PageContent } from '@/components';
import { filterData, getFilterOptions } from '@/utils/data';
import { useCustomer } from '@/hooks';
import { FilterValue, SortConfig, DataSource, FileMetadata, SourceColumn } from '@/types';
import FilesHeader from './FilesHeader';
import ChangeHistory from '../../[table]/[tableId]/ChangeHistory';
import FileDataTableHead from './FileDataTableHead';
import FileDataTableBody from './FileDataTableBody';

// accessors for filtering based on filter type
const filterFields = {
  type: 'generalType',
};

type Params = {
  id: string;
  key: string;
};

type Props = {
  params: Params;
};

export default function Page(props: Props) {
  const { params } = props;
  const { id: dataSourceId } = params;

  const { customerId, customerKey } = useCustomer();

  const [dataSource, setDataSource] = useState<DataSource>();
  const [files, setFiles] = useState<FileMetadata[]>();
  const [sourceCols, setSourceCols] = useState<SourceColumn[]>();
  const [loading, setLoading] = useState<boolean>(true);
  const [colsLoading, setColsLoading] = useState<boolean>();
  const [searchTerm, setSearchTerm] = useState(''); // Setting table search field
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null); // Setting table grid sort order
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
  });

  // get data source and file metadata
  useEffect(() => {
    if (!customerId) {
      return;
    }

    const loadData = async () => {
      setLoading(true);

      const dataSourceRes = await fetch(
        `/api/dataSources/getCustomerDataSource?dataSourceId=${dataSourceId}`,
      );

      const dataSource = await dataSourceRes.json();

      setDataSource(dataSource);

      const { sourceRecordName } = dataSource.config;

      const filesRes = await fetch(
        `/api/dataSources/getGenericObjects?customerKey=${customerKey}&sourceRecordName=${sourceRecordName}`,
      );

      const genericFiles = await filesRes.json();

      setFiles(genericFiles);

      setLoading(false);
    };

    loadData();
  }, [customerId, customerKey, dataSourceId]);

  // fetch source columns
  useEffect(() => {
    if (!customerKey || !dataSource) {
      return;
    }

    const getCols = async () => {
      setColsLoading(true);

      const res = await fetch(
        `/api/dataSources/getGenericSourceColumns?customerKey=${customerKey}&sourceRecordName=${
          dataSource.config!.sourceRecordName
        }`,
      );

      const sourceCols = await res.json();

      setSourceCols(sourceCols);

      setColsLoading(false);
    };

    getCols();
  }, [customerKey, dataSource]);

  const filteredCols = useMemo(
    () =>
      filterData<SourceColumn>(sourceCols || [], {
        searchTerm,
        nameField: 'columnName',
        filterConfig,
        filterFields,
        sortConfig,
      }),
    [sourceCols, searchTerm, filterConfig, sortConfig],
  );

  if (loading) {
    return <Loading />;
  }

  return (
    <PageContent>
      <FilesHeader loading={loading} dataSource={dataSource} files={files} />
      <div className="flex flex-col px-8 pb-28">
        <div className="w-full flex flex-col gap-10">
          <ChangeHistory />
          <div className="w-7/12 w-full min-w-full flex flex-col space-y-3">
            <div className="w-full flex justify-between items-center gap-8">
              <h2 className="text-2xl font-medium text-zinc-900">File data</h2>
              <div className="flex gap-3">
                <InputText
                  className="w-72"
                  icon="SearchMd"
                  placeholder="Search"
                  onChange={(e) => setSearchTerm(e.target.value)}
                  value={searchTerm}
                />
                <DataFilter
                  filterConfig={filterConfig}
                  onFilterConfigChange={setFilterConfig}
                  filterOptions={getFilterOptions('file')}
                />
              </div>
            </div>
            <div className="rounded-md border border-zinc-200 overflow-hidden">
              <table className="w-full border-collapse bg-white text-left text-sm text-zinc-900">
                <FileDataTableHead
                  sortConfig={sortConfig}
                  onSort={setSortConfig}
                  connectorDisplayName={dataSource!.displayName}
                />
                <FileDataTableBody
                  filteredCols={filteredCols}
                  connectorType={dataSource!.connector.connector_type}
                  loading={colsLoading}
                />
              </table>
            </div>
          </div>
        </div>
      </div>
    </PageContent>
  );
}
