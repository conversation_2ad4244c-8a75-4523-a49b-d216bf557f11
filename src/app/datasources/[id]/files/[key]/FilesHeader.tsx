import { <PERSON><PERSON><PERSON><PERSON>, Select, Button, Loading } from '@/components';
import { imgSrc } from 'lib/source';
import { useParams, usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { DataSource, FileMetadata } from '@/types';

type Params = {
  id: string;
  key: string;
};

type Props = {
  loading: boolean;
  dataSource?: DataSource;
  files?: FileMetadata[];
  messageContextHolder?: any;
};

export default function FilesHeader(props: Props) {
  const { loading, dataSource, files, messageContextHolder } = props;

  const { id: dataSourceId, key } = useParams() as Params;

  const fileKey = decodeURIComponent(key);

  const pathname = usePathname();
  const router = useRouter();

  if (loading || !files || !dataSource) {
    return (
      <div className="flex flex-col justify-between w-full px-8 mb-10">
        <Loading />
      </div>
    );
  }

  const { displayName, connector } = dataSource;

  const fileOpts = files.map((file) => ({
    label: file.displayName!,
    value: file.key.replace('partners/generic/land/', ''),
  }));

  const currentOptIdx = fileOpts.findIndex((opt) => opt.value === fileKey);
  const currentOpt = fileOpts[currentOptIdx];

  const prevOpt = fileOpts[currentOptIdx - 1];
  const nextOpt = fileOpts[currentOptIdx + 1];

  return (
    <PageTitle
      title={currentOpt?.label}
      titleIcon={
        <Image
          src={imgSrc.getOrDefault(connector.connector_type).imgPath}
          width={30}
          height={30}
          alt={`${connector.connector_type} Logo`}
        />
      }
      subpage
      breadcrumbs={[
        { label: 'Data sources', url: '/' },
        {
          label: displayName,
          url: `/datasources/${dataSourceId}`,
        },
        { label: currentOpt?.label, url: pathname },
      ]}
    >
      {messageContextHolder}
      <div className="flex gap-3 sm:mt-0 sm:flex-row sm:items-center">
        <Select
          options={fileOpts}
          className="w-80 ring-indigo-600"
          value={fileOpts.find((option) => option.value === fileKey)}
          image={
            <Image
              src={imgSrc.getOrDefault(connector.connector_type).imgPath}
              width={20}
              height={20}
              alt={`${connector.connector_type} Logo`}
            />
          }
          onChange={({ value }) => {
            router.push(`/datasources/${dataSourceId}/files/${encodeURIComponent(value)}`);
          }}
        />
        <Button
          iconOnly
          type="secondary"
          icon="ArrowLeft"
          disabled={!prevOpt}
          href={`/datasources/${dataSourceId}/files/${encodeURIComponent(prevOpt?.value)}`}
        />
        <Button
          iconOnly
          type="secondary"
          icon="ArrowRight"
          disabled={!nextOpt}
          href={`/datasources/${dataSourceId}/files/${encodeURIComponent(nextOpt?.value)}`}
        />
      </div>
    </PageTitle>
  );
}
