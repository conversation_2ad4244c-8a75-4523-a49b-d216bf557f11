import FileDataTableRow from './FileDataTableRow';
import { SourceColumn } from '@/types';
import { Loading } from '@/components';

type Props = {
  filteredCols: SourceColumn[];
  connectorType: string;
  loading?: boolean;
};

const tBodyClass = 'divide-y divide-gray-100 border-t border-gray-100';

export default function FileDataTableBody(props: Props) {
  const { filteredCols, connectorType, loading } = props;

  if (loading) {
    return (
      <tbody className={tBodyClass}>
        <tr>
          {/* @ts-ignore */}
          <td colSpan="100%">
            <div className="w-full flex justify-center items-center py-40">
              <Loading />
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  if (!filteredCols.length) {
    return (
      <tbody className={tBodyClass}>
        <tr>
          {/* @ts-ignore */}
          <td colSpan="100%">
            <div className="w-full flex justify-center items-center py-40">
              <p className="text-zinc-400">No fields</p>
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  return (
    <tbody className={tBodyClass}>
      {filteredCols.map((col, idx) => (
        <FileDataTableRow
          column={col}
          connectorType={connectorType}
          key={`${col.columnName}${idx}`}
        />
      ))}
    </tbody>
  );
}
