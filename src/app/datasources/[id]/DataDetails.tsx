import DataTables from './DataTables';
import DataFiles from './DataFiles';
import { DataSource, ModelSummary } from '@/types';

type Props = {
  dataSource: DataSource;
  useSmartMapper?: boolean;
  sourceModels?: ModelSummary[];
  sourceModelsLoading: boolean;
};

export default function DataDetails(props: Props) {
  const { dataSource, useSmartMapper, sourceModels, sourceModelsLoading } = props;

  const connectorType = dataSource.connector.connector_type;

  const isCustom = connectorType === 'custom';

  if (isCustom) {
    return <DataFiles dataSource={dataSource} />;
  }

  return (
    <DataTables
      connectorType={connectorType}
      useSmartMapper={useSmartMapper}
      sourceModels={sourceModels}
      sourceModelsLoading={sourceModelsLoading}
    />
  );
}
