import DataTables from './DataTables';
import DataFiles from './DataFiles';
import { useDataSourceStore } from '@/stores';
import { SourceModel } from '@/types';

type Props = {
  sourceModels: SourceModel[] | null;
  refreshing: boolean;
};

export default function DataDetails(props: Props) {
  const { sourceModels, refreshing } = props;

  const { currentDataSource: dataSource } = useDataSourceStore();

  const { connector_type } = dataSource!.connector;

  const isCustom = connector_type === 'custom';

  if (isCustom) {
    return <DataFiles />;
  }

  return <DataTables sourceModels={sourceModels} refreshing={refreshing} />;
}
