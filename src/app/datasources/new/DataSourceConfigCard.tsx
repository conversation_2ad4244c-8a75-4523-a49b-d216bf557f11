import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>ton, Card, Select, InputText, FileUpload, Progress } from '@/components';
import { Upload01 } from '@/components/icons';
import { useCustomer } from '@/hooks';
import { DataSource, ConnectorSyncFrequency, FileProgress } from '@/types';
import { createDbName } from '@/utils/string';
import { getAvgUploadProgress } from '@/utils/file';
import { message } from 'antd';
import { Formik, Form } from 'formik';
import { omit } from 'lodash';
import { handleFileUpload } from './utils';
import { api } from '@/helpers/web';

const DATA_SOURCE_COPY = `Adding data sources can take some time. If your desired source type is available below, our team will implement your new data source within 30 days. Otherwise, they can also add a custom source within 90 days.`;

// const RECURRING_COPY = `When enabled, this data source will sync based on the frequency set below. Otherwise, it will only sync at creation.`;

const sourceTypeOptions = [{ label: 'Other / custom data source upload', value: 'custom' }];

// const syncFrequencyOptions = [
//   { label: 'Daily', value: 'daily' },
//   { label: 'Hourly', value: 'hourly' },
// ];

const sourceImages = {
  custom: (
    <div className="w-8 h-8 rounded-lg flex justify-center items-center bg-indigo-50">
      <Upload01 className="w-4 h-4 stroke-indigo-600" />
    </div>
  ),
};

type FormValues = {
  sourceType: string | null;
  recurringEnabled: boolean;
  syncFrequency: ConnectorSyncFrequency | null;
  customSourceName: string | null;
};

const initialFormValues: FormValues = {
  sourceType: null,
  recurringEnabled: true,
  syncFrequency: 'hourly',
  customSourceName: null,
};

const getComplete = (values: FormValues, files: File[]) => {
  const { sourceType, recurringEnabled, syncFrequency, customSourceName } = values;

  const sourceInfoComplete = !!sourceType && !!customSourceName;
  const syncInfoComplete = (recurringEnabled && !!syncFrequency) || !recurringEnabled;
  const filesComplete = !!files.length;
  const infoComplete = sourceInfoComplete && syncInfoComplete && filesComplete;

  return infoComplete;
};

export default function DataSourceConfigCard() {
  const [existingGenericSources, setExistingGenericSources] = useState<DataSource[]>();
  const [files, setFiles] = useState<File[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [progress, setProgress] = useState<FileProgress>({});
  // total percent complete for all files
  const [uploadCompletion, setUploadCompletion] = useState<number>(0);

  const { customerId, customerKey } = useCustomer();
  const [messageApi, contextHolder] = message.useMessage();
  const router = useRouter();

  // get existing generic sources for validation
  useEffect(() => {
    if (!customerId) {
      return;
    }

    const getGenericSources = async () => {
      const res = await fetch(`/api/dataSources/getGenericDataSources?customerId=${customerId}`);

      const dataSources = await res.json();

      setExistingGenericSources(dataSources);
    };

    getGenericSources();
  }, [customerId]);

  // compute total file upload completion
  useEffect(() => {
    if (!submitting) {
      return;
    }

    const avgProgress = getAvgUploadProgress(progress);

    setUploadCompletion(avgProgress);
  }, [progress, submitting]);

  const validate = async (values: FormValues) => {
    const { customSourceName } = values;

    const errors: Record<string, any> = {};

    // This compares the new source name with the source record name we'll generate with db name standards (no special characters, underscores for spaces, etc)
    const duplicateSourceName =
      customSourceName &&
      existingGenericSources?.find(
        (existingSource) =>
          existingSource.config?.sourceRecordName === createDbName(customSourceName),
      );

    if (duplicateSourceName) {
      errors.customSourceName = 'Please choose a unique custom source name';
    }

    return errors;
  };

  const handleSubmit = async (values: FormValues) => {
    try {
      setSubmitting(true);

      const { sourceType, recurringEnabled, syncFrequency, customSourceName } = values;

      const infoComplete = getComplete(values, files);

      if (!infoComplete) {
        throw new Error('Invalid info props');
      }

      const fileInfo = await handleFileUpload({
        customSourceName: customSourceName!,
        customerKey,
        files,
        onProgress: setProgress,
      });

      const reqBody = {
        customerId,
        customSourceName,
        sourceType,
        recurringEnabled,
        syncFrequency,
        // don't pass file body to server
        fileInfo: fileInfo.map((info) => omit(info, 'file')),
      };

      const { success } = await api.post(`/api/dataSources/createNewDataSource`, reqBody);

      if (success) {
        messageApi.open({ type: 'success', content: 'New data source created successfully' });

        router.push('/');
      } else {
        messageApi.open({ type: 'error', content: 'Error creating new data source' });
      }

      setSubmitting(false);
      setUploadCompletion(100);
    } catch (error: any) {
      console.error({ error, stack: error.stack });

      setSubmitting(false);
      setUploadCompletion(0);
    }
  };

  return (
    <Formik initialValues={initialFormValues} validate={validate} onSubmit={handleSubmit}>
      {(formProps) => {
        const { errors, setFieldValue } = formProps;
        const values = formProps.values as FormValues;

        const infoComplete = getComplete(values, files);

        return (
          <Form>
            {contextHolder}
            <Card>
              <div className="flex h-full justify-between divide-x">
                <div className="flex flex-col w-full px-6 gap-y-6">
                  <div className="flex flex-col gap-y-2 w-full">
                    <span className="text-zinc-900 font-medium">Source type</span>
                    <Select
                      placeholder="Select type"
                      className="w-full"
                      options={sourceTypeOptions}
                      formatOptionLabel={({ label, value }) => (
                        <div className="flex items-center gap-x-2">
                          {sourceImages[value]}
                          <span className="text-sm text-zinc-900">{label}</span>
                        </div>
                      )}
                      value={sourceTypeOptions.find((opt) => opt.value === values.sourceType)}
                      onChange={(opt) => setFieldValue('sourceType', opt?.value)}
                    />
                  </div>
                  <div className="flex flex-col gap-y-2 w-full">
                    <span className="text-zinc-900 font-medium">Source name</span>
                    <InputText
                      placeholder="Enter your custom source name"
                      icon=""
                      onChange={(e) => {
                        setFieldValue('customSourceName', e.target.value);
                      }}
                      value={values.customSourceName}
                      error={!!errors.customSourceName}
                      errorMessage={errors.customSourceName}
                    />
                  </div>
                  <FileUpload
                    allowedTypes={['.csv', '.xlsx']}
                    files={files}
                    onFilesChange={setFiles}
                  />
                  {/* <div className="flex flex-col">
              <div className="flex justify-between">
                <span className="text-zinc-900 font-medium">Enable recurring sync</span>
                <Toggle
                  checked={recurringEnabled}
                  onChange={() => {
                    setRecurringEnabled(!recurringEnabled);
                  }}
                />
              </div>
              <span className="text-zinc-500 text-sm w-2/3">{RECURRING_COPY}</span>
            </div>
            <div className="flex flex-col gap-y-2">
              <span className="text-zinc-900 font-medium">Sync frequency</span>
              <Select
                placeholder={
                  recurringEnabled ? 'Select frequency' : 'Enabled recurring sync to edit'
                }
                disabled={!recurringEnabled}
                className="w-full"
                options={syncFrequencyOptions}
                value={syncFrequencyOptions.find((opt) => opt.value === syncFrequency)}
                onChange={({ value }) => {
                  setSyncFrequency(value);
                }}
              />
            </div> */}
                  <Button
                    buttonType="submit"
                    label={submitting ? 'Loading data...' : 'Add new data source'}
                    disabled={!infoComplete || submitting || !!Object.keys(errors).length}
                  />
                  {submitting && <Progress completed={uploadCompletion} />}
                </div>
                <div className="w-full px-6">
                  <div className="w-2/3">
                    <span className="text-zinc-500 text-sm">{DATA_SOURCE_COPY}</span>
                  </div>
                </div>
              </div>
            </Card>
          </Form>
        );
      }}
    </Formik>
  );
}
