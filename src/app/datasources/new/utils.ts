import { generateGenericFileName } from '@/utils/dataSources';
import { customerKeyToBucket } from '@/utils/string';
import { SetStateFunction, FileProgress, FileInfo } from '@/types';
import { fileTypes } from '@/constants/file';
import dayjs from 'dayjs';
import { map, pick } from 'lodash';
import axios from 'axios';

type GenFileInfoProps = {
  files: File[];
  customSourceName: string;
  customerKey: string;
  includeFile?: boolean;
};

export function genFileInfo(props: GenFileInfoProps): FileInfo[] {
  const { files, customSourceName, customerKey, includeFile } = props;

  const currentTimestamp = dayjs().format('YYYYMMDDHHmmss');

  return files.map((file) => {
    const fileName = generateGenericFileName({
      sourceName: customSourceName,
      originalFileName: file.name,
      currentTimestamp,
    });

    const info: FileInfo = {
      bucket: customerKeyToBucket(customerKey),
      key: `partners/generic/land/${fileName}`,
      fileName,
      metadata: {
        fileSize: file.size,
        fileType: fileTypes[file.type] || file.type,
      },
    };

    if (includeFile) {
      info.file = file;
    }

    return info;
  });
}

type UploadFilesProps = {
  fileInfo: FileInfo[];
  onProgress: SetStateFunction<FileProgress>;
};

type ObjectInfo = {
  bucket: string;
  key: string;
};

export async function uploadFiles(props: UploadFilesProps) {
  const { fileInfo, onProgress } = props;

  const keyFiles: Record<string, File> = {};
  const objects: ObjectInfo[] = [];

  fileInfo.forEach((info) => {
    const objInfo = pick(info, ['bucket', 'key']);

    keyFiles[info.key] = info.file!;
    objects.push(objInfo);
  });

  const urlRes = await fetch(
    `/api/storage/getPresignedUrls?objectInfo=${JSON.stringify(objects)}&method=PUT`,
  );

  const keyUrls = await urlRes.json();

  await Promise.all(
    map(keyUrls, async (url, key) => {
      const onUploadProgress = (progressEvent) => {
        const { loaded, total } = progressEvent;

        const ratio = loaded / total;

        const percentComplete = Math.round(ratio * 100);

        onProgress((prev) => ({ ...prev, [key]: percentComplete }));
      };

      await axios.put(url, keyFiles[key], { onUploadProgress });
    }),
  );
}

type HandleUploadProps = {
  files: File[];
  customSourceName: string;
  customerKey: string;
  onProgress: SetStateFunction<FileProgress>;
};

export async function handleFileUpload(props: HandleUploadProps): Promise<FileInfo[]> {
  const { files, customSourceName, customerKey, onProgress } = props;

  const fileInfo = genFileInfo({
    files,
    customSourceName,
    customerKey,
    includeFile: true,
  });

  await uploadFiles({
    fileInfo,
    onProgress,
  });

  return fileInfo;
}
