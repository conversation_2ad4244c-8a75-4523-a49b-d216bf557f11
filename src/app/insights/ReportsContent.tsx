import { useState, useEffect } from 'react';
import { useCustomer, usePage, useFeatureFlags } from '@/hooks';
import { api } from '@/helpers/web';
import { Loading } from '@/components';
import ReportsHeader from './ReportsHeader';

export default function ReportsContent() {
  const [omniUrl, setOmniUrl] = useState<string | undefined>(undefined);
  const { customerDashboards, customerConfig } = useCustomer();
  const { omniOrgName, omniSecretPath, omniConnectionId } = customerConfig || {};
  const { setPageProps } = usePage();
  const { featureFlags } = useFeatureFlags();

  const appModeEnabled = featureFlags['can-embed-workbook']?.enabled;

  useEffect(() => {
    async function getOmniUrl() {
      const reqBody = {
        contentId: customerDashboards?.[0]?.content_id,
        omniOrgName,
        omniSecretPath,
        omniConnectionId,
        appModeEnabled,
      };

      const { url } = await api.post(`/api/reports/omniUrl`, reqBody);

      setOmniUrl(url);
    }

    getOmniUrl();
  }, [customerDashboards, omniOrgName, omniSecretPath, omniConnectionId, appModeEnabled]);

  // collapse sidebar for full page reports view (embedded omni application mode)
  useEffect(() => {
    if (appModeEnabled) {
      setPageProps({ sidebarOpen: false });
    }
  }, [setPageProps, appModeEnabled]);

  if (!omniUrl) {
    return <Loading />;
  }

  return (
    <>
      {!appModeEnabled && <ReportsHeader />}
      <iframe src={omniUrl} height="100%" className="rounded-md" />
    </>
  );
}
