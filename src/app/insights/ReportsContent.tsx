import { useState, useEffect } from 'react';
import { useCustomer, usePage } from '@/hooks';
import { api } from '@/helpers/web';
import { Loading } from '@/components';

export default function ReportsContent() {
  const [omniUrl, setOmniUrl] = useState<string | undefined>(undefined);
  const { customerConfig } = useCustomer();
  const { omniOrgName, omniSecretPath, omniConnectionId } = customerConfig || {};
  const { setPageProps } = usePage();

  useEffect(() => {
    if (!omniConnectionId) {
      return;
    }

    async function getOmniUrl() {
      const reqBody = {
        omniOrgName,
        omniSecretPath,
        omniConnectionId,
      };

      const { url } = await api.post(`/api/reports/omniUrl`, reqBody);

      setOmniUrl(url);
    }

    getOmniUrl();
  }, [omniOrgName, omniSecretPath, omniConnectionId]);

  // collapse sidebar for full page reports view (embedded omni application mode)
  useEffect(() => {
    setPageProps({ sidebarOpen: false });
  }, [setPageProps]);

  if (!omniUrl) {
    return <Loading />;
  }

  return <iframe src={omniUrl} height="100%" className="rounded-md" />;
}
