import { useFormikContext, FormikErrors } from 'formik';
import { InputText, Button } from '@/components';
import { FormValues } from './DataSourceConfigCard';
import { NewCustomerDataSource } from '@/types';

type Props = {
  idx: number;
  onRemove: (idx: number) => void;
};

export default function DataSourceConfigRow(props: Props) {
  const { idx, onRemove } = props;
  const { setFieldValue, errors, values } = useFormikContext<FormValues>();

  const dataSourceError = errors.dataSources?.[idx] as FormikErrors<NewCustomerDataSource>;
  const dataSource = values.dataSources[idx];

  return (
    <div className="flex gap-x-4 items-center">
      <InputText
        search={false}
        icon=""
        placeholder="Connector slug"
        value={dataSource?.connectorSlug || ''}
        onChange={(e) => {
          setFieldValue(`dataSources.${idx}.connectorSlug`, e.target.value);
        }}
        style={{ inputWidth: 'w-60' }}
        error={!!dataSourceError?.connectorSlug}
        errorMessage={dataSourceError?.connectorSlug}
      />
      <InputText
        search={false}
        icon=""
        placeholder="Connector type"
        value={dataSource?.connectorType || ''}
        onChange={(e) => {
          setFieldValue(`dataSources.${idx}.connectorType`, e.target.value);
        }}
        style={{ inputWidth: 'w-60' }}
        error={!!dataSourceError?.connectorType}
        errorMessage={dataSourceError?.connectorType}
      />
      <InputText
        search={false}
        icon=""
        placeholder="Display name"
        value={dataSource?.displayName || ''}
        onChange={(e) => {
          setFieldValue(`dataSources.${idx}.displayName`, e.target.value);
        }}
        style={{ inputWidth: 'w-60' }}
        error={!!dataSourceError?.displayName}
        errorMessage={dataSourceError?.displayName}
      />
      <InputText
        search={false}
        icon=""
        placeholder="Source model key"
        value={dataSource?.sourceModelKey || ''}
        onChange={(e) => {
          setFieldValue(`dataSources.${idx}.sourceModelKey`, e.target.value);
        }}
        style={{ inputWidth: 'w-60' }}
        error={!!dataSourceError?.sourceModelKey}
        errorMessage={dataSourceError?.sourceModelKey}
      />

      {idx > 0 && (
        <Button
          type="secondary"
          theme="critical"
          onClick={() => {
            onRemove(idx);
          }}
          iconOnly
          icon="Trash01"
        />
      )}
    </div>
  );
}
