'use client';

import { PageContent, PageTitle, Loading } from '@/components';
import MemberInviteCard from './MemberInviteCard';
import DataSourceConfigCard from './DataSourceConfigCard';
import { useUserStore } from '@/stores';

export default function ConsolePage() {
  const { user } = useUserStore();

  if (!user) {
    return (
      <PageContent>
        <Loading />
      </PageContent>
    );
  }

  // Only display content for admin users
  if (!user?.isAdmin) {
    return null;
  }

  return (
    <PageContent>
      <PageTitle title="Management console" />
      <div className="flex flex-col gap-y-8">
        <MemberInviteCard />
        <DataSourceConfigCard />
      </div>
    </PageContent>
  );
}
