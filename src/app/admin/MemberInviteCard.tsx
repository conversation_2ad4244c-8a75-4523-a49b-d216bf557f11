import { useState, useCallback } from 'react';
import { Formik, Form, FieldArray, FormikErrors, FormikHelpers } from 'formik';
import { <PERSON><PERSON>, Card } from '@/components';
import { UsersPlus, Plus } from '@/components/icons';
import MemberInviteRow from './MemberInviteRow';
import { validateEmail } from '@/utils/validation';
import { MemberInvite } from '@/types';
import { SendMemberInvites } from '@/types/routes';
import { api } from '@/helpers/web';
import { useCustomer } from '@/hooks';

export type FormInvite = {
  email: string;
  appMetadata: { omni_connection_role: string; omni_content_role: string };
};

export type FormValues = {
  invites: FormInvite[];
};

const initialValues: FormValues = {
  invites: [{ email: '', appMetadata: { omni_connection_role: '', omni_content_role: '' } }],
};

export default function MemberInviteCard() {
  const [submitting, setSubmitting] = useState<boolean>(false);
  const { customerKey } = useCustomer();

  const handleValidate = useCallback((values: FormValues) => {
    const { invites } = values;

    const errors: FormikErrors<FormValues> = {};
    const inviteErrors: FormikErrors<FormInvite>[] = [];

    invites.forEach((invite, idx) => {
      const { email, appMetadata } = invite;
      const inviteError: FormikErrors<FormInvite> = {};

      // Check if this is an empty row (all fields empty)
      const isEmptyRow =
        !email && !appMetadata.omni_connection_role && !appMetadata.omni_content_role;

      // Only validate if the row has some content
      if (!isEmptyRow) {
        if (!email.length || !validateEmail(email)) {
          inviteError.email = 'Please enter a valid email address';
        }

        if (!appMetadata.omni_connection_role.length) {
          inviteError.appMetadata = {
            ...(inviteError.appMetadata || {}),
            omni_connection_role: 'Required',
          };
        }

        if (!appMetadata.omni_content_role.length) {
          inviteError.appMetadata = {
            ...(inviteError.appMetadata || {}),
            omni_content_role: 'Required',
          };
        }
      }

      inviteErrors[idx] = inviteError;
    });

    const hasErrors = inviteErrors.some((inviteError) => Object.keys(inviteError).length > 0);

    if (hasErrors) {
      errors.invites = inviteErrors;
    }

    return errors;
  }, []);

  const handleSubmit = useCallback(
    async (values: FormValues, { resetForm }: FormikHelpers<FormValues>) => {
      setSubmitting(true);
      const invites = values.invites as MemberInvite[];

      const reqBody: SendMemberInvites = { invites, customerKey };

      await api.post('/api/management/sendMemberInvites', reqBody);

      // Clear the form after successful submission
      resetForm();
      setSubmitting(false);
    },
    [customerKey],
  );

  return (
    <Formik initialValues={initialValues} validate={handleValidate} onSubmit={handleSubmit}>
      {({ values, isValid, dirty }) => {
        return (
          <Form>
            <Card>
              <div className="flex flex-col gap-y-4 w-full h-full">
                <div className="flex gap-x-2 items-center">
                  <UsersPlus className="stroke-zinc-900" />
                  <span className="text-zinc-900 text-xl">Invite members to organization</span>
                </div>
                <FieldArray name="invites">
                  {({ push, remove }) => (
                    <>
                      {values.invites.map((_, idx) => (
                        <MemberInviteRow key={idx} idx={idx} onRemove={remove} />
                      ))}
                      <button
                        type="button"
                        className="flex gap-x-2 items-center"
                        onClick={() => {
                          push({
                            email: '',
                            appMetadata: { omni_connection_role: '', omni_content_role: '' },
                          });
                        }}
                      >
                        <Plus className="stroke-zinc-600" />
                        <span className="text-zinc-600">Add more</span>
                      </button>
                    </>
                  )}
                </FieldArray>
                <div className="w-full flex justify-end">
                  <Button
                    label={submitting ? 'Submitting...' : 'Submit'}
                    buttonType="submit"
                    disabled={submitting || !isValid || !dirty}
                  />
                </div>
              </div>
            </Card>
          </Form>
        );
      }}
    </Formik>
  );
}
