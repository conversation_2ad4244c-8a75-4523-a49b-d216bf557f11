import { useFormikContext, FormikErrors } from 'formik';
import { InputText, Select, Button } from '@/components';
import { FormValues, FormInvite } from './MemberInviteCard';
import { Option } from '@/types';

type Props = {
  idx: number;
  onRemove: (idx: number) => void;
  contentGroupOpts: Option[];
};

const connectionRoleOptions = [
  { label: 'Viewer', value: 'viewer' },
  { label: 'Restricted querier', value: 'restricted_querier' },
  { label: 'Querier', value: 'querier' },
];

const contentRoleOptions = [
  { label: 'No Access', value: 'no_access' },
  { label: 'Viewer', value: 'viewer' },
  { label: 'Editor', value: 'editor' },
  { label: 'Manager', value: 'manager' },
];

export default function MemberInviteRow(props: Props) {
  const { idx, onRemove, contentGroupOpts } = props;
  const { values, setFieldValue, errors } = useFormikContext<FormValues>();

  const inviteError = errors.invites?.[idx] as FormikErrors<FormInvite>;
  const invite = values.invites[idx];

  return (
    <div className="flex gap-x-4 items-center">
      <InputText
        search={false}
        icon=""
        placeholder="Email address"
        value={invite?.email || ''}
        onChange={(e) => {
          setFieldValue(`invites.${idx}.email`, e.target.value);
        }}
        style={{ inputWidth: 'w-80' }}
        error={!!inviteError?.email}
        errorMessage={inviteError?.email}
      />
      <Select
        placeholder="Report connection role"
        options={connectionRoleOptions}
        value={
          invite?.appMetadata.omni_connection_role
            ? connectionRoleOptions.find(
                (opt) => opt.value === invite?.appMetadata.omni_connection_role,
              )
            : null
        }
        onChange={(opt) => {
          setFieldValue(`invites.${idx}.appMetadata.omni_connection_role`, opt?.value);
        }}
        errorMessage={inviteError?.appMetadata?.omni_connection_role}
      />
      <Select
        placeholder="Report content role"
        options={contentRoleOptions}
        value={
          invite?.appMetadata.omni_content_role
            ? contentRoleOptions.find((opt) => opt.value === invite?.appMetadata.omni_content_role)
            : null
        }
        onChange={(opt) => {
          setFieldValue(`invites.${idx}.appMetadata.omni_content_role`, opt?.value);
        }}
        errorMessage={inviteError?.appMetadata?.omni_content_role}
      />
      <Select
        placeholder="Content groups"
        options={contentGroupOpts}
        value={
          invite?.appMetadata.omni_content_groups?.map((group) => ({
            label: group,
            value: group,
          })) || []
        }
        onChange={(opts) => {
          const selectedValues = opts.map((opt) => opt.value);
          setFieldValue(`invites.${idx}.appMetadata.omni_content_groups`, selectedValues);
        }}
        errorMessage={
          Array.isArray(inviteError?.appMetadata?.omni_content_groups)
            ? inviteError?.appMetadata?.omni_content_groups.join(', ')
            : inviteError?.appMetadata?.omni_content_groups
        }
        isMulti
      />
      {idx > 0 && (
        <Button
          type="secondary"
          theme="critical"
          onClick={() => {
            onRemove(idx);
          }}
          iconOnly
          icon="Trash01"
        />
      )}
    </div>
  );
}
