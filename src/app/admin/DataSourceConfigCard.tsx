import { useState, useCallback } from 'react';
import { Formik, Form, FieldArray, FormikErrors, FormikHelpers } from 'formik';
import { <PERSON>ton, Card } from '@/components';
import { Database01, Plus } from '@/components/icons';
import DataSourceConfigRow from './DataSourceConfigRow';
import { NewCustomerDataSource, ConnectCustomerDataSourcesProps } from '@/types';
import { api } from '@/helpers/web';
import { useCustomer } from '@/hooks';

export type FormValues = {
  dataSources: NewCustomerDataSource[];
};

const initialValues: FormValues = {
  dataSources: [
    {
      connectorSlug: '',
      connectorType: '',
      displayName: '',
      sourceModelKey: '',
    },
  ],
};

export default function DataSourceConfigCard() {
  const [submitting, setSubmitting] = useState<boolean>(false);
  const { customerKey } = useCustomer();

  const handleValidate = useCallback((values: FormValues) => {
    const { dataSources } = values;

    const errors: FormikErrors<FormValues> = {};
    const dataSourceErrors: FormikErrors<NewCustomerDataSource>[] = [];

    dataSources.forEach((dataSource, idx) => {
      const { connectorSlug, connectorType, displayName, sourceModelKey } = dataSource;
      const inviteError: FormikErrors<NewCustomerDataSource> = {};

      // Check if this is an empty row (all fields empty)
      const isEmptyRow = !connectorSlug && !connectorType && !displayName && !sourceModelKey;

      // Only validate if the row has some content
      if (!isEmptyRow) {
        if (!connectorSlug.length) {
          inviteError.connectorSlug = 'Required';
        }

        if (!connectorType.length) {
          inviteError.connectorType = 'Required';
        }

        if (!displayName?.length) {
          inviteError.displayName = 'Required';
        }

        if (!sourceModelKey?.length) {
          inviteError.sourceModelKey = 'Required';
        }
      }

      dataSourceErrors[idx] = inviteError;
    });

    const hasErrors = dataSourceErrors.some((inviteError) => Object.keys(inviteError).length > 0);

    if (hasErrors) {
      errors.dataSources = dataSourceErrors;
    }

    return errors;
  }, []);

  const handleSubmit = useCallback(
    async (values: FormValues, { resetForm }: FormikHelpers<FormValues>) => {
      setSubmitting(true);

      const dataSources = values.dataSources as NewCustomerDataSource[];

      const reqBody: ConnectCustomerDataSourcesProps = { dataSources, customerKey };

      await api.post('/api/management/connectCustomerDataSources', reqBody);

      // Clear the form after successful submission
      resetForm();
      setSubmitting(false);
    },
    [customerKey],
  );

  return (
    <Formik initialValues={initialValues} validate={handleValidate} onSubmit={handleSubmit}>
      {({ values, isValid, dirty }) => {
        return (
          <Form>
            <Card>
              <div className="flex flex-col gap-y-4 w-full h-full">
                <div className="flex gap-x-2 items-center">
                  <Database01 className="stroke-zinc-900" />
                  <span className="text-zinc-900 text-xl">Connect data sources</span>
                </div>
                <FieldArray name="dataSources">
                  {({ push, remove }) => (
                    <>
                      {values.dataSources.map((_, idx) => (
                        <DataSourceConfigRow key={idx} idx={idx} onRemove={remove} />
                      ))}
                      <button
                        className="flex gap-x-2 items-center"
                        onClick={() => {
                          push({
                            connectorSlug: '',
                            connectorType: '',
                            displayName: '',
                            sourceModelKey: '',
                          });
                        }}
                      >
                        <Plus className="stroke-zinc-600" />
                        <span className="text-zinc-600">Add more</span>
                      </button>
                    </>
                  )}
                </FieldArray>
                <div className="w-full flex justify-end">
                  <Button
                    label={submitting ? 'Submitting...' : 'Submit'}
                    buttonType="submit"
                    disabled={submitting || !isValid || !dirty}
                  />
                </div>
              </div>
            </Card>
          </Form>
        );
      }}
    </Formik>
  );
}
