import { groupBy } from 'lodash';
import DictionaryTableSection from './DictionaryTableSection';
import { TargetModel } from '@/types';

type Props = {
  models: TargetModel[];
};

export default function DictionaryTables(props: Props) {
  const { models } = props;

  if (!models.length) {
    return (
      <div className="w-full flex justify-center items-center py-40">
        <span className="text-zinc-400">No models to display.</span>
      </div>
    );
  }

  const modelsByType = groupBy(models, 'type');

  return (
    <div className="flex flex-col gap-y-8">
      {!!modelsByType.core?.length && (
        <DictionaryTableSection modelType="core" models={modelsByType.core} />
      )}
      {!!modelsByType.consumption?.length && (
        <DictionaryTableSection modelType="consumption" models={modelsByType.consumption} />
      )}
      {!!modelsByType.metrics?.length && (
        <DictionaryTableSection modelType="metrics" models={modelsByType.metrics} />
      )}
    </div>
  );
}
