'use client';
import { useMemo, useState } from 'react';
import { <PERSON><PERSON>, PageContent, InputText, Spinner, Button, MenuTooltip } from '@/components';
import { useDictionaryStore } from '@/stores';
import { useCustomer } from '@/hooks';
import { filterModel, filterField } from './utils';
import DictionaryTables from './DictionaryTables';

export default function Dictionary() {
  const {
    targetModels: models,
    targetSearchTerm,
    setTargetSearchTerm,
    fetchTargetModels,
  } = useDictionaryStore();

  const { customerKey, customerId } = useCustomer();
  const [refreshing, setRefreshing] = useState(false);

  const loading = !models || !!refreshing;

  const handleHardRefresh = async () => {
    setRefreshing(true);

    try {
      // Invalidate target models cache and force refresh
      await fetchTargetModels({ customerKey, customerId, forceRefresh: true });
    } catch (error) {
      console.error('Failed to refresh dictionary:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // This filters the models and their dictionaries, so model.dictionary will only include
  // matching fields. This allows us to provide a count of matching fields.
  const filteredModels = useMemo(() => {
    const initialFiltered =
      models?.filter(
        (model) =>
          filterModel(model, targetSearchTerm) ||
          model.dictionary.some((field) => filterField(field, targetSearchTerm)),
      ) || [];

    const withFilteredDict = initialFiltered.map((model) => ({
      ...model,
      dictionary: model.dictionary.filter((field) => filterField(field, targetSearchTerm)),
    }));

    return withFilteredDict;
  }, [models, targetSearchTerm]);

  if (loading) {
    return (
      <PageContent>
        <Header>Gestalt Data Dictionary</Header>
        <div className="flex items-center gap-4">
          <InputText
            placeholder="Search models"
            value={targetSearchTerm}
            onChange={(e) => setTargetSearchTerm(e.target.value)}
          />
          <>
            <Button
              type="secondary"
              iconOnly
              icon="RefreshCw05"
              onClick={handleHardRefresh}
              disabled={refreshing}
              tooltipText="Hard refresh"
            />
          </>
        </div>
        <div className="flex justify-center pt-10">
          <Spinner />
        </div>
      </PageContent>
    );
  }

  return (
    <PageContent>
      <div className="flex flex-row items-center justify-between mb-8">
        <Header>Gestalt Data Dictionary</Header>
        <div className="flex items-center gap-4">
          <InputText
            placeholder="Search models"
            value={targetSearchTerm}
            onChange={(e) => setTargetSearchTerm(e.target.value)}
          />
          <Button
            type="transparent"
            iconOnly
            icon="RefreshCw05"
            // label={refreshing ? 'Refreshing...' : 'Hard Refresh'}
            onClick={handleHardRefresh}
            disabled={refreshing}
          />
        </div>
      </div>
      <DictionaryTables models={filteredModels} />
    </PageContent>
  );
}
