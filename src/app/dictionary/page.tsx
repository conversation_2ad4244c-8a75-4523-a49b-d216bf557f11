'use client';
import { useMemo } from 'react';
import { Header, PageContent, InputText, Spinner } from '@/components';
import { useDictionaryStore } from '@/stores';
import { filterModel, filterField } from './utils';
import { groupBy } from 'lodash';
import DictionaryTableSection from './DictionaryTableSection';

export default function Dictionary() {
  const { targetModels: models, targetSearchTerm, setTargetSearchTerm } = useDictionaryStore();

  const loading = !models;

  const filteredModelsByType = useMemo(() => {
    const initialFiltered =
      models?.filter(
        (model) =>
          filterModel(model, targetSearchTerm) ||
          model.dictionary.some((field) => filterField(field, targetSearchTerm)),
      ) || [];

    const withFilteredDict = initialFiltered.map((model) => ({
      ...model,
      dictionary: model.dictionary.filter((field) => filterField(field, targetSearchTerm)),
    }));

    const byType = groupBy(withFilteredDict, 'type');

    return byType;
  }, [models, targetSearchTerm]);

  if (loading) {
    return (
      <PageContent>
        <div className="flex justify-center pt-10">
          <Spinner />
        </div>
      </PageContent>
    );
  }

  return (
    <PageContent>
      <div className="flex flex-row items-center justify-between mb-8">
        <Header>Gestalt Data Dictionary</Header>
        <InputText
          placeholder="Search models"
          value={targetSearchTerm}
          onChange={(e) => setTargetSearchTerm(e.target.value)}
        />
      </div>
      <div className="flex flex-col gap-y-8">
        {!!filteredModelsByType.core?.length && (
          <DictionaryTableSection modelType="core" models={filteredModelsByType.core} />
        )}
        {!!filteredModelsByType.consumption?.length && (
          <DictionaryTableSection
            modelType="consumption"
            models={filteredModelsByType.consumption}
          />
        )}
        {!!filteredModelsByType.metrics?.length && (
          <DictionaryTableSection modelType="metrics" models={filteredModelsByType.metrics} />
        )}
      </div>
    </PageContent>
  );
}
