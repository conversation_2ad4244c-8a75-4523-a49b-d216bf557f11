'use client';
import { useState } from 'react';
import Image from 'next/image';
import { useFeatureFlags } from '../../hooks';
import { <PERSON>er, PageContent, Button, Card, InputText, Spinner } from '@/components';
import DictionaryTableRow from './DictionaryTableRow';
import { useDictionaryStore } from '@/stores';

export default function Dictionary() {
  const { featureFlags } = useFeatureFlags();
  const { models } = useDictionaryStore();

  const [modelSearchTerm, setModelSearchTerm] = useState('');

  const loading = !models;

  const canReviewDefs = featureFlags['can-review-dict-defs']?.enabled;

  if (loading) {
    return (
      <PageContent>
        <div className="flex justify-center pt-10">
          <Spinner />
        </div>
      </PageContent>
    );
  }

  const filteredModels = models.filter((model) =>
    model.name.toLowerCase().includes(modelSearchTerm.toLowerCase()),
  );

  return (
    <PageContent>
      <div className="flex flex-row items-center justify-between mb-8">
        <Header>Gestalt Data Dictionary</Header>
        <InputText onChange={(e) => setModelSearchTerm(e.target.value)} />
      </div>
      {canReviewDefs && (
        <div className="mb-8">
          <Card>
            <div className="mb-6">
              <Image src="/dictionary.svg" style={{ width: '50%' }} alt="Dictionary logo" />
            </div>
            <span className="text-2xl mb-3">
              It looks like you haven’t reviewed all of your definitions yet.
            </span>
            <span className="text-slate-400 mb-3">
              Review and set how your company defines industry standard terms.
            </span>
            <Button label="Review definitions" />
          </Card>
        </div>
      )}
      <div className="mb-4">
        <Header kind="secondary">Models</Header>
      </div>
      <div className="p-4 bg-white rounded-xl">
        <table className="w-full">
          <tbody className="divide-y divide-gray-100">
            {filteredModels.map((model, idx) => (
              <DictionaryTableRow model={model} key={`${model.name}${idx}`} />
            ))}
          </tbody>
        </table>
      </div>
    </PageContent>
  );
}
