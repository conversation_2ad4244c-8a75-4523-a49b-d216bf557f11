'use client';
import { useMemo } from 'react';
import { Header, PageContent, InputText, Spinner } from '@/components';
import { useDictionaryStore } from '@/stores';
import { filterModel, filterField } from './utils';
import DictionaryTables from './DictionaryTables';

export default function Dictionary() {
  const { targetModels: models, targetSearchTerm, setTargetSearchTerm } = useDictionaryStore();

  const loading = !models;

  const filteredModels = useMemo(() => {
    const initialFiltered =
      models?.filter(
        (model) =>
          filterModel(model, targetSearchTerm) ||
          model.dictionary.some((field) => filterField(field, targetSearchTerm)),
      ) || [];

    const withFilteredDict = initialFiltered.map((model) => ({
      ...model,
      dictionary: model.dictionary.filter((field) => filterField(field, targetSearchTerm)),
    }));

    return withFilteredDict;
  }, [models, targetSearchTerm]);

  if (loading) {
    return (
      <PageContent>
        <div className="flex justify-center pt-10">
          <Spinner />
        </div>
      </PageContent>
    );
  }

  return (
    <PageContent>
      <div className="flex flex-row items-center justify-between mb-8">
        <Header>Gestalt Data Dictionary</Header>
        <InputText
          placeholder="Search models"
          value={targetSearchTerm}
          onChange={(e) => setTargetSearchTerm(e.target.value)}
        />
      </div>
      <DictionaryTables models={filteredModels} />
    </PageContent>
  );
}
