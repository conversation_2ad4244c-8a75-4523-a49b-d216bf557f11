import { Badge, FieldTypeIcon, CardTableRow } from '@/components';
import ColumnSubRow from './ColumnSubRow';
import { TargetModelDictionaryField } from '@/types';

type Props = {
  column: TargetModelDictionaryField;
  updatedAt: string;
};

export default function ColumnRow(props: Props) {
  const { column, updatedAt } = props;
  const { gen_field_type, field_name, field_description, mapping } = column;

  const leftIcon = <FieldTypeIcon type={gen_field_type} />;

  const isMapped = !!mapping;

  const statusContent = (
    <Badge label={isMapped ? 'Mapped' : 'Unmapped'} type={isMapped ? 'success' : 'neutral'} />
  );

  const subRow = <ColumnSubRow column={column} updatedAt={updatedAt} />;

  return (
    <CardTableRow
      leftIcon={leftIcon}
      leftContent={field_name}
      leftSubContent={field_description}
      statusContent={statusContent}
      subRow={subRow}
    />
  );
}
