import { Badge, FieldTypeIcon, CardTableRow } from '@/components';
import ColumnSubRow from './ColumnSubRow';
import { TargetModelDictionaryField } from '@/types';

type Props = {
  column: TargetModelDictionaryField;
};

export default function ColumnRow(props: Props) {
  const { column } = props;
  const { gen_field_type, field_name, field_description, mappings } = column;

  const leftIcon = <FieldTypeIcon type={gen_field_type} />;

  const isMapped = !!mappings?.length;

  const statusContent = (
    <Badge label={isMapped ? 'Mapped' : 'Unmapped'} type={isMapped ? 'success' : 'neutral'} />
  );

  const subRow = <ColumnSubRow column={column} />;

  return (
    <CardTableRow
      leftIcon={leftIcon}
      leftContent={field_name}
      leftSubContent={field_description}
      statusContent={statusContent}
      subRow={subRow}
    />
  );
}
