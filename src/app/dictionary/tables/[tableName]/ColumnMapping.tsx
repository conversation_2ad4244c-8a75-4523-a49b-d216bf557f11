import { ModelDictionaryField } from '@/types';
import { UnmappedField, SingleSourceMapping, MultipleSourcesMapping } from './mappings';

type Props = {
  column: ModelDictionaryField;
};

export default function ColumnMapping(props: Props) {
  const { column } = props;
  const { field_name, mapping, source_fields = [] } = column;

  const isMapped = !!mapping;
  const multipleSources = source_fields.length > 1;

  if (!isMapped) {
    return <UnmappedField fieldName={field_name} />;
  }

  if (multipleSources) {
    return (
      <MultipleSourcesMapping
        sourceFields={source_fields}
        fieldName={field_name}
        onSql={mapping.on_sql}
      />
    );
  }

  return (
    <SingleSourceMapping
      sourceField={source_fields[0]}
      fieldName={field_name}
      mappingType={mapping.mapping_type}
      onSql={mapping.on_sql}
    />
  );
}
