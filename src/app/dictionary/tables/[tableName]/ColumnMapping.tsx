import { isNil } from 'lodash';
import { TargetModelDictionaryFieldMapping } from '@/types';
import { UnmappedField, SingleSourceMapping, MultipleSourcesMapping } from './mappings';
import { useCustomerStore } from '@/stores';
import { getPackageDisplayOverride } from '@/utils/customer';

type Props = {
  fieldName: string;
  packageMapping?: TargetModelDictionaryFieldMapping;
};

export default function ColumnMapping(props: Props) {
  const { fieldName, packageMapping } = props;
  const { mapping, source_fields = [], package: packageName } = packageMapping || {};

  const { customer } = useCustomerStore();

  const displayPackage = getPackageDisplayOverride(packageName, customer?.customerConfig);

  const multipleSources = source_fields.length > 1;

  if (multipleSources) {
    return (
      <MultipleSourcesMapping
        sourceFields={source_fields}
        fieldName={fieldName}
        onSql={mapping?.on_sql}
        packageName={displayPackage}
      />
    );
  }

  const sourceField = source_fields[0];

  const isNullValue =
    sourceField?.type_field_source === 'static' && sourceField?.text_source_field === 'null';

  if (isNil(mapping) || isNullValue) {
    return <UnmappedField fieldName={fieldName} packageName={displayPackage} />;
  }

  return (
    <SingleSourceMapping
      sourceField={sourceField}
      fieldName={fieldName}
      mappingType={mapping.mapping_type}
      packageName={displayPackage}
      onSql={mapping.on_sql}
    />
  );
}
