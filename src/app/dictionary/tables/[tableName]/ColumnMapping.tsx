import { isNil } from 'lodash';
import { TargetModelDictionaryField } from '@/types';
import { UnmappedField, SingleSourceMapping, MultipleSourcesMapping } from './mappings';

type Props = {
  column: TargetModelDictionaryField;
};

export default function ColumnMapping(props: Props) {
  const { column } = props;
  const { field_name, mapping, source_fields = [] } = column;

  const multipleSources = source_fields.length > 1;

  if (multipleSources) {
    return (
      <MultipleSourcesMapping
        sourceFields={source_fields}
        fieldName={field_name}
        onSql={mapping?.on_sql}
      />
    );
  }

  const sourceField = source_fields[0];

  const isNullValue =
    sourceField?.type_field_source === 'static' && sourceField?.text_source_field === 'null';

  if (isNil(mapping) || isNullValue) {
    return <UnmappedField fieldName={field_name} />;
  }

  return (
    <SingleSourceMapping
      sourceField={sourceField}
      fieldName={field_name}
      mappingType={mapping.mapping_type}
      onSql={mapping.on_sql}
    />
  );
}
