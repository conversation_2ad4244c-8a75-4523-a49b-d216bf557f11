import { TargetModelDictionaryField } from '@/types';
import { UnmappedField, SingleSourceMapping, MultipleSourcesMapping } from './mappings';

type Props = {
  column: TargetModelDictionaryField;
};

export default function ColumnMapping(props: Props) {
  const { column } = props;
  const { field_name, mapping, source_fields = [] } = column;

  // source_fields list includes static values used in transform – filter to only include actual fields from the source
  const sourceSourceFields = source_fields.filter(
    (field) => field.type_field_source === 'source_field',
  );

  const isMapped = !!mapping;
  const multipleSources = sourceSourceFields.length > 1;

  if (!isMapped) {
    return <UnmappedField fieldName={field_name} />;
  }

  if (multipleSources) {
    return (
      <MultipleSourcesMapping
        sourceFields={sourceSourceFields}
        fieldName={field_name}
        onSql={mapping.on_sql}
      />
    );
  }

  return (
    <SingleSourceMapping
      sourceField={sourceSourceFields[0]}
      fieldName={field_name}
      mappingType={mapping.mapping_type}
      onSql={mapping.on_sql}
    />
  );
}
