import { SourceOption } from './CustomFieldsModal';
import Image from 'next/image';
import { Check } from '@/components/icons';
import { imgSrc } from 'lib/source';
import { SetStateFunction } from '@/types';

type Props = {
  sourceOptions: SourceOption[];
  selectedSource: SourceOption;
  onSelect: SetStateFunction<SourceOption>;
};

export default function SourceOptions(props: Props) {
  const { sourceOptions, selectedSource, onSelect } = props;

  return (
    <div className="flex gap-x-2">
      {sourceOptions.map((option, idx) => {
        const selected = option.type === selectedSource.type;

        let image = <div className="w-5 h-5" />;

        if (selected) {
          image = <Check className="w-5 h-5" />;
        } else if (option.type !== 'all') {
          image = (
            <Image
              src={imgSrc.getOrDefault(option.type).imgPath}
              width={20}
              height={20}
              className="brightness-0"
              alt={option.label}
            />
          );
        }

        let optionClass =
          'flex gap-x-2 px-2 py-1 items-center rounded-full border border-gray-200 hover:cursor-pointer';

        if (selected) {
          optionClass = `${optionClass} bg-indigo-600 text-white stroke-white`;
        }

        return (
          <div
            className={optionClass}
            key={`${option.label}${idx}`}
            onClick={() => {
              onSelect(option);
            }}
          >
            {image}
            <span className="text-sm">{option.label}</span>
          </div>
        );
      })}
    </div>
  );
}
