import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { message } from 'antd';
import { Modal, InputText, Loading, Button } from '@/components';
import { SetStateFunction, Source, CustomField } from '@/types';
import { useCustomer } from '@/hooks';
import CustomFieldList from './CustomFieldList';
import { api } from '@/helpers/web';

type Props = {
  isOpen: boolean;
  onOpenChange: SetStateFunction<boolean>;
  source: Source;
  onSubmit: () => Promise<void>;
  modelId?: string;
};

type Params = {
  tableName: string;
};

export type SourceOption = {
  type: string;
  label: string;
};

const BETA_MESSAGE = `Note: new custom field requests will be implemented during your next sync.`;

export default function CustomFieldsModal(props: Props) {
  const { isOpen, onOpenChange, source, onSubmit, modelId } = props;

  const { customerKey, customerId, customerName } = useCustomer();
  const { tableName } = useParams() as Params;
  const [messageApi, contextHolder] = message.useMessage();

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sourceOptions, setSourceOptions] = useState<SourceOption[]>();
  const [customFields, setCustomFields] = useState<CustomField[]>();
  const [selectedField, setSelectedField] = useState<CustomField | null>();
  const [submitting, setSubmitting] = useState<boolean>(false);

  // set source options
  useEffect(() => {
    if (!source.connectors) {
      return;
    }

    const options = [
      { type: 'all', label: 'All sources' },
      ...source.connectors.map((connector) => ({
        type: connector.connector_type,
        label: connector.display_name,
      })),
    ];

    setSourceOptions(options);
  }, [source]);

  // get list of custom fields
  useEffect(() => {
    if (!customerKey) {
      return;
    }

    const getCustomFields = async () => {
      const res = await fetch(
        `/api/dataDictionary/${tableName}/getCustomFields?customerKey=${customerKey}`,
      );

      const { customFields } = await res.json();

      setCustomFields(customFields);
    };

    getCustomFields();
  }, [customerKey, tableName]);

  const handleSubmit = async () => {
    setSubmitting(true);

    const { success } = await api.post(`/api/dataDictionary/${tableName}/addCustomFieldRequest`, {
      customerId,
      customerName,
      customField: selectedField,
      modelName: tableName,
      modelId,
    });

    if (success) {
      onOpenChange(false);

      await onSubmit();
    } else {
      messageApi.open({ type: 'error', content: 'Error requesting custom field.' });
    }

    setSubmitting(false);
    setSelectedField(null);
  };

  if (!sourceOptions) {
    return <Loading />;
  }

  return (
    <Modal
      title="Add custom field"
      open={isOpen}
      setIsOpen={onOpenChange}
      titleUnderline={true}
      width={'w-[754px]'}
    >
      {contextHolder}
      <div className="flex flex-col gap-y-8">
        <div className="px-4">
          <span className="text-sm text-gray-500">{BETA_MESSAGE}</span>
        </div>
        <div className="flex flex-col border border-gray-200 rounded-md gap-y-4 p-8">
          <InputText
            icon="SearchMd"
            placeholder={`Search custom fields`}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {/* <SourceOptions
            sourceOptions={sourceOptions}
            selectedSource={selectedSource}
            onSelect={setSelectedSource}
          /> */}
          <div className="h-96 overflow-scroll">
            <CustomFieldList
              customFields={customFields}
              searchTerm={searchTerm}
              selectedField={selectedField}
              onSelect={setSelectedField}
            />
          </div>
        </div>
        <div className="flex w-full justify-end items-center">
          <Button
            label={submitting ? 'Saving...' : 'Save field'}
            onClick={handleSubmit}
            disabled={!selectedField}
          />
        </div>
      </div>
    </Modal>
  );
}
