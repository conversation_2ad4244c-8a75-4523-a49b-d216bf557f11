import { ReactNode } from 'react';
import { TargetModelDictionaryField } from '@/types';
import ColumnMappingContent from './ColumnMappingContent';

type Props = {
  column: TargetModelDictionaryField;
};

export default function ColumnSubRow(props: Props) {
  const { column } = props;
  const { field_name, mappings } = column;

  let content: ReactNode = <ColumnMappingContent fieldName={field_name} />;

  if (mappings?.length) {
    content = mappings?.map((packageMapping) => (
      <ColumnMappingContent
        fieldName={field_name}
        packageMapping={packageMapping}
        key={packageMapping.mapping.on_sql}
      />
    ));
  }

  return <div className="flex flex-col px-4 py-4 gap-y-4 bg-gray-100 rounded-b-xl">{content}</div>;
}
