import { FieldTypeIcon, Card } from '@/components';
import InfoCard from './InfoCard';
import ColumnMapping from './ColumnMapping';
import { TargetModelDictionaryField } from '@/types';
import Dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

Dayjs.extend(customParseFormat);

type Props = {
  column: TargetModelDictionaryField;
  updatedAt: string;
};

export default function ColumnSubRow(props: Props) {
  const { column, updatedAt } = props;
  const { gen_field_type = '' } = column;

  const updateDisplayDate = Dayjs(updatedAt).format('LL');

  return (
    <div className="bg-gray-100 p-6 w-full h-full flex flex-col gap-10 font-medium text-sm text-gray-900 rounded-b-xl">
      <Card>
        <ColumnMapping column={column} />
      </Card>
      <div className="w-full flex flex-row justify-between gap-10">
        <InfoCard
          title="Type"
          infoIcon={<FieldTypeIcon type={gen_field_type} />}
          info={gen_field_type}
        />
        <InfoCard title="Last changed on:" info={updateDisplayDate} />
        <InfoCard title="Last changed by:" info="Gestalt" />
      </div>
    </div>
  );
}
