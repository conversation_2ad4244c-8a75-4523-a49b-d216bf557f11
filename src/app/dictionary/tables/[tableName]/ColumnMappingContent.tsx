import dayjs from 'dayjs';
import Image from 'next/image';
import { Card } from '@/components';
import ColumnMapping from './ColumnMapping';
import { TargetModelDictionaryFieldMapping } from '@/types';
import { imgSrc } from '@lib/source';

type Props = {
  fieldName: string;
  packageMapping?: TargetModelDictionaryFieldMapping;
};

export default function ColumnMappingContent(props: Props) {
  const { fieldName, packageMapping } = props;
  const { updated_at } = packageMapping || {};

  const updateDisplayDate = updated_at ? dayjs(updated_at).format('LL') : 'N/A';

  const gestaltImgConfig = imgSrc.getOrDefault('gestalt');
  const { imgPath, alt: imgAlt } = gestaltImgConfig;

  return (
    <div className="w-full h-full flex gap-4 font-medium text-sm text-gray-900">
      <div className="w-3/4">
        <Card paddingX="p-4" paddingY="p-6">
          <ColumnMapping fieldName={fieldName} packageMapping={packageMapping} />
        </Card>
      </div>
      <div className="w-1/4 flex gap-x-20">
        <Card paddingX="p-4" paddingY="p-6">
          <div className="flex w-full gap-x-6">
            <Image src={imgPath} alt={imgAlt} width={30} height={30} />
            <div className="flex flex-col">
              <span className="text-gray-500 text-xs">Last changed by Gestalt on:</span>
              <span>{updateDisplayDate}</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
