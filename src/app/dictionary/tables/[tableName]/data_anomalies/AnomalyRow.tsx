import { useState } from 'react';
import { cloneDeep } from 'lodash';
import { Anomaly, SetStateFunction, BaseField } from '@/types';
import { ReviewIcons, ExpanderIcon, FieldTypeIcon } from '@/components';
import { reviewStyles } from '@/components/ReviewIcons';
import AnomalySubRow from './AnomalySubRow';

type Props = {
  anomaly: Anomaly;
  onAnomaliesChange: SetStateFunction<Anomaly[]>;
};

export default function AnomalyRow(props: Props) {
  const { anomaly, onAnomaliesChange } = props;
  const {
    feature_name,
    comment,
    primary_key,
    review_status,
    data_dictionary_field = {} as BaseField,
  } = anomaly;

  const { gen_field_type } = data_dictionary_field;

  const [expanded, setExpanded] = useState(false);
  const [hovered, setHovered] = useState(false);

  const rowClass = review_status ? reviewStyles[review_status].row : reviewStyles.none.row;

  const handleReview = ({ currentReviewStatus, newReviewStatus, primary_key, feature_name }) => {
    onAnomaliesChange((prev) => {
      const anomaliesCopy = cloneDeep(prev);

      const currentAnomaly = anomaliesCopy.find(
        (stateAnomaly) =>
          stateAnomaly.primary_key === primary_key && stateAnomaly.feature_name === feature_name,
      );

      if (currentAnomaly) {
        if (currentReviewStatus === newReviewStatus) {
          currentAnomaly.review_status = 'none';
        } else {
          currentAnomaly.review_status = newReviewStatus;
        }
      }

      return anomaliesCopy;
    });
  };

  return (
    <div className="bg-white p-0.5 rounded-xl">
      <div
        className={`flex justify-between w-full rounded-xl px-8 py-4 hover:cursor-pointer ${rowClass}`}
        onClick={() => setExpanded(!expanded)}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        <div className="flex flex-col gap-y-2">
          <div className="flex gap-x-2">
            <FieldTypeIcon type={gen_field_type} />
            <span className="text-xl">{feature_name}</span>
          </div>
          <span className="text-lg">{comment}</span>
          <span className="text-zinc-500">Record ID: {primary_key}</span>
        </div>
        <div className="flex gap-x-4 items-center">
          <ReviewIcons
            reviewStatus={review_status}
            onReview={({ currentReviewStatus, newReviewStatus }) => {
              handleReview({ currentReviewStatus, newReviewStatus, primary_key, feature_name });
            }}
          />
          <ExpanderIcon expanded={expanded} hovered={hovered} />
        </div>
      </div>
      {expanded && <AnomalySubRow anomaly={anomaly} />}
    </div>
  );
}
