'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { sortBy } from 'lodash';
import { Loading, PageContent, PageTitle, GestaltTableIcon, Card } from '@/components';
import { useCustomer, useFeatureFlags } from '@/hooks';
import { Anomaly } from '@/types';
import AnomalyRow from './AnomalyRow';
import { getAvgDailyAnomalyCount } from './utils';

type Params = {
  tableName: string;
};

type Props = {
  params: Params;
};

export default function DataAnomalies(props: Props) {
  const { params } = props;
  const { tableName } = params;

  const [anomalies, setAnomalies] = useState<Anomaly[]>();

  const { customerKey, customerId } = useCustomer();
  const { featureFlags } = useFeatureFlags();

  const anomaliesEnabled = featureFlags['can-view-anomalies']?.enabled;

  useEffect(() => {
    if (!customerKey || !customerId) {
      return;
    }

    const getAnomalyData = async () => {
      const res = await fetch(
        `/api/dataDictionary/${tableName}/getAnomalyData?customerKey=${customerKey}&customerId=${customerId}`,
      );

      const resBody = await res.json();

      setAnomalies(resBody);
    };

    getAnomalyData();
  }, [customerKey, customerId, tableName]);

  if (!anomaliesEnabled) {
    return null;
  }

  if (!anomalies) {
    return (
      <PageContent>
        <Loading />
      </PageContent>
    );
  }

  const filteredAnomalies = sortBy(anomalies, 'feature_name');

  return (
    <PageContent>
      <PageTitle
        title={tableName}
        titleIcon={<GestaltTableIcon size="large" />}
        subpage
        breadcrumbs={[
          { label: 'Dictionary', url: '/dictionary' },
          {
            label: tableName,
            url: `/dictionary/tables/${tableName}`,
          },
          {
            label: 'Data anomalies',
            url: `/dictionary/tables/${tableName}/data_anomalies`,
          },
        ]}
      />
      <div className="flex justify-between items-center gap-x-24 mb-10">
        <div className="flex flex-col w-full gap-y-8">
          <div>
            <Card>
              <div className="flex flex-col items-center gap-y-4">
                <span>Total outstanding anomalies</span>
                <span className="text-2xl">{anomalies.length}</span>
              </div>
            </Card>
          </div>
          <div>
            <Card>
              <div className="flex flex-col items-center gap-y-4">
                <span>Avg. anomalies found daily</span>
                <span className="text-2xl">{getAvgDailyAnomalyCount(anomalies)}</span>
              </div>
            </Card>
          </div>
        </div>
        <div className="flex flex-col bg-white justify-between rounded-xl px-12 py-8 w-full h-full gap-y-2">
          <span className="text-xl">Data anomalies found over time</span>
          <div className="flex w-full h-full relative">
            <Image src="/img/placeholder-anomaly-metrics-2.png" fill alt="Anomaly metrics" />
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-y-6">
        <span className="text-2xl">Record anomalies</span>
        {filteredAnomalies.map((anomaly) => (
          <AnomalyRow
            anomaly={anomaly}
            key={`${anomaly.primary_key}${anomaly.feature_name}`}
            onAnomaliesChange={setAnomalies}
          />
        ))}
      </div>
    </PageContent>
  );
}
