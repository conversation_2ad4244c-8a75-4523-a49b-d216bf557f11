import { Anomaly } from '@/types';
import Dayjs from 'dayjs';
import { countBy, mean } from 'lodash';

type Options = {
  timestampField?: string;
};

export function getAvgDailyAnomalyCount(anomalies: Anomaly[], opts?: Options): number {
  const { timestampField = 'TIMESTAMP_AS_OF' } = opts || {};

  if (!anomalies.length) {
    return 0;
  }

  const dayCounts = countBy(anomalies, ({ original_record }) =>
    Dayjs(original_record?.[timestampField]).startOf('day').toISOString(),
  );

  return mean(Object.values(dayCounts));
}
