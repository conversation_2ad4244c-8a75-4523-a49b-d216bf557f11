import { useState } from 'react';
import Dayjs from 'dayjs';
import Image from 'next/image';
import { Anomaly } from '@/types';
import { Button } from '@/components';
import RecordModal from './RecordModal';

type Props = {
  anomaly: Anomaly;
};

const TIMESTAMP_FIELD = 'TIMESTAMP_AS_OF';

export default function AnomalySubRow(props: Props) {
  const { anomaly } = props;
  const { original_data_value, median, original_record } = anomaly;

  const [recordModalOpen, setRecordModalOpen] = useState(false);

  return (
    <>
      <div className="px-6 pb-6 flex gap-px font-medium text-sm text-gray-900 rounded-b-xl divide-x border-t">
        <div className="w-2/3 p-6">
          <div className="flex w-full h-full relative">
            <Image src="/img/placeholder-anomaly-grid.png" fill alt="Anomaly grid" />
          </div>
        </div>
        <div className="flex flex-col w-1/3 p-0.5 gap-y-0.5 divide-y">
          <div className="flex flex-col bg-white p-8 gap-y-1">
            <span className="text-zinc-500">Anomaly value</span>
            <span className="text-xl">{original_data_value}</span>
          </div>
          <div className="flex flex-col bg-white p-8 gap-y-1">
            <span className="text-zinc-500">Median value</span>
            <span className="text-xl">{median}</span>
          </div>
          <div className="flex flex-col bg-white p-8 gap-y-1">
            <span className="text-zinc-500">Date detected</span>
            <span className="text-xl">
              {Dayjs(original_record?.[TIMESTAMP_FIELD]).format('MMMM D, YYYY h:mm A')}
            </span>
          </div>
          <div className="flex flex-col bg-white p-8 gap-y-1">
            <div className="w-1/4">
              <Button
                type="secondary"
                label="View record"
                onClick={() => {
                  setRecordModalOpen(true);
                }}
              />
            </div>
          </div>
        </div>
      </div>
      {recordModalOpen && (
        <RecordModal anomaly={anomaly} isOpen={recordModalOpen} setIsOpen={setRecordModalOpen} />
      )}
    </>
  );
}
