import { getAvgDailyAnomalyCount } from './utils';

describe('getAvgDailyAnomalyCount', () => {
  test('counts by day and gets average', () => {
    const anomalies = [
      {
        primary_key: 65232,
        feature_name: 'AMT_TOTAL_DOWN',
        original_data_value: 72500,
        percentile: 99.742360042,
        median: 4550,
        comment:
          "this value is 15.9 times higher than median,it's too high compared to similar loans",
        table_name: 'customer_source_one_financial_db.grand_vault.applications_offers',
        original_record: {
          ID_OFFER: '90792848',
          ID_APPLICATION: '90716637',
          ID_APPLICATION_SERIES: '26941',
          ID_APPLICATION_HASH_KEY:
            '60405867573e34c3849477ee616da84bb7f68e54a15d79ff194bfdbb05cbb4b3',
          TIMESTAMP_AS_OF: '2024-02-17 22:55:03.000 +0000',
          TYPE_OFFER: 'RECOMMENDED',
          STATUS_SWAP: null,
          AMT_SALE_PRICE: 214166,
          AMT_FINANCED_ESTIMATED: 149396,
          AMT_FINANCED_CONTRACT: 149396,
          AMT_ESTIMATED_PAYMENT: 0,
          AMT_INSTALLMENT_PAYMENT: 0,
          AMT_CASH_DOWN: 0,
          AMT_TOTAL_DOWN: 72500,
          AMT_SALES_TAX: 7100,
          AMT_TTL: 450,
          AMT_NET_TRADE: 72500,
          AMT_WARRANTY: 0,
          AMT_REBATE: 0,
          AMT_DISCOUNT: 0,
          AMT_ACCIDENTAL_HEALTH_INSURANCE: 0,
          AMT_CREDIT_LIFE_INSURANCE: 0,
          AMT_GAP_INSURANCE: 0,
          AMT_FEES_BACKEND: 0,
          AMT_FEES_FRONTEND: 180,
          AMT_FEES_OTHER: 0,
          AMT_FEES_FINANCING: 180,
          AMT_FEES_ORIGINATION: 0,
          AMT_FEES_ORIGINATION_GOVERNMENT: 0,
          VAL_APR: 0,
          VAL_BUY_RATE: 0,
          VAL_LTV: 84.02,
          VAL_DTI: 0,
          VAL_PTI: 0,
          VAL_PARTICIPATION: 0,
          VAL_DISCOUNT: 0,
          NUM_TERM_MONTHS: 180,
          NUM_RISK_TIER: null,
        },
        data_dictionary_field: {
          model_id: '9637f8a2-9d44-4c1d-8c56-92f8525e23ac',
          model_name: 'APPLICATIONS_OFFERS',
          field_name: 'amt_total_down',
          field_ordinal: 14,
          field_type: 'number',
          field_description: 'The total down payment.',
          field_format: 'currency',
          gen_field_type: 'currency',
          sources: [
            {
              connectorName: 'defilos',
              sourceTableName: 'structure',
              sourceStrategy: 'combine sources',
              mappings: [
                {
                  operation: 'COPY',
                  source_field: 'total_down',
                  target_field: 'amt_total_down',
                },
              ],
            },
          ],
        },
      },
      {
        primary_key: 65232,
        feature_name: 'AMT_NET_TRADE',
        original_data_value: 42500,
        percentile: 99.809124983,
        median: 0,
        comment: 'normal median value for similar loans is 0, this value is too high',
        table_name: 'customer_source_one_financial_db.grand_vault.applications_offers',
        original_record: {
          ID_OFFER: '124332743',
          ID_APPLICATION: '124257849',
          ID_APPLICATION_SERIES: '67127',
          ID_APPLICATION_HASH_KEY:
            '5b9f0ece37ea5150bba24a51c966842530167851227f7cb31112776461b73f86',
          TIMESTAMP_AS_OF: '2024-02-18 23:10:54.000 +0000',
          TYPE_OFFER: 'RECOMMENDED',
          STATUS_SWAP: null,
          AMT_SALE_PRICE: 293940,
          AMT_FINANCED_ESTIMATED: 230915,
          AMT_FINANCED_CONTRACT: 230915,
          AMT_ESTIMATED_PAYMENT: 0,
          AMT_INSTALLMENT_PAYMENT: 0,
          AMT_CASH_DOWN: 30000,
          AMT_TOTAL_DOWN: 72500,
          AMT_SALES_TAX: 8500,
          AMT_TTL: 975,
          AMT_NET_TRADE: 42500,
          AMT_WARRANTY: 0,
          AMT_REBATE: 0,
          AMT_DISCOUNT: 0,
          AMT_ACCIDENTAL_HEALTH_INSURANCE: 0,
          AMT_CREDIT_LIFE_INSURANCE: 0,
          AMT_GAP_INSURANCE: 0,
          AMT_FEES_BACKEND: 0,
          AMT_FEES_FRONTEND: 0,
          AMT_FEES_OTHER: 0,
          AMT_FEES_FINANCING: 0,
          AMT_FEES_ORIGINATION: 0,
          AMT_FEES_ORIGINATION_GOVERNMENT: 0,
          VAL_APR: 0,
          VAL_BUY_RATE: 0,
          VAL_LTV: 113.48,
          VAL_DTI: 0,
          VAL_PTI: 0,
          VAL_PARTICIPATION: 0,
          VAL_DISCOUNT: 0,
          NUM_TERM_MONTHS: 240,
          NUM_RISK_TIER: null,
        },
        data_dictionary_field: {
          model_id: '9637f8a2-9d44-4c1d-8c56-92f8525e23ac',
          model_name: 'APPLICATIONS_OFFERS',
          field_name: 'amt_net_trade',
          field_ordinal: 17,
          field_type: 'number',
          field_description: 'The net trade-in amount.',
          field_format: 'currency',
          gen_field_type: 'currency',
          sources: [
            {
              connectorName: 'defilos',
              sourceTableName: 'structure',
              sourceStrategy: 'combine sources',
              mappings: [
                {
                  operation: 'COPY',
                  source_field: 'ttl_estimate',
                  target_field: 'amt_net_trade',
                },
              ],
            },
          ],
        },
      },
      {
        primary_key: 65232,
        feature_name: 'AMT_FINANCED_ESTIMATED',
        original_data_value: 230915,
        percentile: 99.934722397,
        median: 36191.08,
        comment:
          "this value is 6.4 times higher than median,it's too high compared to similar loans",
        table_name: 'customer_source_one_financial_db.grand_vault.applications_offers',
        original_record: {
          ID_OFFER: '124332743',
          ID_APPLICATION: '124257849',
          ID_APPLICATION_SERIES: '67127',
          ID_APPLICATION_HASH_KEY:
            '5b9f0ece37ea5150bba24a51c966842530167851227f7cb31112776461b73f86',
          TIMESTAMP_AS_OF: '2024-02-18 23:10:54.000 +0000',
          TYPE_OFFER: 'RECOMMENDED',
          STATUS_SWAP: null,
          AMT_SALE_PRICE: 293940,
          AMT_FINANCED_ESTIMATED: 230915,
          AMT_FINANCED_CONTRACT: 230915,
          AMT_ESTIMATED_PAYMENT: 0,
          AMT_INSTALLMENT_PAYMENT: 0,
          AMT_CASH_DOWN: 30000,
          AMT_TOTAL_DOWN: 72500,
          AMT_SALES_TAX: 8500,
          AMT_TTL: 975,
          AMT_NET_TRADE: 42500,
          AMT_WARRANTY: 0,
          AMT_REBATE: 0,
          AMT_DISCOUNT: 0,
          AMT_ACCIDENTAL_HEALTH_INSURANCE: 0,
          AMT_CREDIT_LIFE_INSURANCE: 0,
          AMT_GAP_INSURANCE: 0,
          AMT_FEES_BACKEND: 0,
          AMT_FEES_FRONTEND: 0,
          AMT_FEES_OTHER: 0,
          AMT_FEES_FINANCING: 0,
          AMT_FEES_ORIGINATION: 0,
          AMT_FEES_ORIGINATION_GOVERNMENT: 0,
          VAL_APR: 0,
          VAL_BUY_RATE: 0,
          VAL_LTV: 113.48,
          VAL_DTI: 0,
          VAL_PTI: 0,
          VAL_PARTICIPATION: 0,
          VAL_DISCOUNT: 0,
          NUM_TERM_MONTHS: 240,
          NUM_RISK_TIER: null,
        },
        data_dictionary_field: {
          model_id: '9637f8a2-9d44-4c1d-8c56-92f8525e23ac',
          model_name: 'APPLICATIONS_OFFERS',
          field_name: 'amt_financed_estimated',
          field_ordinal: 9,
          field_type: 'number',
          field_description: 'The estimated financed amount.',
          field_format: 'currency',
          gen_field_type: 'currency',
          sources: [],
        },
      },
    ];

    expect(getAvgDailyAnomalyCount(anomalies)).toBe(1.5);
  });

  test('no anomalies', () => {
    const anomalies = [];

    expect(getAvgDailyAnomalyCount(anomalies)).toBe(0);
  });
});
