import { map } from 'lodash';
import { Modal, FieldTypeIcon } from '@/components';
import { Anomaly, BaseField, SetStateFunction } from '@/types';

type Props = {
  anomaly: Anomaly;
  isOpen: boolean;
  setIsOpen: SetStateFunction<boolean>;
};

export default function RecordModal(props: Props) {
  const { anomaly, isOpen, setIsOpen } = props;
  const { feature_name, original_record = {}, data_dictionary_field = {} } = anomaly;
  const { gen_field_type } = data_dictionary_field as BaseField;

  return (
    <Modal
      title={
        <div className="flex gap-2 items-center text-lg">
          <FieldTypeIcon type={gen_field_type} />
          <h2>{feature_name} anomaly</h2>
        </div>
      }
      open={isOpen}
      setIsOpen={setIsOpen}
      titleUnderline={true}
      className="w-[1054px] h-[600px] overflow-scroll"
    >
      <table className="table">
        <thead className="table-head">
          <th className="table-head-cell">Field</th>
          <th className="table-head-cell">Value</th>
        </thead>
        <tbody>
          {map(original_record, (val, key) => (
            <tr key={`${val}${key}`}>
              <td className="table-cell">{key}</td>
              <td className="table-cell">{val ?? 'null'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </Modal>
  );
}
