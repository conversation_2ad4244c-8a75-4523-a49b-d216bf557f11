import { LinkAnimation, FieldBox, DataTransformBox } from '@/components';
import { MappingSourceField, ContractMappingType } from '@/types';
import { imgSrc } from 'lib/source';

type Props = {
  sourceField: MappingSourceField;
  fieldName: string;
  mappingType: ContractMappingType;
  onSql?: string;
};

export default function SingleSourceMapping(props: Props) {
  const { sourceField, fieldName, mappingType, onSql } = props;
  const {
    text_source_field,
    text_source_model_dbt_name,
    text_source_model_name,
    text_source_model_origin,
  } = sourceField;

  return (
    <div className="flex flex-row w-full items-center">
      <FieldBox
        field={text_source_field}
        imgSrc={imgSrc.getOrDefault(text_source_model_origin).imgPath}
        imgAlt="Connector Logo"
        subField={text_source_model_name}
        subFieldTitle={text_source_model_dbt_name}
        truncate
        width="w-96"
      />
      <div className="px-4">
        <LinkAnimation width="w-28" />
      </div>
      {mappingType === 'transform' && (
        <>
          <div className="self-stretch">
            <DataTransformBox onSql={onSql} fieldName={fieldName} />
          </div>
          <div className="px-4">
            <LinkAnimation width="w-28" />
          </div>
        </>
      )}
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
      />
    </div>
  );
}
