import { MappingSourceField, ContractMappingType } from '@/types';
import SingleSourceCopyMapping from './SingleSourceCopyMapping';
import SingleSourceTransformMapping from './SingleSourceTransformMapping';

type Props = {
  sourceField: MappingSourceField;
  fieldName: string;
  mappingType: ContractMappingType;
  packageName: string;
  onSql?: string;
};

export default function SingleSourceMapping(props: Props) {
  const { sourceField, fieldName, mappingType, packageName, onSql } = props;

  if (mappingType === 'transform') {
    return (
      <SingleSourceTransformMapping
        sourceField={sourceField}
        fieldName={fieldName}
        packageName={packageName}
        onSql={onSql}
      />
    );
  }

  return (
    <SingleSourceCopyMapping
      sourceField={sourceField}
      fieldName={fieldName}
      packageName={packageName}
    />
  );
}
