import { LinkAnimation, FieldBox, DataTransformBox } from '@/components';
import { MappingSourceField, ContractMappingType } from '@/types';
import { imgSrc } from 'lib/source';
import SourceFieldBox from './SourceFieldBox';

type Props = {
  sourceField: MappingSourceField;
  fieldName: string;
  mappingType: ContractMappingType;
  onSql?: string;
};

export default function SingleSourceMapping(props: Props) {
  const { sourceField, fieldName, mappingType, onSql } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <SourceFieldBox sourceField={sourceField} />
      <div className="px-4">
        <LinkAnimation width="w-28" />
      </div>
      {mappingType === 'transform' && (
        <>
          <div className="self-stretch">
            <DataTransformBox onSql={onSql} fieldName={fieldName} />
          </div>
          <div className="px-4">
            <LinkAnimation width="w-28" />
          </div>
        </>
      )}
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
      />
    </div>
  );
}
