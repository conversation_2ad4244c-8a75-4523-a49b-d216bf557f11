import { FieldBox } from '@/components';
import { Variable } from '@/components/icons';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';

type Props = {
  sourceField: MappingSourceField;
};

export default function SourceFieldBox(props: Props) {
  const { sourceField } = props;

  const {
    text_source_field,
    text_source_model_dbt_name,
    text_source_model_name,
    text_source_model_origin,
    type_field_source,
  } = sourceField;

  if (type_field_source === 'static') {
    const containerClass =
      'flex flex-row items-center w-96 h-14 border-2 border-solid border-gray-100 rounded-lg overflow-clip bg-white';

    return (
      <div className={containerClass}>
        <div className="p-2 w-10 bg-white shrink-0 flex items-center">
          <Variable className="stroke-black" />
        </div>
        <div
          className={`flex flex-row h-full w-full min-w-0 overflow-hidden items-center p-3 gap-y-2 bg-zinc-100`}
        >
          <div className="flex flex-col w-full min-w-0">
            <span className="font-semibold" title="Static value">
              "{text_source_field}"
            </span>
          </div>
        </div>
      </div>
    );
  }

  const { imgPath, dropdownStyle } = imgSrc.getOrDefault(text_source_model_origin);

  return (
    <FieldBox
      field={text_source_field}
      imgSrc={imgPath}
      imgAlt="Connector Logo"
      subField={text_source_model_name}
      subFieldTitle={text_source_model_dbt_name}
      imgStyle={{ style: dropdownStyle?.style }}
      truncate
      width="w-96"
    />
  );
}
