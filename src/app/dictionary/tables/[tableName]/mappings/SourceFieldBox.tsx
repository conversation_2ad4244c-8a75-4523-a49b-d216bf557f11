import Image from 'next/image';
import { FieldBox } from '@/components';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';

type Props = {
  sourceField: MappingSourceField;
  packageName: string;
  width?: string;
};

export default function SourceFieldBox(props: Props) {
  const { sourceField, packageName, width } = props;

  const {
    text_source_field,
    text_source_model_warehouse_name,
    text_source_model_name,
    type_field_source,
  } = sourceField;

  const { imgPath, alt, dropdownStyle } = imgSrc.getOrDefault(packageName);

  if (type_field_source === 'static') {
    const containerClass =
      'flex flex-row items-center w-72 h-14 border-2 border-solid border-gray-100 rounded-lg overflow-clip bg-white';

    return (
      <div className={containerClass}>
        <div className="p-2 w-10 bg-white shrink-0 flex items-center" title={packageName}>
          <Image src={imgPath} alt={alt} {...dropdownStyle} />
        </div>
        <div
          className={`flex flex-row h-full w-full min-w-0 overflow-hidden items-center p-3 gap-y-2 bg-zinc-100`}
        >
          <div className="flex flex-col w-full min-w-0">
            <span className="font-semibold" title="Static value">
              "{text_source_field}"
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div title={packageName}>
      <FieldBox
        field={text_source_field}
        imgSrc={imgPath}
        imgAlt="Connector Logo"
        subField={text_source_model_name}
        subFieldTitle={text_source_model_warehouse_name}
        imgStyle={{ style: dropdownStyle?.style }}
        truncate
        width={width}
      />
    </div>
  );
}
