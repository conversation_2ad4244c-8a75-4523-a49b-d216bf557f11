import { LinkAnimation, FieldBox, DataTransformBox } from '@/components';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';
import SourceFieldBox from './SourceFieldBox';

type Props = {
  sourceField: MappingSourceField;
  fieldName: string;
  packageName: string;
  onSql?: string;
};

export default function SingleSourceTransformMapping(props: Props) {
  const { sourceField, fieldName, packageName, onSql } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <SourceFieldBox sourceField={sourceField} packageName={packageName} />
      <div className="px-2">
        <LinkAnimation width="w-10" />
      </div>
      <div className="self-stretch">
        <DataTransformBox onSql={onSql} fieldName={fieldName} packageName={packageName} />
      </div>
      <div className="px-2">
        <LinkAnimation width="w-10" />
      </div>
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
        truncate
      />
    </div>
  );
}
