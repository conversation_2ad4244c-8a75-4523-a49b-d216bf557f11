import { DisconnectedIcon, FieldBox } from '@/components';
import { imgSrc } from 'lib/source';

type Props = {
  fieldName: string;
  packageName?: string;
};

export default function UnmappedField(props: Props) {
  const { fieldName, packageName } = props;

  const gestaltImgConfig = imgSrc.getOrDefault('gestalt');
  const sourceImgConfig = packageName ? imgSrc.getOrDefault(packageName) : null;

  return (
    <div className="flex flex-row w-full items-center">
      <FieldBox
        field="Unlinked"
        imgAlt="Unlinked"
        imgSrc={sourceImgConfig?.imgPath}
        imgStyle={sourceImgConfig?.dropdownStyle && { ...sourceImgConfig.dropdownStyle }}
      />
      <DisconnectedIcon width="w-32" />
      <FieldBox imgSrc={gestaltImgConfig.imgPath} imgAlt="Gestalt Logo Cube" field={fieldName} />
    </div>
  );
}
