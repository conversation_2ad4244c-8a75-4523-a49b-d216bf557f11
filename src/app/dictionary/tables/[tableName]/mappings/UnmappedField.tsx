import { DisconnectedIcon, FieldBox } from '@/components';
import { imgSrc } from 'lib/source';

type Props = {
  fieldName: string;
};

export default function UnmappedField(props: Props) {
  const { fieldName } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <FieldBox field="Unlinked" imgAlt="Unlinked" />
      <DisconnectedIcon />
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
      />
    </div>
  );
}
