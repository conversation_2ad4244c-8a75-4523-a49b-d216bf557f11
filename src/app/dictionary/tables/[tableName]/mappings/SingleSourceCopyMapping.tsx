import { LinkAnimation, FieldBox } from '@/components';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';
import SourceFieldBox from './SourceFieldBox';

type Props = {
  sourceField: MappingSourceField;
  fieldName: string;
  packageName: string;
};

export default function SingleSourceCopyMapping(props: Props) {
  const { sourceField, fieldName, packageName } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <SourceFieldBox sourceField={sourceField} packageName={packageName} />
      <div className="px-2">
        <LinkAnimation width="w-28" />
      </div>
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
        truncate
      />
    </div>
  );
}
