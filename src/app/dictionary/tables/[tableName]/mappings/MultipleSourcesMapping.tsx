import { LinkAnimation, FieldBox, DataTransformBox } from '@/components';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';
import SourceFieldBox from './SourceFieldBox';

type Props = {
  sourceFields: MappingSourceField[];
  fieldName: string;
  onSql?: string;
  packageName: string;
};

export default function MultipleSourcesMapping(props: Props) {
  const { sourceFields, fieldName, packageName, onSql } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <div className="flex flex-col gap-y-2">
        {sourceFields.map((source) => {
          const { id_source_model, text_source_field } = source;

          return (
            <div
              className="flex flex-row items-center w-full"
              key={`${id_source_model}${text_source_field}`}
            >
              <SourceFieldBox sourceField={source} packageName={packageName} />
              <div className="px-2">
                <LinkAnimation width="w-10" />
              </div>
            </div>
          );
        })}
      </div>
      <div className="self-stretch">
        <DataTransformBox onSql={onSql} fieldName={fieldName} packageName={packageName} />
      </div>
      <div className="px-2">
        <LinkAnimation width="w-10" />
      </div>
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
        truncate
      />
    </div>
  );
}
