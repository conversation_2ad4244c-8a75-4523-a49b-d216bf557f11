import { LinkAnimation, FieldBox, DataTransformBox } from '@/components';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';
import SourceFieldBox from './SourceFieldBox';

type Props = {
  sourceFields: MappingSourceField[];
  fieldName: string;
  onSql?: string;
};

export default function MultipleSourcesMapping(props: Props) {
  const { sourceFields, fieldName, onSql } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <div className="flex flex-col gap-y-2">
        {sourceFields.map((source) => {
          const { id_source_model } = source;

          return (
            <div className="flex flex-row items-center w-full" key={id_source_model}>
              <SourceFieldBox sourceField={source} />
              <div className="px-6">
                <LinkAnimation width="w-28" />
              </div>
            </div>
          );
        })}
      </div>
      <div className="self-stretch">
        <DataTransformBox onSql={onSql} fieldName={fieldName} />
      </div>
      <div className="px-6">
        <LinkAnimation width="w-28" />
      </div>
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
      />
    </div>
  );
}
