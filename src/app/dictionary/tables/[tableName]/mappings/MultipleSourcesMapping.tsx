import { LinkAnimation, FieldBox, DataTransformBox } from '@/components';
import { MappingSourceField } from '@/types';
import { imgSrc } from 'lib/source';

type Props = {
  sourceFields: MappingSourceField[];
  fieldName: string;
  onSql?: string;
};

export default function MultipleSourcesMapping(props: Props) {
  const { sourceFields, fieldName, onSql } = props;

  return (
    <div className="flex flex-row w-full items-center">
      <div className="flex flex-col gap-y-2">
        {sourceFields.map((source) => {
          const {
            id_source_model,
            text_source_model_name,
            text_source_model_dbt_name,
            text_source_model_origin,
            text_source_field,
          } = source;

          const imgConfig = imgSrc.getOrDefault(text_source_model_origin);

          return (
            <div className="flex flex-row items-center w-full" key={id_source_model}>
              <FieldBox
                imgSrc={imgConfig.imgPath}
                imgAlt="Connector Logo"
                field={text_source_field}
                subField={text_source_model_name}
                subFieldTitle={text_source_model_dbt_name}
                imgStyle={{ style: imgConfig.dropdownStyle?.style }}
              />
              <div className="px-6">
                <LinkAnimation width="w-28" />
              </div>
            </div>
          );
        })}
      </div>
      <div className="self-stretch">
        <DataTransformBox onSql={onSql} fieldName={fieldName} />
      </div>
      <div className="px-6">
        <LinkAnimation width="w-28" />
      </div>
      <FieldBox
        imgSrc={imgSrc.getOrDefault('gestalt').imgPath}
        imgAlt="Gestalt Logo Cube"
        field={fieldName}
      />
    </div>
  );
}
