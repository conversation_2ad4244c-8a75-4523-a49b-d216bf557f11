'use client';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { isNil } from 'lodash';
import {
  PageContent,
  Overview,
  PageTitle,
  GestaltTableIcon,
  InputSelect,
  Button,
  InputText,
  DataFilter,
  Loading,
} from '@/components';
import { Columns02, LinkBroken03, Rows02, MessageTextSquare02 } from '@/components/icons';
import ColumnRow from './ColumnRow';
import { useCustomer, useFeatureFlags } from '@/hooks';
import { imgSrc } from '../../../../../lib/source';
import {
  FilterAccessor,
  FilterValue,
  Source,
  CustomerRequest,
  TargetModelDictionaryField,
} from '@/types';
import { filterDataRow, getFilterOptions } from '@/utils/data';
import AnomalyOverview from './AnomalyOverview';
import CustomFieldsModal from './CustomFieldsModal';
import FieldRequestList from './FieldRequestList';
import { api } from '@/helpers/web';
import { useDictionaryStore } from '@/stores';

type Props = {
  params: Record<string, any>;
};

// accessors for filtering columns based on filter type
const filterFields: Record<string, FilterAccessor> = {
  type: (col: TargetModelDictionaryField, val: string) =>
    col.gen_field_type === val || col.field_type.toLowerCase() === val,
  mapping: (col: TargetModelDictionaryField, val: string) =>
    val === 'mapped' ? !!col.mapping : !col.mapping,
};

const filterOptions = getFilterOptions('dictionaryColumn');

export default function TableInfo(props: Props) {
  const { params } = props;
  const { tableName } = params;
  const { customerKey, customerId, customerFivetranDestId } = useCustomer();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { featureFlags } = useFeatureFlags();
  const { targetModels: models, targetSearchTerm, setTargetSearchTerm } = useDictionaryStore();

  // Clear search term only when coming from "View model" button
  useEffect(() => {
    const clearSearch = searchParams?.get('clearSearch');

    if (clearSearch === 'true') {
      setTargetSearchTerm('');
      // Clean up the URL by removing the query parameter
      const url = new URL(window.location.href);
      url.searchParams.delete('clearSearch');
      router.replace(url.pathname, { scroll: false });
    }
  }, [searchParams, setTargetSearchTerm, router]);

  const currentModel = models?.find(
    (model) => model.name.toLowerCase() === tableName.toLowerCase(),
  );
  const currentModelIdx = models?.findIndex(
    (model) => model.name.toLowerCase() === tableName.toLowerCase(),
  );

  const [anomalyCount, setAnomalyCount] = useState<number>();
  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    type: { label: 'All', value: 'all', filter: false },
    mapping: { label: 'All', value: 'all-mapped', filter: false },
  });
  const [customFieldsModalOpen, setCustomFieldsModalOpen] = useState<boolean>(false);
  const [source, setSource] = useState<Source>({
    loading: true,
    error: null,
    connectors: [],
  });
  const [customFieldRequests, setCustomFieldRequests] = useState<CustomerRequest[]>();

  const anomaliesEnabled = featureFlags['can-view-anomalies']?.enabled;

  // get anomaly summary
  useEffect(() => {
    if (!customerKey || !anomaliesEnabled) {
      return;
    }

    const getAnomalySummary = async () => {
      const { count } = await api.get(`/api/dataDictionary/${tableName}/getAnomalySummary`, {
        customerKey,
      });

      setAnomalyCount(count);
    };

    getAnomalySummary();
  });

  // get list of sources
  useEffect(() => {
    if (!customerFivetranDestId || !customerId) {
      return;
    }

    const getSource = async () => {
      try {
        const { data } = await api.get('/api/connectors/19', {
          fivetranDestId: customerFivetranDestId,
          customerId,
        });

        setSource({ loading: false, connectors: data || [], error: null });
      } catch (error: any) {
        setSource({ loading: false, connectors: [], error: error.message });
      }
    };

    getSource();
  }, [customerFivetranDestId, customerId]);

  // separate callback to get custom field requests so that we can trigger it when a new request is added
  const getCustomerReqs = useCallback(async () => {
    if (!currentModel || !customerId) {
      return;
    }

    const { customFieldRequests } = await api.get(
      `/api/dataDictionary/${tableName}/getCustomFieldRequests`,
      {
        customerId,
        modelId: currentModel.id,
      },
    );

    setCustomFieldRequests(customFieldRequests);
  }, [tableName, customerId, currentModel]);

  useEffect(() => {
    getCustomerReqs();
  }, [getCustomerReqs]);

  const modelOptions = useMemo(() => {
    if (!models) {
      return [];
    }

    return models.map((model) => ({
      label: model.name.toLowerCase(),
      value: model.name.toLowerCase(),
      image: imgSrc.gestalt.imgPath,
      disabled: false,
    }));
  }, [models]);

  const filteredCols = useMemo(() => {
    if (!currentModel) {
      return [];
    }

    return currentModel.dictionary.filter((field) =>
      filterDataRow(field, {
        nameField: 'field_name',
        searchTerm: targetSearchTerm,
        filterFields,
        filterConfig,
      }),
    );
  }, [currentModel, targetSearchTerm, filterConfig]);

  if (!models || !currentModel) {
    return (
      <PageContent>
        <PageTitle
          title={tableName}
          titleIcon={<GestaltTableIcon size="large" />}
          subpage
          breadcrumbs={[
            { label: 'Dictionary', url: '/dictionary' },
            {
              label: tableName,
              url: `/dictionary/tables/${tableName}`,
            },
          ]}
        />
        <Loading />
      </PageContent>
    );
  }

  const { type, description, fieldCount, rowCount, mappingCount } = currentModel;

  const overviewContents = [
    {
      header: 'Total fields',
      content: (
        <div className="flex flex-row items-center">
          <Columns02 className="w-6 h-6 stroke-gray-500 mr-2" />
          <h3 className="text-3xl font-semibold capitalize">{fieldCount}</h3>
        </div>
      ),
    },
    {
      header: 'Unmapped fields',
      content: (
        <div className="flex flex-row items-center">
          <LinkBroken03 className="w-6 h-6 stroke-gray-500 mr-2" />
          <h3 className="text-3xl font-semibold capitalize">
            {type === 'core' ? fieldCount! - mappingCount! : 'N/A'}
          </h3>
        </div>
      ),
    },
    {
      header: 'Total rows',
      content: (
        <div className="flex flex-row items-center">
          <Rows02 className="w-6 h-6 stroke-gray-500 mr-2" />
          <h3 className="text-3xl font-semibold capitalize">
            {!isNil(rowCount) ? rowCount.toLocaleString() : 'N/A'}
          </h3>
        </div>
      ),
    },
  ];

  const descriptionContents = [
    {
      content: (
        <div className="flex flex-row items-center">
          <div className="flex flex-col">
            <MessageTextSquare02 className="w-6 h-6 stroke-gray-500 mr-4" />
          </div>
          <span>{description}</span>
        </div>
      ),
    },
  ];

  return (
    <>
      <PageContent>
        <PageTitle
          title={tableName}
          titleIcon={<GestaltTableIcon size="large" />}
          subpage
          breadcrumbs={[
            { label: 'Dictionary', url: '/dictionary' },
            {
              label: tableName,
              url: `/dictionary/tables/${tableName}`,
            },
          ]}
        >
          <div className="flex gap-3 sm:mt-0 sm:flex-row sm:items-center">
            <InputSelect
              className="w-80"
              onChange={(val) => router.push(`/dictionary/tables/${val}`)}
              options={modelOptions}
              defaultValue={tableName}
              images
              showSelected={false}
            />
            <Button
              iconOnly
              type="secondary"
              icon="ArrowLeft"
              disabled={currentModelIdx === 0}
              href={`/dictionary/tables/${models[currentModelIdx! - 1]?.name?.toLowerCase()}`}
            />
            <Button
              iconOnly
              type="secondary"
              icon="ArrowRight"
              disabled={currentModelIdx === models.length - 1}
              href={`/dictionary/tables/${models[currentModelIdx! + 1]?.name?.toLowerCase()}`}
            />
          </div>
        </PageTitle>
        <>
          <div className="flex justify-between w-full gap-x-8">
            <div className="mb-10 w-1/3">
              <Overview title="Description" overviewContents={descriptionContents} />
            </div>
            {anomaliesEnabled && (
              <div className="w-1/2">
                <AnomalyOverview anomalyCount={anomalyCount} />
              </div>
            )}
          </div>
          <div className="mb-10">
            <Overview overviewContents={overviewContents} />
          </div>
          {!!customFieldRequests?.length && (
            <div className="mb-10">
              <FieldRequestList
                customFieldRequests={customFieldRequests}
                refetch={getCustomerReqs}
              />
            </div>
          )}
          <div className=" flex justify-between items-center mb-4">
            <h1 className="text-2xl font-medium text-zinc-900">Fields</h1>
            <div className="flex gap-3">
              <InputText
                icon="SearchMd"
                placeholder="Search fields"
                value={targetSearchTerm}
                onChange={(e) => setTargetSearchTerm(e.target.value)}
              />
              <DataFilter
                filterConfig={filterConfig}
                onFilterConfigChange={setFilterConfig}
                filterOptions={filterOptions}
              />
              {type === 'consumption' && (
                <Button
                  label="Add custom field"
                  onClick={() => {
                    setCustomFieldsModalOpen(true);
                  }}
                />
              )}
            </div>
          </div>
          <div>
            {filteredCols.length ? (
              filteredCols.map((col, idx) => (
                <ColumnRow column={col} key={`${col.field_name}${idx}`} />
              ))
            ) : (
              <div className="w-full flex justify-center items-center py-40">
                <span className="text-zinc-400">No fields to display.</span>
              </div>
            )}
          </div>
        </>
      </PageContent>
      <CustomFieldsModal
        isOpen={customFieldsModalOpen}
        onOpenChange={setCustomFieldsModalOpen}
        source={source}
        onSubmit={getCustomerReqs}
        modelId={currentModel.id}
      />
    </>
  );
}
