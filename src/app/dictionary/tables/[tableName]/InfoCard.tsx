import { ReactNode } from 'react';
import { Card } from '@/components';

type Props = {
  title: string;
  infoIcon?: ReactNode;
  info: string;
  paddingX?: string;
  paddingY?: string;
};

export default function InfoCard(props: Props) {
  const { title, infoIcon, info, paddingX, paddingY } = props;

  return (
    <Card paddingX={paddingX} paddingY={paddingY}>
      <div className="flex justify-start w-full">
        <div className="flex flex-col">
          <span className="text-gray-500 mb-2">{title}</span>
          <div className="flex flex-row items-center w-full h-full">
            {infoIcon && <div className="mr-2">{infoIcon}</div>}
            <span className="font-medium text-lg capitalize">{info}</span>
          </div>
        </div>
      </div>
    </Card>
  );
}
