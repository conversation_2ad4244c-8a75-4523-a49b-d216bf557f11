import Image from 'next/image';
import { isNil } from 'lodash';
import { <PERSON>, Button, Loading } from '@/components';
import { useRouter, usePathname } from 'next/navigation';

type Props = {
  anomalyCount?: number;
};

export default function AnomalyOverview(props: Props) {
  const { anomalyCount } = props;

  const router = useRouter();
  const pathname = usePathname();

  if (isNil(anomalyCount)) {
    return <Loading />;
  }

  return (
    <div className="w-7/12 w-full flex flex-col space-y-3">
      <div className="flex justify-between">
        <h2 className="text-2xl font-medium text-zinc-900">Data anomalies</h2>
        <Button
          label="View details"
          type="secondary"
          onClick={() => {
            router.push(`${pathname}/data_anomalies`);
          }}
        />
      </div>
      <div className="w-full p-12 flex flex-col gap-y-6 justify-center items-center rounded-xl bg-white">
        <Image
          src="/img/placeholder-anomaly-metrics.png"
          width={500}
          height={500}
          alt="Anomaly metrics"
        />
        <Banner
          type="error"
          message={`${anomalyCount} data anomalies require your review`}
          detailHref={`${pathname}/data_anomalies`}
        />
      </div>
    </div>
  );
}
