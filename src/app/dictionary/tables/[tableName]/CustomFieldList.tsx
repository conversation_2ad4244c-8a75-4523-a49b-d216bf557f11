import { CustomField, SetStateFunction } from '@/types';
import { Loading, DataLabel } from '@/components';
import { imgSrc } from 'lib/source';
import { filterData } from '@/utils/data';

type Props = {
  customFields?: CustomField[];
  searchTerm: string;
  selectedField?: CustomField | null;
  onSelect: SetStateFunction<CustomField>;
};

export default function CustomFieldList(props: Props) {
  const { customFields, searchTerm, selectedField, onSelect } = props;

  if (!customFields) {
    return <Loading />;
  }

  if (!customFields.length) {
    return (
      <div className="flex flex-col justify-center items-center py-40">
        <span className="text-zinc-400">No custom fields to display.</span>
      </div>
    );
  }

  const filteredFields = filterData<CustomField>(customFields, {
    searchTerm,
    nameField: 'text_name',
  });

  return (
    <div className="flex flex-col gap-y-2">
      {filteredFields.map((customField, idx) => {
        const selected =
          selectedField && customField.id_custom_field === selectedField.id_custom_field;

        return (
          <div
            className={`hover:cursor-pointer ${selected && 'border-2 border-emerald-500'}`}
            key={`${customField.id_custom_field}${idx}`}
            onClick={() => {
              onSelect(customField);
            }}
          >
            <DataLabel
              label={customField.text_name}
              description={`Entity type: ${customField.type_entity}`}
              image={imgSrc.gestalt.imgPath}
            />
          </div>
        );
      })}
    </div>
  );
}
