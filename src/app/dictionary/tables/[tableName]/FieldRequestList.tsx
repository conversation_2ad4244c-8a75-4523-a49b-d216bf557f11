import { ContentHeader, CardTableRow, Badge, FieldTypeIcon } from '@/components';
import { CustomerRequest, Option } from '@/types';
import { api } from '@/helpers/web';

type Props = {
  customFieldRequests: CustomerRequest[];
  refetch: () => Promise<void>;
};

const WORKING_MESSAGE = 'Our team is working on implementing your requested custom field.';

export default function FieldRequestList(props: Props) {
  const { customFieldRequests, refetch } = props;

  const handleDeleteRequest = async (requestId) => {
    const { success } = await api.post(`/api/customer/deleteCustomerRequest`, { requestId });

    if (success) {
      await refetch();
    }
  };

  return (
    <div className="flex flex-col gap-y-4">
      <ContentHeader>Pending custom fields</ContentHeader>
      <div className="flex flex-col gap-y-2">
        {customFieldRequests.map((request) => {
          const { id, data } = request;
          const { text_name } = data;

          const leftIcon = <FieldTypeIcon type="text" />;
          const statusContent = <Badge type="warning" label="Pending" />;

          const editOptions: Option[] = [
            {
              label: 'Delete',
              kind: 'delete',
              value: request,
              onChange: async (opt) => {
                await handleDeleteRequest(opt.value.id);
              },
            },
          ];

          return (
            <CardTableRow
              leftIcon={leftIcon}
              leftContent={text_name}
              leftSubContent={WORKING_MESSAGE}
              statusContent={statusContent}
              styleOpts={{ padding: 'none' }}
              key={id}
              options={editOptions}
            />
          );
        })}
      </div>
    </div>
  );
}
