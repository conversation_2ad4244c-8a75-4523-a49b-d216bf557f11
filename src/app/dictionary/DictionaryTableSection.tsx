import { upperFirst } from 'lodash';
import { Header } from '@/components';
import DictionaryTableRow from './DictionaryTableRow';
import { ModelType, TargetModel } from '@/types';

type Props = {
  modelType: ModelType;
  models: TargetModel[];
};

export default function DictionaryTableSection(props: Props) {
  const { modelType, models } = props;

  return (
    <div className="flex flex-col gap-y-4">
      <div>
        <Header kind="secondary">{upperFirst(modelType)} models</Header>
      </div>
      <div className="p-4 bg-white rounded-xl">
        <table className="w-full">
          <tbody className="divide-y divide-gray-100">
            {models.map((model) => (
              <DictionaryTableRow model={model} key={model.id} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
