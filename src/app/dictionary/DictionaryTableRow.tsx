import { useRouter } from 'next/navigation';
import {
  GestaltTableIcon,
  Link03,
  Columns02,
  Rows02,
  Link,
  MessageTextSquare02,
  <PERSON>ton,
} from '@/components';
import { TargetModel } from '@/types';
import { useDictionaryStore } from '@/stores';

type Props = {
  model: TargetModel;
};

export default function DictionaryTableRow(props: Props) {
  const { model } = props;
  const { name, mappingCount, fieldCount, rowCount, description, dictionary } = model;
  const { targetSearchTerm, setTargetSearchTerm } = useDictionaryStore();

  const router = useRouter();

  let mapMessage = '0 mappings';

  if (mappingCount) {
    if (mappingCount === 1) {
      mapMessage = '1 mapping';
    } else {
      mapMessage = `${mappingCount} mappings`;
    }
  }

  const modelHref = `dictionary/tables/${name.toLowerCase()}`;

  return (
    <tr className="flex w-full items-center bg-white justify-between">
      <td className="px-6 py-3 font-medium text-sm text-gray-900">
        <div className="gap-x-2 text-center sm:text-left flex flex-row items-center mb-2">
          <GestaltTableIcon />
          <span className="font-semibold text-lg">{name.toLowerCase()}</span>
        </div>
        <div className="gap-x-2 flex flex-row items-center text-gray-500 stroke-gray-500">
          <Link03 className="w-4 h-4 stroke-inherit" />
          <span>{mapMessage}</span>
          <Columns02 className="w-4 h-4 stroke-inherit" />
          <span>{fieldCount?.toLocaleString()} fields</span>
          <Rows02 className="w-4 h-4 stroke-inherit" />
          <span>{rowCount?.toLocaleString()} rows</span>
          <MessageTextSquare02 className="w-4 h-4 stroke-inherit" />
          <span className="w-96 line-clamp-1" title={description}>
            {description}
          </span>
        </div>
      </td>
      <td className="px-6 py-3 text-right flex gap-x-4 items-center justify-end">
        {!!targetSearchTerm.length && (
          <Link href={modelHref}>
            <span className="text-sm">View {dictionary.length} matching fields</span>
          </Link>
        )}
        <Button
          type="secondary"
          label="View model"
          onClick={() => {
            setTargetSearchTerm('');

            router.push(modelHref);
          }}
        />
      </td>
    </tr>
  );
}
