import {
  GestaltTableIcon,
  Link03,
  Columns02,
  Rows02,
  Link,
  MessageTextSquare02,
} from '@/components';
import { Model } from '@/types';

type Props = {
  model: Model;
};

export default function DictionaryTableRow(props: Props) {
  const { name, mappingCount, fieldCount, rowCount, description } = props.model;

  let mapMessage = '0 mappings';

  if (mappingCount) {
    if (mappingCount === 1) {
      mapMessage = '1 mapping';
    } else {
      mapMessage = `${mappingCount} mappings`;
    }
  }

  return (
    <tr className="bg-white">
      <td className="px-6 py-3 font-medium text-sm text-gray-900">
        <div className="gap-x-2 text-center sm:text-left flex flex-row items-center mb-2">
          <GestaltTableIcon />
          <span className="font-semibold text-lg">{name.toLowerCase()}</span>
        </div>
        <div className="gap-x-2 flex flex-row items-center text-gray-500 stroke-gray-500">
          <Link03 className="w-4 h-4 stroke-inherit" />
          <span>{mapMessage}</span>
          <Columns02 className="w-4 h-4 stroke-inherit" />
          <span>{fieldCount?.toLocaleString()} fields</span>
          <Rows02 className="w-4 h-4 stroke-inherit" />
          <span>{rowCount?.toLocaleString()} rows</span>
          <MessageTextSquare02 className="w-4 h-4 stroke-inherit" />
          <span className="w-96 line-clamp-1" title={description}>
            {description}
          </span>
        </div>
      </td>
      <td className="px-6 py-3 text-right">
        <Link href={`dictionary/tables/${name.toLowerCase()}`}>View fields</Link>
      </td>
    </tr>
  );
}
