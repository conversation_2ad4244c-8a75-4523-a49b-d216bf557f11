import './globals.css';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import { ReactFlowProvider } from '@xyflow/react';
import { FeatureFlagProvider, CustomerProvider, PageProvider } from '../providers';

export default function RootLayout({ children }) {
  return (
    <html lang="en" data-theme="corporate">
      <body>
        <UserProvider>
          <CustomerProvider>
            <FeatureFlagProvider>
              <ReactFlowProvider>
                <PageProvider>{children}</PageProvider>
              </ReactFlowProvider>
            </FeatureFlagProvider>
          </CustomerProvider>
        </UserProvider>
      </body>
    </html>
  );
}
