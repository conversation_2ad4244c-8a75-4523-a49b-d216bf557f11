import './globals.css';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import { PageProvider, InitProvider } from '../providers';

export default function RootLayout({ children }) {
  return (
    <html lang="en" data-theme="corporate">
      <body>
        <UserProvider>
          <InitProvider>
            <PageProvider>{children}</PageProvider>
          </InitProvider>
        </UserProvider>
      </body>
    </html>
  );
}
