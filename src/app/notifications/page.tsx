'use client';

// import Link from "next/link";
import { Helmet } from 'react-helmet';
import Dayjs from 'dayjs';
import calendar from 'dayjs/plugin/calendar';
import LocalizedFormat from 'dayjs/plugin/localizedFormat';
import { useState, useEffect } from 'react';
// import Button from "@/components/Button";
import InputText from '@/components/InputText';
import Badge from '@/components/Badge';
import AlertCircle from '@/components/icons/AlertCircle';
import AlertTriangle from '@/components/icons/AlertTriangle';
import FileSearch02 from '@/components/icons/FileSearch02';
// import FilterLines from "@/components/icons/FilterLines";
import { useCustomer } from '@/hooks';

Dayjs.extend(calendar);
Dayjs.extend(LocalizedFormat);

export default function Notifications() {
  const [_searchInputVal, setSearchInputVal] = useState();
  const [tabFilter, setTabFilter] = useState('Unseen');
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [showFullMessage, setShowFullMessage] = useState();

  const { customerFivetranDestId } = useCustomer();

  useEffect(() => {
    fetch(`/api/notifications/getlogs?fivetranDestId=${customerFivetranDestId}`)
      .then((res) => res.json())
      .then((rows) => {
        console.log(`rows: ${rows}`);
        const notificationResults: any[] = [];

        for (const [index, row] of rows.entries()) {
          let dataSource = row.CONNECTOR_ID ? `connector: ${row.CONNECTOR_ID}` : undefined;

          if (!dataSource && row.TRANSFORMATION_ID)
            dataSource = `transformation: ${row.TRANSFORMATION_ID}`;
          notificationResults.push({
            id: index + 1,
            message: `${row.MESSAGE_EVENT} - ${JSON.stringify(
              JSON.parse(row.MESSAGE_DATA),
              null,
              4,
            )}`,
            dataSource: dataSource,
            status: 'Unseen',
            received: new Date(row.TIME_STAMP),
            type: row.EVENT,
          });
        }
        // console.log(notificationResults);
        setNotifications(notificationResults);
        setFilteredData(notificationResults);
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
      });
  }, [customerFivetranDestId]);

  function evaluateSearch(e) {
    const { value } = e.target;
    setSearchInputVal(value);
    setFilteredData(
      notifications.filter((n) =>
        Object.keys(n).some((key) => n[key].toString().toLowerCase().includes(value.toLowerCase())),
      ),
    );
    e.preventDefault();
  }

  function tabFilterMatch(status) {
    return tabFilter == 'All' || tabFilter == status;
  }

  return (
    <div className="w-full max-w-full h-full min-h-full flex flex-col p-4 overflow-clip">
      <Helmet>
        <title>Notifications & alerts</title>
        <meta name="description" content="Notifications and alerts for data imports" />
      </Helmet>
      {!loading ? (
        <>
          <header className="mt-16 md:mt-8 lg:mt-0">
            <div className="shrink relative flex justify-between items-center w-full py-4">
              <div className="flex flex-row gap-x-2 text-center sm:text-left">
                <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
                  Notifications & alerts
                </h1>
              </div>
            </div>
          </header>

          <div className="w-full flex items-center gap-8">
            <div className="w-full flex gap-4 shrin border-b border-zinc-200">
              <button
                className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent
              hover:text-indigo-700 focus:outline-none ${tabFilter == 'Unseen' && 'activeTab'}`}
                onClick={() => setTabFilter('Unseen')}
              >
                Unseen
              </button>
              <button
                className={`pb-2 px-1 font-semibold text-zinc-500 border-b-2 border-transparent
                hover:text-indigo-700 focus:outline-none ${tabFilter == 'All' && 'activeTab'}`}
                onClick={() => setTabFilter('All')}
              >
                All
              </button>
            </div>

            <div className="flex gap-4">
              <div className="w-64">
                <InputText placeholder="Search" icon="SearchMd" onInput={evaluateSearch} />
              </div>
              {/* <Button label="Filter" type="secondary" Icon={FilterLines} /> */}
            </div>
          </div>

          <div className="w-full flex justify-stretch space-x-10 mt-6">
            <div className="w-7/12 w-full min-w-full flex flex-col space-y-3">
              <div className="rounded-lg overflow-hidden">
                <table
                  id="notificationsGrid"
                  className="w-full border-collapse bg-white text-left text-sm text-zinc-900"
                >
                  <thead className="bg-zinc-50">
                    <tr>
                      <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Message</th>
                      <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Data source</th>
                      <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Status</th>
                      <th className="px-6 py-3 text-xs font-semibold text-zinc-500">Received</th>
                      {/* <th className="px-6 py-3 text-xs font-semibold text-zinc-500"></th> */}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100 border-t border-gray-100">
                    {filteredData.map((notification) => (
                      <tr
                        key={notification.id}
                        className={!tabFilterMatch(notification.status) ? 'hidden' : undefined}
                      >
                        <td className="px-6 py-3 font-medium text-sm text-gray-900">
                          <p
                            onClick={() =>
                              setShowFullMessage(
                                showFullMessage == notification.id ? null : notification.id,
                              )
                            }
                          >
                            {notification.type === 'SEVERE' && (
                              <AlertCircle className="inline mr-2 stroke-red-700" />
                            )}
                            {notification.type === 'WARNING' ? (
                              <AlertTriangle className="inline mr-2 stroke-yellow-700" />
                            ) : (
                              <FileSearch02 className="inline mr-2 stroke-purple-500" />
                            )}
                            {notification.message.length < 250 || showFullMessage == notification.id
                              ? notification.message
                              : `${notification.message.substring(0, 250)}...`}
                          </p>
                        </td>
                        <td className="px-6 py-3 font-medium text-sm text-gray-900">
                          {notification.dataSource}
                        </td>
                        <td className="px-6 py-3 font-medium text-sm text-gray-900">
                          <Badge
                            label={notification.status}
                            type={notification.status == 'Unseen' ? 'error' : undefined}
                          />
                        </td>
                        <td className="px-6 py-3 font-medium text-sm text-gray-900">
                          {notification.received &&
                            Dayjs(notification.received).calendar(null, {
                              sameDay: '[Today at] LT',
                              lastDay: '[Yesterday at] LT',
                              lastWeek: 'LL [at] LT',
                              sameElse: 'LL [at] LT',
                            })}
                        </td>
                        {/* <td className="px-6 py-3 font-medium text-sm text-gray-900">
                          <Link
                            href={`/datasources/${notification.connector_id}/${notification.table_name}`}
                            className="text-sm hover:text-zinc-700 text-zinc-500"
                          >
                            Go to alert
                          </Link>
                        </td> */}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="h-full flex space-x-20 px-6">
          <div
            role="status"
            className="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2"
          >
            <svg
              aria-hidden="true"
              className="w-8 h-8 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
    </div>
  );
}
