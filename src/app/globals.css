@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

*,
html {
  font-family: 'DM Sans', sans-serif;
}

.activeTab {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity));
  --tw-border-opacity: 1;
  border-color: rgb(67 56 202 / var(--tw-border-opacity));
}

.sourceLinkOnline {
  background: url(../../public/img/sourceLink_Online.svg);
  background-repeat: repeat-x;
  animation: sourceLinkOnline 2s linear infinite;
}
@keyframes sourceLinkOnline {
  0% {
    background-position: 0 0;
  }
  to {
    background-position: 61px 0;
  }
}

.outboundLinkOnline {
  background: url(../../public/img/sourceLink_Online.svg);
  animation: outboundLinkOnline 2s linear infinite;
}
@keyframes outboundLinkOnline {
  0% {
    background-position: 0 0;
  }
  to {
    background-position: -61px 0;
  }
}

@keyframes dash {
  from {
    stroke-dashoffset: 1000;
  }
}

/* animates the archer line between two elements */
.archer-animation {
  animation: dash 60s linear infinite;
}

.sourceLinkDelayed {
  background: url(../../public/img/sourceLink_Delayed.svg);
  animation: sourceLinkDelayed 4s linear infinite;
}
@keyframes sourceLinkDelayed {
  0% {
    background-position: 0 0;
  }
  to {
    background-position: 80px 0;
  }
}

.outboundLinkDelayed {
  background: url(../../public/img/sourceLink_Delayed.svg);
  animation: outboundLinkDelayed 4s linear infinite;
}
@keyframes outboundLinkDelayed {
  0% {
    background-position: 0 0;
  }
  to {
    background-position: -80px 0;
  }
}

.ant-select-selection-item {
  max-height: 100%;
}

/* Hides scrollbar  */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  text-align: left;
  font-size: 14px;
  line-height: 20px;
  color: #0f172a;
}

.table-head {
  background-color: #fafafa;
}

.table-head-cell {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  white-space: nowrap;
  cursor: pointer;
}

.table-cell {
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
