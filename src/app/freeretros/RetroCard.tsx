import { Button } from '@/components';
import Image from 'next/image';
import { Retro } from '@/types';
import { imgSrc } from 'lib/source';
import PartnerBadge from './PartnerBadge';
import { completedStatuses } from '@/constants/retro';
import { kebabCase } from 'lodash';

type Props = {
  retro: Retro;
};

export default function RetroCard(props: Props) {
  const { retro } = props;
  const { name, description, url, recommended, latestCustomerRetroStatus, hasHistory } = retro;

  const latestRetroCompleted =
    latestCustomerRetroStatus && completedStatuses.includes(latestCustomerRetroStatus);

  return (
    <div className="w-[360px] h-[320px] flex rounded-xl border border-zinc-200">
      <div className="w-full flex flex-col">
        <div className="w-full h-3/4 flex flex-col p-4">
          <div className="flex justify-between mb-4 w-full">
            <div className="p-2 border border-zinc-100 rounded-xl">
              <Image
                src={imgSrc.getOrDefault(retro.kind).imgPath}
                alt={name}
                width={48}
                height={48}
              />
            </div>
            <div className="flex flex-col items-end gap-y-2">
              {latestRetroCompleted && <PartnerBadge status="completed" />}
              {recommended && <PartnerBadge recommended />}
            </div>
          </div>
          <div className="flex flex-col">
            <span className="text-xl">{name}</span>
            <span className="text-sm text-zinc-400 mb-2">{url}</span>
            <span className="text-sm text-zinc-600 line-clamp-3" title={description}>
              {description}
            </span>
          </div>
        </div>
        <div className="h-1/4 flex justify-end items-center px-4 w-full border-t border-zinc-200 gap-x-2">
          {hasHistory && (
            <Button
              type="secondary"
              icon="File07"
              label="Review results"
              href={`/freeretros/${kebabCase(retro.kind)}/history`}
            />
          )}
          <Button
            type="secondary"
            label="Get free retro"
            href={`/freeretros/${kebabCase(retro.kind)}`}
          />
        </div>
      </div>
    </div>
  );
}
