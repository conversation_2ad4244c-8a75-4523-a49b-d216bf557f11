'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { PageContent, InputText, DataFilter, Loading } from '@/components';
import RetroCard from './RetroCard';
import { FilterValue, Retro } from '@/types';
import { filterData } from '@/utils/data';
import { useCustomer } from '@/hooks';
import { api } from '@/helpers/web';

const filterOptions = [
  {
    legend: 'Status',
    type: 'status',
    options: [
      { label: 'All', value: 'all-status', filter: false },
      { label: 'Completed', value: 'completed' },
      { label: 'Error', value: 'error' },
    ],
  },
  {
    legend: 'Recommended',
    type: 'recommended',
    options: [
      { label: 'All', value: 'all-recommended', filter: false },
      { label: 'Recommended', value: 'recommended' },
    ],
  },
];

const filterFields = {
  status: 'status',
  recommended: (col) => col.recommended,
};

type Params = {
  partner: string;
};

type Props = {
  params: Params;
};

export default function FreeRetros(props: Props) {
  const { params } = props;
  const { partner } = params;

  const [filterConfig, setFilterConfig] = useState<Record<string, FilterValue>>({
    status: { label: 'All', value: 'all-status', filter: false },
    recommended: { label: 'All', value: 'all-recommended', filter: false },
  });

  const [partnerRetros, setPartnerRetros] = useState<Retro[]>();

  const [searchTerm, setSearchTerm] = useState('');

  const { customerId } = useCustomer();

  // fetch partner retro info
  useEffect(() => {
    if (!customerId) {
      return;
    }

    const getPartnerRetros = async () => {
      const { retros } = await api.get(`/api/free-retros/${partner}/getPartnerRetros`, {
        customerId,
      });

      setPartnerRetros(retros);
    };

    getPartnerRetros();
  }, [customerId, partner]);

  if (!partnerRetros?.length) {
    return <Loading />;
  }

  const filteredRetros = filterData<Retro>(partnerRetros, {
    searchTerm,
    nameField: 'name',
    filterConfig,
    filterFields,
  });

  return (
    <PageContent>
      <div className="rounded-xl bg-white">
        <div className="flex flex-col w-full h-[346px] items-center justify-center relative bg-zinc-50">
          <Image src="/img/freeretros-header.svg" alt="Free retros" fill className="absolute z-0" />
          <div className="flex flex-col items-center justify-center w-1/2 z-10">
            <span className="text-center text-zinc-600 mb-8">Rapid experimentation:</span>
            <span className="text-center text-4xl">
              Discover which vendor products can provide big value-add to your business
            </span>
          </div>
        </div>
        <div className="p-10">
          <div className="flex items-center mb-10 w-full">
            <div className="flex flex-col w-full mr-4">
              <InputText
                placeholder="Search test drives"
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                }}
                value={searchTerm}
              />
            </div>
            <DataFilter
              filterConfig={filterConfig}
              onFilterConfigChange={setFilterConfig}
              filterOptions={filterOptions}
            />
          </div>
          <div className="flex flex-wrap gap-4">
            {filteredRetros.map((retro) => (
              <RetroCard retro={retro} key={retro.id} />
            ))}
          </div>
        </div>
      </div>
    </PageContent>
  );
}
