import { JaggedCheck, Check } from '@/components/icons';

type Props = {
  recommended?: boolean;
  status?: 'completed' | 'error';
};

export default function PartnerBadge(props: Props) {
  const { recommended, status } = props;

  let className = 'px-2 py-1.5 flex space-x-1.5 justify-center items-center rounded w-32 h-8';
  let textClass = 'text-xs';
  let icon: any = null;
  let statusMessage = '';

  if (recommended) {
    className = `${className} bg-purple-50`;
    textClass = `${textClass} text-purple-800`;
    icon = <JaggedCheck className="stroke-purple-800" />;
    statusMessage = 'Recommended';
  }

  if (status === 'completed') {
    className = `${className} bg-emerald-50`;
    textClass = `${textClass} text-emerald-800`;
    icon = <Check className="stroke-emerald-800 w-4 stroke-2" />;
    statusMessage = 'Completed';
  }

  return (
    <div className={className}>
      {icon}
      <span className={textClass}>{statusMessage}</span>
    </div>
  );
}
