import dayjs from 'dayjs';
import { useParams } from 'next/navigation';
import { CustomerRetro } from '@/types';
import { Badge } from '@/components';
import { Play, Check } from '@/components/icons';
import { Button } from '@/components';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { completedStatuses } from '@/constants/retro';

dayjs.extend(localizedFormat);

type Props = {
  retroHistory: CustomerRetro;
};

type Params = {
  partner: string;
};

export default function HistoryRow(props: Props) {
  const { retroHistory } = props;
  const { status, createdAt, createdBy, customerRetroId, endedAt } = retroHistory;

  const { partner } = useParams() as Params;

  const createdDayjs = dayjs(createdAt);

  const createdAtDate = createdDayjs.format('LL');
  const createdAtTime = createdDayjs.format('LT');

  let endedAtMessage = 'Has not ended';

  if (endedAt) {
    const endedDayjs = dayjs(endedAt);
    const endedAtTime = endedDayjs.format('LT');

    endedAtMessage = `Ended at ${endedAtTime}`;
  }

  const retroComplete = status && completedStatuses.includes(status);

  let statusBadge = <Badge type="warning" label="Pending partnership" />;

  if (status === 'accepted') {
    statusBadge = <Badge type="success" label="Accepted partnership" />;
  }

  if (status === 'declined') {
    statusBadge = <Badge type="error" label="Rejected partnership" />;
  }

  let headerMessage = `Started on ${createdAtDate}`;

  if (createdBy) {
    headerMessage = `${headerMessage} by ${createdBy}`;
  }

  return (
    <tr className="bg-white">
      <td className="px-6 py-2 font-medium text-sm text-gray-900">
        <div className="gap-x-2 text-center sm:text-left flex flex-row items-center mb-2">
          <span className="font-semibold text-lg">{headerMessage}</span>
        </div>
        <div className="gap-x-4 flex flex-row items-center text-gray-500 stroke-gray-500">
          <div className="flex items-center gap-x-1">
            <Play className="w-4 h-4 stroke-inherit" />
            <span>{`Started at ${createdAtTime}`}</span>
          </div>
          <div className="flex gap-x-1 items-center">
            <Check className="w-4 h-4 stroke-inherit" />
            <span>{endedAtMessage}</span>
          </div>
        </div>
      </td>
      <td className="px-6 py-2">{statusBadge}</td>
      <td className="px-6 py-2">
        {retroComplete ? (
          <div className="flex justify-center items-center">
            <Button
              label="View result"
              icon="File07"
              type="secondary"
              href={`/freeretros/${partner}/${customerRetroId}`}
            />
          </div>
        ) : (
          <Badge type="neutral" label="Result pending" />
        )}
      </td>
    </tr>
  );
}
