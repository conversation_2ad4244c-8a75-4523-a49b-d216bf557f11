'use client';

import { useState, useEffect } from 'react';
import { PageContent, PageTitle, Loading, Button } from '@/components';
import { useRouter } from 'next/navigation';
import { unSlugify } from '@/utils/string';
import { useCustomer } from '@/hooks';
import { Retro, CustomerRetro } from '@/types';
import HistoryRow from './HistoryRow';
import { getCurrentRetro, handleCustomerRetro } from '../utils';
import { useUser } from '@auth0/nextjs-auth0/client';

type Params = {
  partner: string;
};

type Props = {
  params: Params;
};

export default function RetroHistoryPage(props: Props) {
  const { params } = props;
  const { partner } = params;

  const router = useRouter();
  const { user } = useUser();
  const { customerId } = useCustomer();

  const [retro, setRetro] = useState<Retro>();
  const [retroHistory, setRetroHistory] = useState<CustomerRetro[]>();
  const [customerRetroLoading, setCustomerRetroLoading] = useState<boolean>(false);

  // get retro info
  useEffect(() => {
    if (!customerId) {
      return;
    }

    const getRetro = async () => {
      const currentRetro = await getCurrentRetro({ partner, customerId });

      setRetro(currentRetro);
    };

    getRetro();
  }, [partner, customerId]);

  // get customer retro history
  useEffect(() => {
    if (!customerId) {
      return;
    }

    const getHistory = async () => {
      const res = await fetch(
        `/api/free-retros/${partner}/getRetroHistory?customerId=${customerId}`,
      );

      const { retroHistory } = await res.json();

      setRetroHistory(retroHistory);
    };

    getHistory();
  }, [customerId, partner]);

  const handleStartRetro = async () => {
    await handleCustomerRetro({
      retro: retro!,
      partner,
      customerId,
      userName: user!.name as string,
      onLoading: setCustomerRetroLoading,
      router,
    });
  };

  if (!retro || !retroHistory) {
    return <Loading />;
  }

  return (
    <PageContent>
      <PageTitle
        title={`Past ${unSlugify(partner)} retros`}
        subpage
        breadcrumbs={[
          { label: 'Free retros', url: '/freeretros' },
          {
            label: `Past ${unSlugify(partner)} retros`,
            url: `/freeretros/${partner}/history`,
          },
        ]}
      >
        <Button
          label={customerRetroLoading ? 'Loading...' : 'Get free retro'}
          onClick={handleStartRetro}
        />
      </PageTitle>
      <div className="p-4 bg-white rounded-xl">
        <table className="w-full">
          <tbody className="divide-y divide-gray-100">
            {retroHistory.map((history) => (
              <HistoryRow retroHistory={history} key={history.customerRetroId} />
            ))}
          </tbody>
        </table>
      </div>
    </PageContent>
  );
}
