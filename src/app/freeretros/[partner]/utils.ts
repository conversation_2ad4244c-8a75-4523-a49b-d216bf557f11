import { Retro, SetStateFunction } from '@/types';
import { InsertCustomerRetro } from '@/types/routes';
import { initialStatuses } from '@/constants/retro';
import { camelCase } from 'lodash';
import { api } from '@/helpers/web';

type GetRetroProps = {
  partner: string;
  customerId: string;
};

export async function getCurrentRetro(props: GetRetroProps): Promise<Retro> {
  const { partner, customerId } = props;

  const { retros } = await api.get(`/api/free-retros/${camelCase(partner)}/getPartnerRetros`, {
    customerId,
  });

  const currentRetro = retros.find((retro) => retro.kind === camelCase(partner));

  return currentRetro;
}

type HandleRetroProps = {
  retro: Retro;
  partner: string;
  customerId: string;
  userName: string;
  onLoading: SetStateFunction<boolean>;
  router: any;
};

export async function handleCustomerRetro(props: HandleRetroProps) {
  const { retro, partner, customerId, userName, onLoading, router } = props;

  // if latest customer retro is in initial state, we don't need to insert a record. We redirect using that customer retro's id
  const isInitial =
    retro?.latestCustomerRetroStatus && initialStatuses.includes(retro.latestCustomerRetroStatus);

  if (isInitial) {
    router.push(`/freeretros/${partner}/${retro!.latestCustomerRetroId}`);

    return;
  }

  // otherwise, we need to insert a new customer retro record and redirect using that id
  onLoading(true);

  const insertRetroProps: InsertCustomerRetro = {
    customerId,
    retroId: retro!.id,
    userName,
  };

  const { customerRetroId } = await api.post(
    `/api/free-retros/${partner}/insertCustomerRetro`,
    insertRetroProps,
  );

  router.push(`/freeretros/${partner}/${customerRetroId}`);

  onLoading(false);
}
