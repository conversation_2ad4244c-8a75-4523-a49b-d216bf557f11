import { Modal, PdfIframe } from '@/components';
import { SetStateFunction } from '@/types';

type Props = {
  isOpen: boolean;
  onOpenChange: SetStateFunction<boolean>;
  pdfData: any;
  docTitle: string;
};

export default function DataStudyModal(props: Props) {
  const { isOpen, onOpenChange, pdfData, docTitle } = props;

  return (
    <Modal
      open={isOpen}
      setIsOpen={onOpenChange}
      title={
        <div className="flex gap-2 items-center text-lg">
          <h2>{docTitle}</h2>
        </div>
      }
      className="w-[1054px] h-[600px] overflow-scroll"
      fillContent
    >
      <PdfIframe docTitle={docTitle} data={pdfData} />
    </Modal>
  );
}
