import { SolutionCopy } from '@/types';

type Props = {
  solutionCopy: SolutionCopy;
};

export default function SolutionCopyComponent(props: Props) {
  const { solutionCopy } = props;
  const { header, overview, content } = solutionCopy;

  return (
    <div className="flex flex-col w-1/2">
      <span className="text-zinc-900 text-2xl mb-6">{header}</span>
      <span className="text-zinc-500 mb-4">{overview}</span>
      <div className="flex flex-col gap-y-4">
        {content.map(({ header, body }, idx) => (
          <div className="flex flex-col gap-y-1" key={`${header}${idx}`}>
            <span className="font-bold text-zinc-900">{header}</span>
            <span className="text-zinc-500">{body}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
