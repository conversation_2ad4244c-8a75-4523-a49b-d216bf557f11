import { useState, useMemo } from 'react';
import { BasicTable, InputText } from '@/components';
import ApexBarChart from '@/components/client-only/ApexBarChart';
import { FingoalResultsData, TransactionTagMetadata, FingoalUserTagMetadata } from '@/types';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { getFingoalChartProps } from './utils';
import { startCase, upperFirst, orderBy } from 'lodash';
import { filterData } from '@/utils/data';

dayjs.extend(localizedFormat);

type Props = {
  data: FingoalResultsData;
};

export default function FingoalResultsDisplay(props: Props) {
  const { data } = props;
  const { results, additionalResults } = data;
  const { transactionTagMetadata } = results;
  const { userTagMetadata = [] } = additionalResults || {};

  const [txnTagSearchTerm, setTxnTagSearchTerm] = useState<string>();
  const [userTagSearchTerm, setUserTagSearchTerm] = useState<string>();

  const mock = {
    results,
    additionalResults: { userTagMetadata },
  };

  const chartProps = getFingoalChartProps(mock);

  const tableOpts = {
    formatHeader: (header) => upperFirst(startCase(header).toLowerCase()),
  };

  const tableContainerClass =
    'flex flex-col max-h-[500px] overflow-y-hidden w-full bg-white shadow-md rounded-lg border border-gray-200 border-collapse';
  const tableSubContainerClass = 'overflow-y-auto';

  const filteredTxnTagMetadata = useMemo(() => {
    return filterData<TransactionTagMetadata>(transactionTagMetadata, {
      searchTerm: txnTagSearchTerm,
      nameField: 'transactionTag',
    });
  }, [transactionTagMetadata, txnTagSearchTerm]);

  const filteredUserTagMetadata = useMemo(() => {
    return filterData<FingoalUserTagMetadata>(userTagMetadata, {
      searchTerm: userTagSearchTerm,
      nameField: 'userTag',
    });
  }, [userTagMetadata, userTagSearchTerm]);

  return (
    <div className="flex flex-col gap-y-6">
      <div className="grid grid-cols-2 gap-6">
        {chartProps.map((props) => (
          <div
            key={props.title}
            className="bg-white rounded-lg shadow-md p-4 border border-gray-200"
          >
            <ApexBarChart {...props} />
          </div>
        ))}
      </div>
      <div className="flex gap-x-6 w-full">
        <div className="flex flex-col gap-y-2 w-full">
          <div className="w-1/2">
            <InputText
              icon={'SearchMd'}
              placeholder="Search"
              onChange={(e) => setTxnTagSearchTerm(e.target.value)}
            />
          </div>
          <div className={tableContainerClass}>
            <div className={tableSubContainerClass}>
              <BasicTable
                data={orderBy(filteredTxnTagMetadata, 'userCount', 'desc')}
                opts={{ ...tableOpts, rowKeyAccessor: (row) => row.transactionTag }}
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-y-2 w-full">
          <div className="w-1/2">
            <InputText
              icon={'SearchMd'}
              placeholder="Search"
              onChange={(e) => setUserTagSearchTerm(e.target.value)}
            />
          </div>
          <div className={tableContainerClass}>
            <div className={tableSubContainerClass}>
              <BasicTable
                data={orderBy(filteredUserTagMetadata, 'userCount', 'desc')}
                opts={{ ...tableOpts, rowKeyAccessor: (row) => row.userTag }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
