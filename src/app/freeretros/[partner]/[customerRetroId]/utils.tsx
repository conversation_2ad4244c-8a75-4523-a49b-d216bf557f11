import { ReactNode } from 'react';
import {
  RetroStatus,
  CustomerRetro,
  RetroSurveyQuestion,
  StepProps,
  FingoalResultsData,
  BarChartProps,
  RetroResults,
} from '@/types';
import { <PERSON><PERSON>, Badge } from '@/components';
import { AlarmClock } from '@/components/icons';
import NDAStep from './NDAStep';
import SurveyQuestions from './SurveyQuestions';
import Processing from './Processing';
import OutputReview from './OutputReview';
import { api } from '@/helpers/web';
import { orderBy } from 'lodash';
import { downloadFiles, downloadCsv } from '@/utils/file';

type CopyProps = {
  retroName: string;
  retroStatus: string;
};

export function getRetroCompletedCopy(props: CopyProps): string {
  const { retroName, retroStatus } = props;

  const undecidedCopy = `If you find the file that ${retroName} created for you is helpful and would like to
  partner with them, select the “Partner with ${retroName}” button below, otherwise
  select decline. You can always reevaluate partnership in the future.`;

  const declinedCopy = `You have chosen to reject a partnership with ${retroName} at this time. You may reconsider a partnership by starting a new free retro with them at any time.`;

  const acceptedCopy = `You have chosen to accept a partnership with ${retroName} at this time. A ${retroName} representative will be reaching out to you soon through email.`;

  switch (retroStatus) {
    case 'accepted':
      return acceptedCopy;
    case 'declined':
      return declinedCopy;
    default:
      return undecidedCopy;
  }
}

export function genFingoalResultsCsv(resultsData: FingoalResultsData): string {
  const { results, additionalResults } = resultsData;
  const { transactionTagMetadata } = results;
  const { userTagMetadata } = additionalResults;

  const headers = ['Tag', 'Tag type', 'User count'];

  const sortedTxnTags = orderBy(transactionTagMetadata, 'userCount', 'desc');
  const sortedUserTags = orderBy(userTagMetadata, 'userCount', 'desc');

  const txnTagVals = sortedTxnTags.map(({ transactionTag, userCount }) => [
    transactionTag,
    'transaction',
    userCount,
  ]);

  const userTagVals = sortedUserTags.map(({ userTag, userCount }) => [userTag, 'user', userCount]);

  const headerStr = headers.join(',');
  const txnTagStrings = txnTagVals.map((vals) => vals.join(','));
  const userTagStrings = userTagVals.map((vals) => vals.join(','));

  return `${headerStr}\n${txnTagStrings.join('\n')}\n${userTagStrings.join('\n')}`;
}

export function genResultsCsv(resultsData: RetroResults, partner: string): string {
  switch (partner) {
    case 'fingoal':
      return genFingoalResultsCsv(resultsData as FingoalResultsData);
    default:
      throw new Error('No partner path found for csv generation');
  }
}

type ActionProps = {
  retroName: string;
  retroStatus: string;
  handleRetroDecision: (status: RetroStatus) => Promise<void>;
  acceptSaving: boolean;
  declineSaving: boolean;
  resultsUrl: string;
  additionalResultsUrl?: string;
  resultsData?: RetroResults;
};

// returns footer actions based on status
export function getRetroCompletedAction(props: ActionProps): () => ReactNode {
  const {
    retroName,
    retroStatus,
    handleRetroDecision,
    acceptSaving,
    declineSaving,
    resultsUrl,
    additionalResultsUrl,
    resultsData,
  } = props;

  const downloadButtonProps = {
    type: 'secondary',
    label: 'Download File',
    iconLeft: 'Download01',
    onClick: async () => {
      if (resultsData) {
        const csvStr = genResultsCsv(resultsData, retroName.toLowerCase());

        await downloadCsv(csvStr, 'retro_results.csv');

        return;
      }

      const files = [{ name: 'retro_results.json', url: resultsUrl }];

      if (additionalResultsUrl) {
        files.push({ name: 'additional_retro_results.json', url: additionalResultsUrl });
      }

      await downloadFiles(document, files);
    },
  };

  const undecidedAction = () => (
    <>
      <Button
        label={acceptSaving ? 'Saving...' : `Partner with ${retroName}`}
        disabled={acceptSaving}
        onClick={() => {
          handleRetroDecision('accepted');
        }}
      />
      <Button
        type="secondary"
        label={declineSaving ? 'Saving...' : 'Decline partnership for now'}
        disabled={declineSaving}
        onClick={() => {
          handleRetroDecision('declined');
        }}
      />
      <Button {...downloadButtonProps} />
    </>
  );

  const declinedAction = () => (
    <>
      <Badge label="Partnership rejected" type="error" /> <Button {...downloadButtonProps} />{' '}
    </>
  );

  const acceptedAction = () => (
    <>
      <Badge label="Partnership accepted" type="success" /> <Button {...downloadButtonProps} />{' '}
    </>
  );

  switch (retroStatus) {
    case 'accepted':
      return acceptedAction;
    case 'declined':
      return declinedAction;
    default:
      return undecidedAction;
  }
}

type StatusProps = {
  customerRetro: CustomerRetro;
  partner: string;
  customerKey: string;
  hasSurvey: boolean;
};

type StatusResult = {
  targetStep: number;
  resultsData?: RetroResults;
  resultsUrl?: string;
  additionalResultsUrl?: string;
};

export async function handleRetroStatus(props: StatusProps): Promise<StatusResult> {
  const { customerRetro, partner, customerKey, hasSurvey } = props;
  const { customerRetroId, config, status, retroConfig } = customerRetro;

  const ndaStep = 0;
  const surveyStep = 1;
  const processingStep = hasSurvey ? 2 : 1;
  const reviewStep = hasSurvey ? 3 : 2;

  // if agreement has not yet been signed, return the agreement signature step
  if (!config?.agreementSigned) {
    return { targetStep: ndaStep };
  }

  // if retro is initiated and requires a survey step, return survey step
  if (status === 'initiated' && hasSurvey) {
    return { targetStep: surveyStep };
  }

  // if there is no survey required or the survey has been completed, generate the output data and return the processing step
  if (['initiated', 'surveyComplete'].includes(status!)) {
    await api.post(`/api/free-retros/${partner}/generateRetroData`, {
      partner,
      customerKey,
      customerRetroId,
    });

    return { targetStep: processingStep };
  }

  // if retro is pending (awaiting results), check for results. If results are available,
  // update the retro status and return the file for display.
  if (status === 'pending') {
    // if it's an api data exchange, we don't need any extra retro handling. We just wait for the data
    // exchange to complete.
    if (retroConfig.data_exchange_type === 'api') {
      return { targetStep: processingStep };
    }

    const { newStatus, resultsUrl } = await api.get(
      `/api/free-retros/${partner}/handleRetroResult`,
      { customerKey, customerRetroId },
    );

    // if results are not yet available, still return the loading step
    if (newStatus === 'pending') {
      return { targetStep: processingStep };
    }

    // if they are available, return the final step and the url for the results data
    return { targetStep: reviewStep, resultsUrl };
  }

  // if retro is already completed, return the final step and the url for the results data
  const { resultsUrl, additionalResultsUrl, resultsData } = await api.get(
    `/api/free-retros/${partner}/getRetroResults`,
    {
      customerRetroId,
      customerKey,
    },
  );

  return {
    targetStep: reviewStep,
    resultsUrl,
    additionalResultsUrl,
    resultsData,
  };
}

// compares list of survey questions with object responses to determine survey completion
export function getSurveyComplete(
  questions: RetroSurveyQuestion[],
  values: Record<string, any>,
): boolean {
  return questions.every((question) => !!values[question.question_text]);
}

const baseSteps: StepProps[] = [
  {
    header: 'Sign NDA',
    content: (
      <div className="flex flex-row items-center">
        <AlarmClock className="stroke-inherit mr-2" />
        <h3 className="text-inherit">Less than a minute</h3>
      </div>
    ),
  },
  {
    header: 'Wait for data to process',
    content: (
      <div className="flex flex-row items-center">
        <AlarmClock className="stroke-inherit mr-2" />
        <h3 className="text-inherit">Within 12 hours</h3>
      </div>
    ),
  },
  {
    header: 'Review output',
    content: (
      <div className="flex flex-row items-center">
        <AlarmClock className="stroke-inherit mr-2" />
        <h3 className="text-inherit">About 5 minutes</h3>
      </div>
    ),
  },
];

const surveyStep = {
  header: 'Describe your use case',
  content: (
    <div className="flex flex-row items-center">
      <AlarmClock className="stroke-inherit mr-2" />
      <h3 className="text-inherit">5 minutes</h3>
    </div>
  ),
};

type GetStepsProps = {
  hasSurvey: boolean;
};

export function getRetroSteps(props: GetStepsProps): StepProps[] {
  const { hasSurvey } = props;

  if (hasSurvey) {
    return [baseSteps[0], surveyStep, ...baseSteps.slice(1)];
  }

  return baseSteps;
}

type GetStepContentsProps = {
  customerRetro?: CustomerRetro;
  hasSurvey: boolean;
  handleRetro: Function;
  resultsData?: RetroResults;
  resultsUrl?: string;
  additionalResultsUrl?: string;
};

export function getRetroStepContents(props: GetStepContentsProps): ReactNode[] {
  const { customerRetro, hasSurvey, handleRetro, resultsData, resultsUrl, additionalResultsUrl } =
    props;

  if (!customerRetro) {
    return [];
  }

  if (hasSurvey) {
    return [
      <NDAStep onNext={handleRetro} customerRetro={customerRetro} key={0} />,
      <SurveyQuestions customerRetro={customerRetro} onNext={handleRetro} key={1} />,
      <Processing customerRetro={customerRetro} key={2} />,
      <OutputReview
        customerRetro={customerRetro}
        resultsData={resultsData}
        resultsUrl={resultsUrl}
        additionalResultsUrl={additionalResultsUrl}
        key={3}
      />,
    ];
  }

  return [
    <NDAStep onNext={handleRetro} customerRetro={customerRetro} key={0} />,
    <Processing customerRetro={customerRetro} key={1} />,
    <OutputReview
      customerRetro={customerRetro}
      resultsData={resultsData}
      resultsUrl={resultsUrl}
      additionalResultsUrl={additionalResultsUrl}
      key={2}
    />,
  ];
}

export function getHasSurvey(customerRetro?: CustomerRetro): boolean {
  return !!customerRetro?.surveyQuestions?.questions?.length;
}

export function getFingoalChartProps(data: FingoalResultsData): BarChartProps[] {
  const { results, additionalResults } = data;
  const { transactionTagMetadata } = results;
  const { userTagMetadata } = additionalResults;

  const topTransactionTags = orderBy(transactionTagMetadata, 'userCount', 'desc').slice(0, 5);
  const topUserTags = orderBy(userTagMetadata, 'userCount', 'desc').slice(0, 5);

  return [
    {
      title: 'Transaction tags',
      xCategories: topTransactionTags.map((tagObj) => tagObj.transactionTag),
      xTitle: 'Transaction tag',
      yTitle: 'User count',
      seriesData: [
        { name: 'userCount', data: topTransactionTags.map((tagObj) => tagObj.userCount) },
      ],
      style: {
        color: '#45a7c0',
      },
    },
    {
      title: 'User tags',
      xCategories: topUserTags.map((tagObj) => tagObj.userTag),
      xTitle: 'User tag',
      yTitle: 'User count',
      seriesData: [{ name: 'userCount', data: topUserTags.map((tagObj) => tagObj.userCount) }],
      style: {
        color: '#45a7c0',
      },
    },
  ];
}
