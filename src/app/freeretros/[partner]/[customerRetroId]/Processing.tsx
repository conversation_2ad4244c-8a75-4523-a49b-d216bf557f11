import Image from 'next/image';
import { Card } from '@/components';
import { CustomerRetro } from '@/types';

type Props = {
  customerRetro: CustomerRetro;
};

export default function Processing(props: Props) {
  const { customerRetro } = props;

  if (!customerRetro) {
    return null;
  }

  return (
    <Card>
      <div className="flex flex-col w-1/2 gap-y-8 items-center py-36">
        <Image src="/img/freeretros-processing.svg" width={250} height={250} alt="Processing" />
        <div className="flex flex-col gap-y-2 items-center">
          <span className="text-zinc-700 text-2xl">
            {customerRetro.retroName} is processing your data
          </span>
          <span className="text-zinc-500 text-center">
            We’ve sent {customerRetro.retroName} the necessary data to provide you with a preview of
            their services. This process could take up to 12 hours, we will email you when it’s
            ready!
          </span>
        </div>
      </div>
    </Card>
  );
}
