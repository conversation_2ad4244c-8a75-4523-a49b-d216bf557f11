import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card } from '@/components';
import { CustomerRetro, RetroStatus, RetroResults } from '@/types';
import { getRetroCompletedCopy, getRetroCompletedAction } from './utils';
import { message } from 'antd';
import { useCustomer } from '@/hooks';
import { api } from '@/helpers/web';
import ResultsDisplay from './ResultsDisplay';

type Props = {
  customerRetro: CustomerRetro;
  resultsUrl?: string;
  additionalResultsUrl?: string;
  resultsData?: RetroResults;
};

type Params = {
  partner: string;
};

export default function OutputReview(props: Props) {
  const { customerRetro, resultsUrl, additionalResultsUrl, resultsData } = props;
  const { partner } = useParams() as Params;

  const [retroStatus, setRetroStatus] = useState<RetroStatus>();
  const [acceptSaving, setAcceptSaving] = useState<boolean>(false);
  const [declineSaving, setDeclineSaving] = useState<boolean>(false);

  const [messageApi, contextHolder] = message.useMessage();
  const { customerName } = useCustomer();

  // initially set retro status to the current status of the retro
  useEffect(() => {
    setRetroStatus(customerRetro.status);
  }, [customerRetro]);

  const handleRetroDecision = async (status: 'accepted' | 'declined') => {
    if (status === 'accepted') {
      setAcceptSaving(true);
    } else {
      setDeclineSaving(true);
    }

    const { success } = await api.post(`/api/free-retros/${partner}/handleRetroDecision`, {
      customerRetroId: customerRetro.customerRetroId,
      status,
      customerName,
      retroName: customerRetro.retroName,
    });

    if (success) {
      messageApi.open({ type: 'success', content: 'Partnership information saved' });

      setRetroStatus(status);
    } else {
      messageApi.open({ type: 'error', content: 'Error saving partnership information' });
    }

    setAcceptSaving(false);
    setDeclineSaving(false);
  };

  if (!customerRetro || !retroStatus || !resultsUrl) {
    return null;
  }

  const completedCopy = getRetroCompletedCopy({ retroName: customerRetro.retroName, retroStatus });

  const CompletedAction = getRetroCompletedAction({
    retroName: customerRetro.retroName,
    retroStatus,
    handleRetroDecision,
    acceptSaving,
    declineSaving,
    resultsUrl,
    additionalResultsUrl,
    resultsData,
  });

  return (
    <div className="flex flex-col gap-y-12">
      {contextHolder}
      <Card>
        <div className="px-6">
          <div className="flex flex-col gap-y-6 w-1/2">
            <span className="text-zinc-900 text-2xl">Your file is ready!</span>
            <span className="text-zinc-900">
              {customerRetro.retroName} has processed your data and returned a file for your review.
              Download or view the file that {customerRetro.retroName} returned below.
              <br />
              <br />
              {completedCopy}
            </span>
            <div className="flex w-full gap-x-3 items-center">
              <CompletedAction />
            </div>
          </div>
        </div>
      </Card>
      <ResultsDisplay partner={partner} resultsData={resultsData} />
    </div>
  );
}
