'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { OverviewSteps, PageContent, PageTitle, Loading } from '@/components';
import { slugify, unSlugify } from '@/utils/string';
import { useCustomer } from '@/hooks';
import { CustomerRetro, RetroResults } from '@/types';
import { handleRetroStatus, getRetroSteps, getRetroStepContents, getHasSurvey } from './utils';
import { api } from '@/helpers/web';
import { isNil } from 'lodash';

type Params = {
  partner: string;
  customerRetroId: string;
};

type Props = {
  params: Params;
};

export default function CustomerRetroComponent(props: Props) {
  const { params } = props;
  const { partner, customerRetroId } = params;

  const { customerId, customerKey } = useCustomer();

  const [selectedStepIdx, setSelectedStepIdx] = useState<number>();
  // array of index of completed steps
  const [completedStepIdxs, setCompletedStepIdxs] = useState<number[]>([]);
  const [customerRetro, setCustomerRetro] = useState<CustomerRetro>();
  const [resultsUrl, setResultsUrl] = useState<string>();
  const [additionalResultsUrl, setAdditionalResultsUrl] = useState<string>();
  const [resultsData, setResultsData] = useState<RetroResults>();

  const handleRetro = useCallback(async () => {
    const customerRetro: CustomerRetro = await api.get(
      `/api/free-retros/${partner}/getCustomerRetro`,
      { customerRetroId },
    );

    setCustomerRetro(customerRetro);

    const hasSurvey = getHasSurvey(customerRetro);

    const { targetStep, resultsData, resultsUrl, additionalResultsUrl } = await handleRetroStatus({
      customerRetro,
      partner,
      customerKey,
      hasSurvey,
    });

    setSelectedStepIdx(targetStep);
    setResultsUrl(resultsUrl);
    setAdditionalResultsUrl(additionalResultsUrl);
    setResultsData(resultsData);
  }, [partner, customerRetroId, customerKey]);

  // fetch customer retro, handle steps based on retro status and config, and handle generating the data file.
  useEffect(() => {
    if (!customerId || !customerKey) {
      return;
    }

    const getCustomerRetro = async () => {
      await handleRetro();
    };

    getCustomerRetro();
  }, [customerId, customerKey, handleRetro]);

  // handle completed steps
  useEffect(() => {
    const completedSteps = Array.from(Array(selectedStepIdx), (_, idx) => idx);

    setCompletedStepIdxs(completedSteps);
  }, [selectedStepIdx]);

  const steps = useMemo(
    () => getRetroSteps({ hasSurvey: getHasSurvey(customerRetro) }),
    [customerRetro],
  );

  const stepContents = useMemo(
    () =>
      getRetroStepContents({
        customerRetro,
        hasSurvey: getHasSurvey(customerRetro),
        resultsData,
        resultsUrl,
        additionalResultsUrl,
        handleRetro,
      }),
    [customerRetro, resultsData, resultsUrl, additionalResultsUrl, handleRetro],
  );

  if (!customerRetro || isNil(selectedStepIdx) || !stepContents[selectedStepIdx]) {
    return <Loading />;
  }

  const contents = stepContents[selectedStepIdx];

  return (
    <PageContent>
      <PageTitle
        title={`${customerRetro.retroName} Retro`}
        subpage
        breadcrumbs={[
          { label: 'Free retros', url: '/freeretros' },
          {
            label: unSlugify(partner),
            url: `/freeretros/${slugify(partner)}`,
          },
          {
            label: 'Retro',
            url: `/freeretros/${slugify(partner)}/retro`,
          },
        ]}
      />
      <div className="flex flex-col gap-y-12">
        <OverviewSteps
          steps={steps}
          selectedStepIdx={selectedStepIdx}
          completedStepIdxs={completedStepIdxs}
        />
        {contents}
      </div>
    </PageContent>
  );
}
