import { Loading, Card } from '@/components';
import FingoalResultsDisplay from './FingoalResultsDisplay';
import { FingoalResultsData, RetroResults } from '@/types';

type Props = {
  partner: string;
  resultsData?: RetroResults;
};

export default function ResultsDisplay(props: Props) {
  const { partner, resultsData } = props;

  if (!resultsData) {
    return (
      <Card>
        <Loading />
      </Card>
    );
  }

  switch (partner) {
    case 'fingoal':
      return <FingoalResultsDisplay data={resultsData as FingoalResultsData} />;
    default:
      return null;
  }
}
