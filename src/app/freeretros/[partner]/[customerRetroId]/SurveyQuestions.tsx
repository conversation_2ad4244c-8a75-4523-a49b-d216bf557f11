import { useState } from 'react';
import { Formik, Form } from 'formik';
import { sortBy } from 'lodash';
import { useParams } from 'next/navigation';
import { CustomerRetro, AddSurveyResponsesProps } from '@/types';
import { Button, Card } from '@/components';
import { getSurveyComplete } from './utils';
import { api } from '@/helpers/web';
import { useCustomer } from '@/hooks';
import SurveyQuestion from './SurveyQuestion';
import { genFileTimestamp } from '@/utils/file';

type Props = {
  customerRetro: CustomerRetro;
  onNext: Function;
};

type Params = {
  partner: string;
  customerRetroId: string;
};

export default function SurveyQuestions(props: Props) {
  const { customerRetro, onNext } = props;
  const { customerRetroId, retroName, surveyQuestions } = customerRetro;
  const { partner } = useParams() as Params;
  const { customerKey } = useCustomer();

  const [submitting, setSubmitting] = useState<boolean>(false);

  if (!surveyQuestions?.questions?.length) {
    return;
  }

  const sortedQuestions = sortBy(surveyQuestions.questions, 'ordinal');

  const handleSubmit = async (values: Record<string, any>) => {
    setSubmitting(true);

    const surveyResponsesBody: AddSurveyResponsesProps = {
      customerKey,
      customerRetroId,
      partner,
      surveyResponses: values,
      fileTimestamp: genFileTimestamp(),
    };

    await Promise.all([
      api.post(`/api/free-retros/${partner}/updateCustomerRetroStatus`, {
        customerRetroId,
        status: 'surveyComplete',
      }),
      api.workerPost({
        service: 'retros:addSurveyResponses',
        body: surveyResponsesBody,
        customerKey,
      }),
    ]);

    await onNext();

    setSubmitting(false);
  };

  return (
    <Formik initialValues={{}} onSubmit={handleSubmit}>
      {({ values }) => {
        const complete = getSurveyComplete(sortedQuestions, values);

        return (
          <Form>
            <Card>
              <span className="font-bold text-xl mb-8">{retroName} Survey Questions</span>
              <div className="flex flex-col gap-y-8 w-full">
                {sortedQuestions.map((question, idx) => (
                  <SurveyQuestion question={question} key={`${question.question_text}${idx}`} />
                ))}
                <div className="self-end">
                  <Button
                    label={submitting ? 'Saving...' : 'Submit'}
                    buttonType="submit"
                    disabled={!complete || submitting}
                  />
                </div>
              </div>
            </Card>
          </Form>
        );
      }}
    </Formik>
  );
}
