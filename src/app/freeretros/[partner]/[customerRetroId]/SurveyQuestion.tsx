import { Radio } from 'antd';
import { useFormikContext } from 'formik';
import { RetroSurveyQuestion } from '@/types';
import { InputText } from '@/components';

type Props = {
  question: RetroSurveyQuestion;
};

export default function SurveyQuestion(props: Props) {
  const { question } = props;
  const { question_text, question_type, options } = question;
  const formCtx = useFormikContext();
  const { setFieldValue } = formCtx;
  const values = formCtx.values as Record<string, any>;

  if (question_type === 'input') {
    return (
      <div className="flex flex-col gap-y-2">
        <span className="text-bold text-lg text-zinc-900">{question_text}</span>
        <InputText
          placeholder=""
          icon=""
          onChange={(e) => {
            setFieldValue(question_text, e.target.value);
          }}
          value={values[question_text]}
        />
      </div>
    );
  }

  if (question_type === 'radio') {
    return (
      <div className="flex flex-col gap-y-2">
        <span className="text-bold text-lg text-zinc-900">{question_text}</span>
        <div className="flex items-center">
          <Radio.Group
            onChange={(e) => {
              setFieldValue(question_text, e.target.value);
            }}
            value={values[question_text]}
          >
            <div className="flex gap-x-2">
              {options!.map((option) => (
                <Radio value={option} key={option}>
                  <span className="text-zinc-700">{option}</span>
                </Radio>
              ))}
            </div>
          </Radio.Group>
        </div>
      </div>
    );
  }
}
