import { useEffect, useState, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { message } from 'antd';
import { startCase } from 'lodash';
import { Button, Loading } from '@/components';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useCustomer } from '@/hooks';
import { CustomerRetro } from '@/types';
import { api } from '@/helpers/web';

const PANDA_SESSION_BASE_URL = 'https://app.pandadoc.com/s';

type Props = {
  onNext: Function;
  customerRetro: CustomerRetro;
};

type Params = {
  partner: string;
};

export default function NDAStep(props: Props) {
  const { onNext, customerRetro } = props;
  const { partner } = useParams() as Params;

  const { user } = useUser();
  const { customerId, customerKey } = useCustomer();
  const [messageApi, contextHolder] = message.useMessage();

  const [documentId, setDocumentId] = useState<string>();
  const [documentName, setDocumentName] = useState<string>();
  const [agreementSessionId, setAgreementSessionId] = useState<string>();
  const [signComplete, setSignComplete] = useState<boolean>(false);
  const [docCompleting, setDocCompleting] = useState<boolean>(false);

  const ndaText = (
    <span className="text-zinc-900">
      To access this free {startCase(partner)} retro, we kindly ask you to first review and sign the
      Non-Disclosure Agreement (NDA) using the document embedded below. Your signature ensures
      confidentiality regarding the information shared during this preview.
      <br />
      <br />
      Once signed, this page will update and we will proceed with the free retro by handing off the
      necessary data to {startCase(partner)}.
    </span>
  );

  // fetch session id for embedded signing
  useEffect(() => {
    if (!user) {
      return;
    }

    const getSessionId = async () => {
      const { sessionId, documentId, documentName, documentCompleted } = await api.get(
        `/api/free-retros/${partner}/getPandaAgreementSession`,
        { userEmail: user.email },
      );

      setDocumentId(documentId);
      setDocumentName(documentName);
      setAgreementSessionId(sessionId);
      // If Pandadoc already has a completed document, enable the "Continue" button to commit that completion
      setSignComplete(!!documentCompleted);
    };

    getSessionId();
  }, [user, partner]);

  // add event listener for document completion, enabling continuation
  useEffect(() => {
    const setComplete = (event) => {
      if (event.data?.type === 'session_view.document.completed') {
        setSignComplete(true);
      }
    };

    window.addEventListener('message', setComplete);

    return () => window.removeEventListener('message', setComplete);
  }, []);

  const handleDocCompletion = useCallback(async () => {
    setDocCompleting(true);

    const { success } = await api.post(`/api/free-retros/${partner}/completeAgreementDoc`, {
      partner,
      customerId,
      customerKey,
      documentId,
      documentName,
      retroId: customerRetro.retroId,
    });

    if (success) {
      onNext();
    } else {
      messageApi.open({ type: 'error', content: 'Error signing agreement' });
    }

    setDocCompleting(false);
  }, [
    partner,
    customerId,
    customerKey,
    documentId,
    documentName,
    customerRetro,
    messageApi,
    onNext,
  ]);

  if (!agreementSessionId) {
    return <Loading />;
  }

  return (
    <div className="flex flex-col gap-y-12 w-full">
      {contextHolder}
      <div className="bg-white rounded-xl p-8">
        <div className="flex flex-col w-1/2 gap-y-6">
          <span className="text-zinc-900 text-2xl">Non-Disclosure Agreement (NDA)</span>
          {ndaText}
        </div>
      </div>
      <iframe src={`${PANDA_SESSION_BASE_URL}/${agreementSessionId}`} width="100%" height="600" />
      <div className="w-full mb-12">
        <div className="flex justify-end">
          <Button
            label={docCompleting ? 'Saving...' : 'Continue'}
            disabled={!signComplete || docCompleting}
            onClick={handleDocCompletion}
          />
        </div>
      </div>
    </div>
  );
}
