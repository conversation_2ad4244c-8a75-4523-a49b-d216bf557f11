'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { PageContent, PageTitle, Card, Button, Link, Loading } from '@/components';
import { Play, ChevronLeft } from '@/components/icons';
import { slugify, unSlugify } from '@/utils/string';
import Image from 'next/image';
import { imgSrc, retroImages } from 'lib/source';
import { useCustomer } from '@/hooks';
import { useUser } from '@auth0/nextjs-auth0/client';
import { Retro } from '@/types';
import DataStudyModal from './DataStudyModal';
import { getCurrentRetro, handleCustomerRetro } from './utils';
import SolutionCopy from './SolutionCopy';

type Params = {
  partner: string;
};

export default function PartnerRetroInfo() {
  const { partner } = useParams() as Params;
  const router = useRouter();

  const { user } = useUser();
  const { customerId } = useCustomer();

  const [retro, setRetro] = useState<Retro>();
  const [dataStudyModalOpen, setDataStudyModalOpen] = useState<boolean>(false);
  const [dataStudyPdfData, setDataStudyPdfData] = useState<any>();
  const [customerRetroLoading, setCustomerRetroLoading] = useState<boolean>(false);

  // get current retro
  useEffect(() => {
    if (!customerId) {
      return;
    }

    const getRetro = async () => {
      const currentRetro = await getCurrentRetro({ partner, customerId });

      setRetro(currentRetro);
    };

    getRetro();
  }, [customerId, partner]);

  const handleStartRetro = async () => {
    await handleCustomerRetro({
      retro: retro!,
      partner,
      customerId,
      userName: user!.name as string,
      onLoading: setCustomerRetroLoading,
      router,
    });
  };

  if (!retro) {
    return <Loading />;
  }

  const handleDataStudy = async () => {
    const res = await fetch(`/api/free-retros/${partner}/getDataStudyDoc`);

    const { data } = await res.json();

    setDataStudyPdfData(data);
    setDataStudyModalOpen(true);
  };

  const retroImgConfig = retroImages.getOrDefault(retro.kind);

  return (
    <>
      <PageContent>
        <PageTitle
          subpage
          breadcrumbs={[
            { label: 'Free retros', url: '/freeretros' },
            {
              label: unSlugify(partner),
              url: `/freeretros/${slugify(partner)}`,
            },
          ]}
        />
        <div className="flex flex-col gap-y-10">
          <Card>
            <div className="flex flex-col items-center gap-y-10 w-1/3">
              <Image
                src={imgSrc.getOrDefault(retro.kind).imgPath}
                width={48}
                height={48}
                alt={retro.name}
              />
              <span className="text-5xl text-center">{retro.name} Retro</span>
              <span className="text-zinc-500 text-sm text-center">{retro.description}</span>
              <div className="flex gap-x-8">
                <Button
                  label={customerRetroLoading ? 'Loading...' : 'Start free retro'}
                  Icon={Play}
                  onClick={handleStartRetro}
                />
                <Button
                  type="secondary"
                  label="View data study process"
                  onClick={() => {
                    handleDataStudy();
                  }}
                />
              </div>
            </div>
          </Card>
          <Card>
            <div className="flex">
              <div className="flex flex-col gap-y-4 w-1/2">
                <span className="text-zinc-900 text-2xl">
                  {retro.howItWorksHeader || `How the ${retro.name} Retro works`}
                </span>
                <span className="text-zinc-500 whitespace-pre-wrap">{retro.howItWorks}</span>
              </div>
              <div className="flex w-1/2 justify-end items-center">
                <div
                  className="flex p-6 justify-center items-center rounded-xl"
                  style={retroImgConfig.howItWorks.style.container}
                >
                  <Image
                    src={retroImgConfig.howItWorks.imgPath}
                    alt="Free retros info"
                    {...retroImgConfig.howItWorks.style.image}
                  />
                </div>
              </div>
            </div>
          </Card>
          <Card>
            <div className="flex justify-between w-full">
              <div className="flex flex-col w-1/2">
                <Image
                  src={retroImgConfig.solution.imgPath}
                  alt={retroImgConfig.solution.alt}
                  {...retroImgConfig.solution.style}
                />
                {retroImgConfig.solution2 && (
                  <Image
                    src={retroImgConfig.solution2.imgPath}
                    alt={retroImgConfig.solution2.alt}
                    {...retroImgConfig.solution2.style}
                  />
                )}
              </div>
              <SolutionCopy solutionCopy={retro.solutionCopy} />
            </div>
          </Card>
          {retro.howItWorksDetails && (
            <Card>
              <div className="flex justify-between w-full">
                <div className="flex flex-col w-1/2">
                  <span className="text-zinc-900 text-2xl mb-6">How {retro.name} works</span>
                  <span className="whitespace-pre-wrap">{retro.howItWorksDetails}</span>
                </div>
                <div className="flex flex-col w-1/2 items-end">
                  <Image
                    src={retroImgConfig.howItWorksDetails.imgPath}
                    alt={retroImgConfig.howItWorksDetails.alt}
                    {...retroImgConfig.howItWorksDetails.style}
                  />
                </div>
              </div>
            </Card>
          )}
          <Card>
            <div className="flex justify-between w-full">
              <Link href="/freeretros">
                <div className="flex gap-x-4">
                  <ChevronLeft className="stroke-inherit" />
                  <span>Back to all retros</span>
                </div>
              </Link>
              <Button
                label={customerRetroLoading ? 'Loading...' : 'Start free study'}
                Icon={Play}
                onClick={handleStartRetro}
              />
            </div>
          </Card>
        </div>
      </PageContent>
      {dataStudyModalOpen && (
        <DataStudyModal
          isOpen={dataStudyModalOpen}
          onOpenChange={setDataStudyModalOpen}
          pdfData={dataStudyPdfData}
          docTitle={`${retro.name} Data Study Process`}
        />
      )}
    </>
  );
}
