'use client';
import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';

// export const metadata = {
//   title: 'Announcements',
//   description: 'find out about new features',
// }

const menuItems = [
  ['Most Recent', '@danya'],
  ['<PERSON>sama', '@osama'],
  ['<PERSON>yan', '@loyan'],
  ['Carl<PERSON>', '@carllose'],
  ['Micheal', '@micheal'],
];

export default function Announcements() {
  const [selectedItem, setSelectedItem] = useState({
    item: menuItems[0],
    idx: 0,
  });
  const [state, setState] = useState(false);
  const selectMenuRef = useRef<any>();

  useEffect(() => {
    const handleSelectMenu = (e) => {
      if (!selectMenuRef.current?.contains(e.target)) {
        setState(false);
      }
    };
    document.addEventListener('click', handleSelectMenu);
  }, []);

  const navigation = [
    {
      href: 'javascript:void(0)',
      name: 'View all',
    },
    {
      href: 'javascript:void(0)',
      name: 'Product',
    },
    {
      href: 'javascript:void(0)',
      name: 'Industry',
    },
    {
      href: 'javascript:void(0)',
      name: 'Legal',
    },
    {
      href: 'javascript:void(0)',
      name: 'Customer Success',
    },
  ];

  const posts = [
    {
      title: 'What is SaaS? Software as a Service Explained',
      desc: 'Going into this journey, I had a standard therapy regimen, based on looking at the research literature. After I saw the movie, I started to ask other people what they did for their anxiety, and some',
      img: 'https://images.unsplash.com/photo-1556155092-490a1ba16284?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80',
      authorLogo: 'https://api.uifaces.co/our-content/donated/xZ4wg2Xj.jpg',
      authorName: 'Sidi dev',
      category: 'Industry',
      date: 'Jan 4 2022',
      href: '/',
    },
    {
      title: 'A Quick Guide to WordPress Hosting',
      desc: "According to him, â€œI'm still surprised that this has happened. But we are surprised because we are so surprised.â€More revelations about Whittington will be featured in the film",
      img: 'https://images.unsplash.com/photo-1620287341056-49a2f1ab2fdc?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80',
      authorLogo: 'https://api.uifaces.co/our-content/donated/FJkauyEa.jpg',
      authorName: 'Micheal',
      category: 'Product',
      date: 'Jan 4 2022',
      href: '/',
    },
    {
      title: '7 Promising VS Code Extensions Introduced in 2022',
      desc: "I hope I remembered all the stuff that they needed to know. They're like, 'okay,' and write it in their little reading notebooks. I realized today that I have all this stuff that",
      img: 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80',
      authorLogo: 'https://randomuser.me/api/portraits/men/46.jpg',
      authorName: 'Luis',
      category: 'Industry',
      date: 'Jan 4 2022',
      href: '/',
    },
    {
      title: 'How to Use Root C++ Interpreter Shell to Write C++ Programs',
      desc: "The powerful gravity waves resulting from the impact of the planets' moons â€” four in total â€” were finally resolved in 2015 when gravitational microlensing was used to observe the",
      img: 'https://images.unsplash.com/photo-1617529497471-9218633199c0?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80',
      authorLogo: 'https://api.uifaces.co/our-content/donated/KtCFjlD4.jpg',
      authorName: 'Lourin',
      category: 'Customer Success',
      date: 'Jan 4 2022',
      href: '/',
    },
  ];

  return (
    <>
      <div className="items-start justify-between gap-x-4 py-4 sm:flex">
        <div className="max-w-lg">
          <h3 className="text-gray-800 text-2xl font-bold">Announcements</h3>
          {/* <p className="text-gray-600 mt-2">
                        Lorem Ipsum text of the printing and typesetting industry.
                    </p> */}
        </div>
        <div className="mt-6 sm:mt-0">
          {/* <div className="form-control">
  <div className="input-group">
    <input type="text" placeholder="Search…" className="input input-sm input-bordered" />
    <button className="btn btn-sm btn-square">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
    </button>
  </div>
</div> */}
          <form onSubmit={(e) => e.preventDefault()} className="max-w-sm px-4 mx-auto mt-0">
            <div className="relative">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="absolute top-0 bottom-0 w-6 h-6 my-auto text-gray-400 left-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <input
                type="text"
                placeholder="Search"
                className="w-full input-sm py-3 pl-12 pr-4 text-gray-500 border rounded-md outline-none bg-gray-50 focus:bg-white focus:border-indigo-600"
              />
            </div>
          </form>
        </div>
      </div>
      <div className="flex mt-6">
        <ul className="w-full border-b flex items-center gap-x-3 overflow-x-auto">
          {navigation.map((item, idx) => (
            // Replace [idx == 0] with [window.location.pathname == item.path] or create your own logic
            <li
              key={idx}
              className={`py-2 border-b-2 ${
                idx == 0 ? 'border-indigo-600 text-indigo-600' : 'border-white text-gray-500'
              }`}
            >
              <Link
                href={item.href}
                className="py-2.5 px-4 rounded-lg duration-150 text-sm hover:text-indigo-600 hover:bg-gray-50 active:bg-gray-100 font-medium"
              >
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
        <div className="relative w-1/4 px-4 text-base">
          <button
            ref={selectMenuRef}
            className="flex items-center gap-2 w-full px-3 py-2 text-gray-500 bg-white border rounded-md shadow-sm cursor-default outline-none focus:border-indigo-600"
            aria-haspopup="true"
            aria-expanded="true"
            aria-labelledby="listbox-label"
            onClick={() => setState(!state)}
          >
            <div className="flex-1 text-left text-xs flex items-center gap-x-1">
              {selectedItem.item[0]}
            </div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 9l4-4 4 4m0 6l-4 4-4-4"
              />
            </svg>
          </button>

          {state ? (
            <div className="relative w-full">
              <ul
                className="absolute w-full mt-3 overflow-y-auto bg-white border rounded-md shadow-sm max-h-64"
                role="listbox"
              >
                {menuItems.map((el, idx) => (
                  <li
                    key={idx}
                    onClick={() =>
                      setSelectedItem({
                        item: el,
                        idx,
                      })
                    }
                    role="option"
                    aria-selected={selectedItem.idx == idx ? true : false}
                    className={`${
                      selectedItem.idx == idx ? 'text-indigo-600 bg-indigo-50' : ''
                    } flex items-center justify-between gap-2 px-3 cursor-default py-2 duration-150 text-gray-500 hover:text-indigo-600 hover:bg-indigo-50`}
                  >
                    <div className="flex-1 text-left flex items-center gap-x-1">{el[0]}</div>
                    {selectedItem.idx == idx ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-5 h-5 text-indigo-600"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      ''
                    )}
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            ''
          )}
        </div>
      </div>
      <section className="mt-12 mx-auto px-2 max-w-screen-xl md:px-4">
        <div className="mt-12 grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
          {posts.map((items, key) => (
            <article
              className="max-w-md mx-auto mt-4 shadow-lg border rounded-md duration-300 hover:shadow-sm"
              key={key}
            >
              <Link href={items.href}>
                <Image
                  src={items.img}
                  loading="lazy"
                  alt={items.title}
                  className="w-full h-48 rounded-t-md"
                />
                <div className="flex items-center mt-2 pt-3 ml-4 mr-2">
                  {/* <div className="flex-none w-10 h-10 rounded-full">
                                        <img src={items.authorLogo} className="w-full h-full rounded-full" alt={items.authorName} />
                                    </div>
                                    <div className="ml-3">
                                        <span className="block text-gray-900">{items.authorName}</span>
                                        <span className="block text-gray-400 text-sm">{items.date}</span>
                                    </div> */}
                  <span className="block text-gray-400 text-sm">{items.category}</span>
                </div>
                <div className="pt-3 ml-4 mr-2 mb-3">
                  <h3 className="font-sans text-md text-gray-900">{items.title}</h3>
                  <p className="text-gray-400 text-sm mt-1 antialiased">{items.desc}</p>
                </div>
              </Link>
            </article>
          ))}
        </div>
      </section>
      <div className="max-w-xs gap-2 grid grid-cols-2 float-right mb-8">
        <button className="btn btn-sm btn-outline btn-ghost">Previous</button>
        <button className="btn btn-sm btn-outline btn-ghost">Next</button>
      </div>
    </>
  );
}
