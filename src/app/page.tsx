'use client';

import { useEffect, useState } from 'react';
import Dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import LocalizedFormat from 'dayjs/plugin/localizedFormat';
import duration from 'dayjs/plugin/duration';
import { useCustomer } from '@/hooks';
import { DataSource } from '@/types';
import DataSources from './homepage/DataSources';
import { api } from '@/helpers/web';

Dayjs.extend(relativeTime);
Dayjs.extend(utc);
Dayjs.extend(LocalizedFormat);
Dayjs.extend(duration);

export default function Home() {
  const { customerId } = useCustomer();
  const [dataSources, setDataSources] = useState<DataSource[]>();
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    if (!customerId) {
      return;
    }

    const getSource = async () => {
      setLoading(true);

      const dataSources = await api.get('/api/dataSources/getCustomerDataSources', { customerId });

      setDataSources(dataSources);

      setLoading(false);
    };

    getSource();
  }, [customerId]);

  return (
    <div className="w-full max-w-full h-full min-h-full flex flex-col p-4 overflow-clip">
      <header>
        <div className="relative flex justify-between items-center w-full px-6 py-8">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Data Sources</h1>
          </div>
        </div>
      </header>
      <DataSources loading={loading} dataSources={dataSources} />
    </div>
  );
}
