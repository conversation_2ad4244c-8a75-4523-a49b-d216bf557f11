'use client';

import { ReactNode, useEffect, useRef } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import {
  useUserStore,
  useDictionaryStore,
  useDataSourceStore,
  useCustomerStore,
  useFeatureFlagsStore,
} from '@/stores';
import { api } from '@/helpers/web';

type Props = {
  children: ReactNode;
};

export default function InitProvider(props: Props) {
  const { children } = props;

  const { user, isLoading } = useUser();
  const { fetchUserInfo } = useUserStore();
  const { fetchTargetModels, initializeSourceModels } = useDictionaryStore();
  const { setDataSources } = useDataSourceStore();
  const { setCustomer } = useCustomerStore();
  const { setFeatureFlags } = useFeatureFlagsStore();

  const hasInitialized = useRef(false);

  useEffect(() => {
    if (isLoading || !user) {
      return;
    }

    // prevent unnecessary re-init
    if (hasInitialized.current) {
      return;
    }

    hasInitialized.current = true;

    // Fetch initialization data in order of priority. Essential data is loaded first, blocking
    // additional requests until it completes to avoid overloading the server. Less essential data will load in the background.
    const initialize = async () => {
      // First, initialize feature flags, customer info, and data sources in one call based on auth id. We always
      // want this dataset loaded in memory, regardless of where you are in the app.
      const { featureFlagsConfig, customer, dataSources } = await api.get(
        '/api/customer/customerInit',
        {
          authOrgId: user.org_id,
        },
      );

      setCustomer(customer);
      setDataSources(dataSources);
      setFeatureFlags(featureFlagsConfig, user);

      const { customerId, customerKey } = customer;

      // Then, fetch data dictionaries, which the current page may or may not depend on. If you're on a page that
      // doesn't require dictionary info, this will not block page render.
      await Promise.all([
        fetchTargetModels({ customerKey, customerId }),
        initializeSourceModels({ customerId }),
      ]);

      // Fetch user info last – this is role info that determines access to certain pages and doesn't matter for typical users. E.g. isAdmin will resolve
      // to false, so the initial non-admin UI won't need to update.
      await fetchUserInfo();
    };

    initialize();
  }, [
    isLoading,
    user,
    setCustomer,
    setDataSources,
    setFeatureFlags,
    fetchUserInfo,
    fetchTargetModels,
    initializeSourceModels,
  ]);

  return <>{children}</>;
}
