import Vorpal from 'vorpal';
import { loadService } from 'modules/service';
import App from 'modules/app';
import { GenericJob } from '@/jobs';

const vorpal = new Vorpal();

// e.g. run dataSources:updateNewConnector --customerId <customerId> etc
vorpal
  .command('run <service>', 'Executes a service')
  .allowUnknownOptions()
  .action(async (args) => {
    try {
      const app = new App();

      await app.init();

      const service = await loadService(args.service);

      const result = await app.execute(async (ctx) => service(args.options, ctx));

      vorpal.log(result);
    } catch (error: any) {
      vorpal.log(error.message);
    }
  });

vorpal
  .command('queue <service>', 'Adds job to the client queue')
  .allowUnknownOptions()
  .action(async (args) => {
    try {
      const app = new App();

      await app.init();

      await app.execute(async (ctx) => {
        const job = new GenericJob(
          { queueName: 'client', service: args.service, props: args.options },
          ctx,
        );

        await job.enqueue();
      });
    } catch (error: any) {
      vorpal.log(error.message);
    }
  });

// kick off the CLI
vorpal.delimiter('gestalt-cli ->').show();
