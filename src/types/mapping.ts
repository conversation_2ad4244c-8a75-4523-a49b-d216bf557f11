import { ReactElement } from 'react';
import { TargetDataSource, MappingDictionaryField, SourceModel } from './models';
import { Node, Edge } from '@xyflow/react';
import { Option } from '.';

export type MatchReviewStatus = 'accepted' | 'rejected' | 'none';

export type MappingMatch = {
  source_model_id: string;
  source_model_name: string;
  source_model_dbt_name?: string;
  source_field_id: string;
  source_field: string;
  source_type: string;
  target_model_id: string;
  target_model_name: string;
  target_field_id: string;
  target_field: string;
  target_type: string;
  confidence: number;
  review_status?: MatchReviewStatus;
};

export type Mapping = {
  source_model_id: string;
  target_model_id: string;
  source_field: string;
  source_fields?: string[] | null;
  source_field_type?: string | null;
  target_field_type?: string | null;
  target_field_format?: string | null;
  target_gen_field_type?: string | null;
  target_field: string;
  source_model: string;
  target_model: string;
  source_name?: string;
  is_match?: boolean;
  prev_target_sources?: TargetDataSource[];
  delete?: boolean;
  operation?: string;
  on_sql?: string;
};

export type MapperStatus = 'processing' | 'success' | 'error' | 'initial';

export type NodeSelectOptions = {
  nodeId: string;
  selected: boolean;
  currentNodes: Node<BaseNodeProps>[];
  currentEdges: Edge[];
};

export type NodeSelectFunction = (e: any, opts: NodeSelectOptions) => void;

type BaseNodeValue = {
  nodeKind: NodeKind;
};

export interface MappingNodeValue extends BaseNodeValue {
  nodeKind: NodeKind;
  field: MappingDictionaryField;
  sourceModelKey: string;
  sourceModelName?: string;
}

export interface StaticNodeValue extends BaseNodeValue {
  dataType?: string;
  value?: any;
}

type SourceFieldOptions = {
  type: 'order';
  label: string;
};

type DropdownOptions = {
  label: string;
  opts: Option[];
  optKey: string;
};

type TransformSubConfig = {
  sourceFieldMin?: number;
  sourceFieldMax?: number;
  sourceFieldOpts?: SourceFieldOptions;
  dropdownOpts?: DropdownOptions;
  defaultOpts?: TransformOptions;
};

export type TransformConfig = {
  key: string;
  label: string;
  Icon: (props: any) => ReactElement;
  config?: TransformSubConfig;
};

export type ConditionalPath = {
  pathId: string;
  sourceNodeId: string | null;
  operator: string | null;
  comparisonNodeId: string | null;
  resultNodeId: string | null;
};

export type TransformOptions = {
  // order of source node ids, used for transforms where order is important
  sourceNodeOrder?: string[];
  // separator for concatenation
  sourceSeparator?: string;
  mathOperator?: string;
  targetDataType?: string;
  targetCase?: string;
  conditionalPaths?: ConditionalPath[];
  // paths are draggable in the UI, so we maintain the order separately
  conditionalPathOrder?: string[];
};

export interface TransformNodeValue extends BaseNodeValue {
  transformKey: string;
  opts?: TransformOptions;
}

export type BaseNodeProps = {
  value: BaseNodeValue;
};

export type MappingNodeProps = {
  value: MappingNodeValue;
};

export type TransformNodeProps = {
  value: TransformNodeValue;
};

export type StaticNodeProps = {
  value: StaticNodeValue;
};

export type GeneralNode = Node<MappingNodeProps | TransformNodeProps | StaticNodeProps>;
export type TransformNode = Node<TransformNodeProps>;
export type MappingNode = Node<MappingNodeProps>;
export type StaticNode = Node<StaticNodeProps>;

export type EdgeStyleProps = {
  type: 'straight' | 'bezier' | 'step';
  // inline css style
  style?: Record<string, any>;
};

export type NodeKind = 'source' | 'target' | 'transform' | 'static';

export type MappingDeliveryNodeData = {
  field: MappingDictionaryField;
  sourceModelKey: string;
};

export type TransformDeliveryNodeData = {
  transformKey: string;
  opts: TransformOptions;
};

export type StaticDeliveryNodeData = {
  dataType: string;
  value: any;
};

export type DeliveryNodeData =
  | MappingDeliveryNodeData
  | TransformDeliveryNodeData
  | StaticDeliveryNodeData;

// configured nodes and edges that are sent to the worker for manipulation and building the contract.
// We can remove all client-specific properties.
export type DeliveryNode = {
  id: string;
  nodeKind: NodeKind;
  data: DeliveryNodeData;
};

export type DeliveryEdge = {
  id: string;
  source: string;
  target: string;
};

export type ModelJoin = {
  id?: string;
  order?: number;
  sourceModelName?: string;
  sourceModelId?: string;
  sourceModelDbtName?: string;
  sourceModelField?: string;
  joinModelName?: string;
  joinModelId?: string;
  joinModelDbtName?: string;
  joinModelField?: string;
};

export type ModelJoinOptionValue = Omit<SourceModel, 'dictionary'>;

export type JoinType = 'inner' | 'left' | 'right';

export type Operator = '=' | '>' | '<' | '>=' | '<=';

export type FilterCombinator = 'and' | 'or';

export type FilterRule = {
  model_ordinal: number;
  model_alias?: string;
  field_name?: string;
  value?: any;
  operator?: Operator;
  rules?: FilterRule[];
};

export type FilterCondition = {
  combinator: FilterCombinator;
  not: boolean;
  rules: FilterRule;
};

export type MappingSourceType = 'transform_output' | 'source_field';

export type OperationType =
  | '+'
  | '-'
  | '/'
  | '*'
  | 'coalesce'
  | 'cast'
  | 'uuid'
  | 'concat'
  | 'case'
  | 'static';

export type TransformProperties = {
  separator?: string;
  target_type?: string;
  target_case?: string;
  static_type?: string;
  static_value?: any;
  on_sql?: string;
};

export type ContractMappingOperation = {
  type: OperationType;
  properties: TransformProperties | null;
};

export type MappingSource = {
  type: MappingSourceType;
  model_ordinal: number | null;
  model_alias: string | null;
  field_name: string | null;
  operation: ContractMappingOperation | null;
  sources: MappingSource[] | null;
};

export type ContractMappingType = 'copy' | 'transform';

export type ContractMapping = {
  target_field: string;
  mapping_type: ContractMappingType;
  on_sql?: string;
  sources: MappingSource[];
};
