import { ContractMapping } from './mapping';

export type ModelMapping = {
  operation?: string;
  source_field?: string;
  source_fields?: string[];
  target_field: string;
  cast?: string;
  type?: string;
  value?: string;
  delete?: boolean;
  onsql?: string;
};

export type TargetModelMappingRow = {
  target_model_id: string;
  target_model_name: string;
  source_name: string;
  source_model_name: string;
  mapping: ModelMapping;
};

type SourceTableMapping = {
  name: string;
  join_id?: string;
  person_info?: string;
  person_join?: boolean;
  person_type?: string;
  mappings: ModelMapping[];
};

export type SourceTransformation = {
  combination?: string;
  primary_key?: string;
  record_strategy?: string;
  restrict?: string[];
  source_strategy?: string;
  sources: SourceTableMapping[];
};

export type ModelType = 'core' | 'consumption' | 'metrics';

export type BaseModel = {
  id: string;
  name: string;
  version?: string;
  fieldCount: number;
  mappingCount: number;
};

export type MappingSourceField = {
  id_source_model: string;
  text_source_field: string;
  text_source_model_alias: string;
  text_source_model_dbt_name: string;
  text_source_model_name: string;
  text_source_model_origin: string;
  type_field_source: string;
};

export type SourceFieldMapping = {
  id_target_model: string;
  target_model_name: string;
  contract_mapping: ContractMapping;
};

export type BaseDictionaryField = {
  field_name: string;
  field_description?: string;
  field_ordinal?: number;
  field_type: string;
  gen_field_type: string;
};

export interface TargetModelDictionaryField extends BaseDictionaryField {
  mapping: ContractMapping | null;
  source_fields?: MappingSourceField[];
}

export interface SourceModelDictionaryField extends BaseDictionaryField {
  mappings?: SourceFieldMapping[];
}

export interface TargetModel extends BaseModel {
  description: string;
  type: ModelType;
  dictionary: TargetModelDictionaryField[];
  updatedAt: string;
  rowCount: number | null;
}

export interface SourceModel extends BaseModel {
  dbtName: string;
  origin: string;
  dictionary: SourceModelDictionaryField[];
}

export type FeatureModelMapping = {
  id_target_model: string;
  id_source_model: string;
  timestamp_modified: string;
  type_financial_product: string;
  text_target_model_name: string;
  text_source_model_name: string;
  flag_matched: boolean;
  deleted_at: string;
};

export type ModelMatch = {
  sourceModel: SourceModel;
  targetModel: TargetModel;
  existing?: boolean;
  delete?: boolean;
};

export type SaveModelMatch = {
  sourceModelId: string;
  targetModelId: string;
  existing?: boolean;
  delete?: boolean;
};

export type TargetDataSource = {
  connectorName: string;
  sourceTableName: string;
  sourceStrategy?: string;
  mappings: ModelMapping[];
};

export interface MappingDictionaryField extends BaseDictionaryField {
  model_id: string;
  model_name: string;
  dbt_name?: string;
}

export type FieldKind = 'source' | 'target';

export type CustomField = {
  id_custom_field: string;
  id_business_key: string;
  text_name: string;
  type_custom_field: string;
  type_entity: string;
};

export type ModelQueryOptions = {
  includeUnmapped?: boolean;
};
