import { Mapping, ContractMapping } from './mapping';

export type ModelMapping = {
  operation?: string;
  source_field?: string;
  source_fields?: string[];
  target_field: string;
  cast?: string;
  type?: string;
  value?: string;
  delete?: boolean;
  onsql?: string;
};

export type TargetModelMappingRow = {
  target_model_id: string;
  target_model_name: string;
  source_name: string;
  source_model_name: string;
  mapping: ModelMapping;
};

type SourceTableMapping = {
  name: string;
  join_id?: string;
  person_info?: string;
  person_join?: boolean;
  person_type?: string;
  mappings: ModelMapping[];
};

export type SourceTransformation = {
  combination?: string;
  primary_key?: string;
  record_strategy?: string;
  restrict?: string[];
  source_strategy?: string;
  sources: SourceTableMapping[];
};

export type ModelType = 'core' | 'consumption' | 'metrics';

export type MappingSourceField = {
  id_source_model: string;
  text_source_field: string;
  text_source_model_alias: string;
  text_source_model_dbt_name: string;
  text_source_model_name: string;
  text_source_model_origin: string;
  type_field_source: string;
};

export type ModelDictionaryField = {
  field_description: string;
  field_name: string;
  field_ordinal: number;
  field_type: string;
  gen_field_type: string;
  target_field: string;
  mapping: ContractMapping | null;
  source_fields?: MappingSourceField[];
};

export type Model = {
  id: string;
  name: string;
  dbtName?: string;
  description?: string;
  type?: ModelType;
  origin?: string;
  version?: string;
  dictionary: ModelDictionaryField[];
  fieldCount?: number;
  rowCount?: number;
  mappingCount?: number;
};

export type ListModel = {
  id: string;
  name: string;
};

export type ModelSummary = Pick<
  Model,
  'id' | 'name' | 'type' | 'description' | 'fieldCount' | 'mappingCount' | 'rowCount'
>;

// subset of model type returned from generating models from smart mapper matches.
// Used for merging with existing target models
// export type MappingModel = Pick<Model, 'id' | 'transformations'>;

export type FeatureModelMapping = {
  id_target_model: string;
  id_source_model: string;
  timestamp_modified: string;
  type_financial_product: string;
  text_target_model_name: string;
  text_source_model_name: string;
  flag_matched: boolean;
  deleted_at: string;
};

export type ModelMatch = {
  sourceModel: Model;
  targetModel: Model;
  existing?: boolean;
  delete?: boolean;
};

export type SaveModelMatch = {
  sourceModelId: string;
  targetModelId: string;
  existing?: boolean;
  delete?: boolean;
};

export type TargetDataSource = {
  connectorName: string;
  sourceTableName: string;
  sourceStrategy?: string;
  mappings: ModelMapping[];
};

export type BaseField = {
  model_id: string;
  model_name: string;
  dbt_name?: string;
  field_name: string;
  field_ordinal?: number;
  field_type: string;
  field_description: string;
  field_format?: string;
  gen_field_type?: string;
};

export type FieldKind = 'source' | 'target';

// type returned from target_data_dictionary
export type TargetField = BaseField & {
  sources?: TargetDataSource[];
};

// source_data_dictionary, includes name for source type, like filemaker_olh
export type SourceField = BaseField & {
  name: string;
  mappings?: Mapping[];
};

export type ModelSourceFields = {
  modelName: string;
  modelId: string;
  dbtName: string;
  fields: SourceField[];
};

export type SourceFieldsByModel = ModelSourceFields[];

export type CustomField = {
  id_custom_field: string;
  id_business_key: string;
  text_name: string;
  type_custom_field: string;
  type_entity: string;
};

export type ModelQueryOptions = {
  includeUnmapped?: boolean;
};
