export type Auth0User = {
  given_name: string;
  family_name: string;
  nickname: string;
  name: string;
  picture: string;
  updated_at: string;
  email: string;
  email_verified: boolean;
  sub: string;
  sid: string;
  org_id: string;
  org_name: string;
};

export type UserRole = {
  id: string;
  name: string;
  description: string;
};

export type User = Auth0User & {
  roles: UserRole[];
  isAdmin: boolean;
};
