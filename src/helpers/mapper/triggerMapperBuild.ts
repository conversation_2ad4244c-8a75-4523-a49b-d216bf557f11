import { Context } from '@/types';

type Props = {
  dataSourceKey: string;
  customerDataSourceId: string;
};

// const sqlText = `
//     update config.customer_data_sources
//     set mapping_status = :1
//     where id = :2
// `;

export default async function triggerMapperBuild(props: Props, ctx: Context) {
  const { dataSourceKey } = props;
  const { mapper } = ctx;

  await Promise.all([
    mapper.build({ dataSourceKey }),
    // TO DO: hook up with mapper api to check build status and keep track of that in our mapping_status column
    // db.execute({ sqlText, binds: ['pending', customerDataSourceId] }),
  ]);
}
