import { Context, TargetField } from '@/types';
import { getNormalizedFieldType } from '@/utils/data';

type Props = {
  modelId: string;
  version?: string;
};

const sqlText = `
    select
    tmv.id_model as "model_id"
    , tm.text_name as "model_name"
    , lf.value:field_name::STRING as "field_name"
    , lf.value:field_ordinal::INTEGER as "field_ordinal"
    , lf.value:field_type::STRING as "field_type"
    , lf.value:field_description::STRING as "field_description"
    from gestalt_applications_db.model_repository.target_models_versions tmv
    inner join gestalt_applications_db.model_repository.target_models tm
    on tmv.id_model = tm.id_model
    , lateral flatten(input => tmv.var_metadata:shape:standardized:fields) as lf
    where tmv.id_model = :1
    and tmv.text_version = :2
  `;

export default async function getTargetDataDictionary(
  props: Props,
  ctx: Context,
): Promise<TargetField[]> {
  const { modelId, version = '1.0.0' } = props;
  const { db } = ctx;

  const fields = await db.execute({
    sqlText,
    binds: [modelId, version],
  });

  return fields.map((field) => ({
    ...field,
    gen_field_type: getNormalizedFieldType(field.field_type),
  }));
}
