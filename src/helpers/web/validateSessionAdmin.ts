import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '@auth0/nextjs-auth0';
import { Context } from '@/types';
import { invariant } from '@/utils/error';

type Props = {
  req: NextApiRequest;
  res: NextApiResponse;
};

// This helper should run in the API layer for any client-triggered server actions that require the admin role. It
// behaves more like middleware – throw an error if the user is not an admin, pass if they are.
// TO DO: move to actual middleware?
export default async function validateSessionAdmin(props: Props, ctx: Context): Promise<void> {
  const { req, res } = props;
  const { auth } = ctx;

  const session = await getSession(req, res);

  invariant(!!session, 'No session defined');

  const {
    user: { sub },
  } = session!;

  // After retrieving the user's session, we query the management API for the user's roles to check if they are an admin.
  // If not, throw an error to prevent non-admin users from doing admin stuff.
  const isAdmin = await auth.validateAdmin(sub);

  invariant(isAdmin, `User ${sub} is not an administrator`);
}
