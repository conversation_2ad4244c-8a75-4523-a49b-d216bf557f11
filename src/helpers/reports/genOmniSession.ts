import { Context } from '@/types';
import { invariant } from '@/utils/error';

type Props = {
  orgName: string;
  userId: string;
  userName: string;
  omniOrgName?: string;
  omniSecretPath?: string;
  omniConnectionId: string;
};

// returns embed url for session
export default async function genOmniSession(props: Props, ctx: Context): Promise<string> {
  const {
    orgName,
    userId,
    userName,
    omniOrgName,
    omniSecretPath = 'OMNI_SECRET',
    omniConnectionId,
  } = props;

  const { auth, reports } = ctx;

  invariant(!!omniConnectionId, 'No connection id defined for application-mode embed');

  // Pass auth0 user app_metadata to determine JIT permissions.
  const { app_metadata } = await auth.getUser(userId);
  const { omni_connection_role, omni_content_role, omni_content_groups } = app_metadata || {};

  return await reports.genApplicationEmbed({
    orgName,
    userId,
    userName,
    omniOrgName,
    omniSecretPath,
    omniConnectionId: omniConnectionId!,
    connectionRole: omni_connection_role,
    contentRole: omni_content_role,
    contentGroups: omni_content_groups,
  });
}
