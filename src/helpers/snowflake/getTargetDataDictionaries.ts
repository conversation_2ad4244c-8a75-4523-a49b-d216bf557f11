import { sortBy } from 'lodash';
import { Context, TargetModel } from '@/types';
import { findGeneralType } from '@/utils/data';

type Props = {
  customerId: string;
  customerKey: string;
  modelVersion?: string;
};

const sqlText = `
  select
    src.id_model as "id"
    , src.text_name as "name"
    , src.text_description as "description"
    , src.type_model as "type"
    , src.var_dictionary as "dictionary"
    , src.num_field_count as "fieldCount"
    , src.num_mapping_count as "mappingCount"
    , src_j_1.row_count as "rowCount"
  from config.customer_target_models_dictionary as src
  inner join identifier(:1) as src_j_1
  on src.text_name = src_j_1.table_name
  where src.id_customer = :2
  and src.text_version = :3
  and src_j_1.table_schema = 'GRAND_VAULT'
`;

export default async function getTargetDataDictionaries(
  props: Props,
  ctx: Context,
): Promise<TargetModel[]> {
  const { customerId, customerKey, modelVersion = '1.0.0' } = props;
  const { db } = ctx;

  const tablesTable = `${customerKey}_db.information_schema.tables`;

  const models = await db.execute({
    sqlText,
    binds: [tablesTable, customerId, modelVersion],
  });

  const enhanced = models.map((model) => ({
    ...model,
    description: model.description || `A Gestalt model for ${model.name.toLowerCase()}.`,
    dictionary: model.dictionary.map((field: any) => ({
      ...field,
      gen_field_type: findGeneralType(field.field_type),
    })),
  }));

  return sortBy(enhanced, 'name');
}
