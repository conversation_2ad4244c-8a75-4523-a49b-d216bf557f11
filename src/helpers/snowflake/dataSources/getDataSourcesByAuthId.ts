import { DataSource, Context } from '@/types';

type Props = {
  authOrgId: string;
};

const sqlText = `
    select
        src.id as "id"
        , src.customer_id as "customer_id"
        , src.connector_id as "connector_id"
        , src.display_name as "display_name"
        , src.source_model_key as "source_model_key"
        , src.original_source_key as "original_source_key"
        , src.mapping_status as "mapping_status"
        , src.config as "config"
        , src_j_2.connector_id as "connector_slug"
        , src_j_2.connector_type as "connector_type"
        , src_j_2.display_name as "connector_display_name"
        , src_j_2.status as "connector_status"
        , src_j_2.sync_frequency as "sync_frequency"
        , src_j_2.last_file_received as "connector_last_file_received"
        , src_j_2.last_executed_at as "connector_executed_at"
        , src_j_3.source_record_name as "source_record_name"
        , src_j_3.source_table_name as "source_table_name"
    from config.customer_data_sources as src
    inner join config.customers as src_j_1
    on src.customer_id = src_j_1.id
    left join fivetran.connectors as src_j_2
    on src.connector_id = src_j_2.id
    left join config.generic_source_config as src_j_3
    on src.config_id = src_j_3.id
    where src_j_1.auth_id = :1
    order by iff(src_j_2.connector_type = 'custom' or src_j_2.connector_type is null, 1, 0)
    , lower(src.display_name)
`;

export default async function getDataSourcesByAuthId(
  props: Props,
  ctx: Context,
): Promise<DataSource[]> {
  const { authOrgId } = props;
  const { db } = ctx;

  const dataSources = await db.execute({
    sqlText,
    binds: [authOrgId],
  });

  return dataSources.map((source) => ({
    id: source.id,
    customerId: source.customer_id,
    displayName: source.display_name,
    sourceModelKey: source.source_model_key,
    originalSourceKey: source.original_source_key,
    mappingStatus: source.mapping_status,
    connector: {
      id: source.connector_id,
      connector_id: source.connector_slug,
      connector_type: source.connector_type,
      display_name: source.connector_display_name,
      status: source.connector_status,
      sync_frequency: source.sync_frequency,
      last_file_received: source.connector_last_file_received,
      executed_at: source.connector_executed_at,
    },
    config: {
      sourceRecordName: source.source_record_name,
      sourceTableName: source.source_table_name,
    },
    dataSourceConfig: source.config,
  }));
}
