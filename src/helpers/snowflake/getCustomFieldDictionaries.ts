import { Context, TargetModelDictionaryField } from '@/types';

type Props = {
  customerId: string;
  customerKey: string;
};

type ResultModel = {
  id: string;
  dictionary: TargetModelDictionaryField[];
};

const sqlText = `
    select
        src.data:model_id::STRING as "id"
        , array_agg(
            object_construct(
                'field_name', src_j_1.text_qualified_name
                , 'field_type', 'CUSTOM'
                , 'field_description', 'Custom field added by user'
            )
        ) as "dictionary"
    from config.customer_requests as src
    inner join identifier(:1) as src_j_1
    on src.data:id_custom_field::STRING = src_j_1.id_custom_field
    where src.customer_id = :2
    and src.kind = 'custom_field'
    and src.status = 'completed'
    group by 1
`;

export default async function getCustomFieldDictionaries(
  props: Props,
  ctx: Context,
): Promise<ResultModel[]> {
  const { customerId, customerKey } = props;
  const { db } = ctx;

  try {
    const customFieldsLookupRef = `${customerKey}_db.grand_vault.lookup_custom_fields`;

    return await db.execute({ sqlText, binds: [customFieldsLookupRef, customerId] });
  } catch (error: any) {
    console.error({ error, stack: error.stack });

    // Not all customers will have a custom fields lookup model – skip if not configured
    return [];
  }
}
