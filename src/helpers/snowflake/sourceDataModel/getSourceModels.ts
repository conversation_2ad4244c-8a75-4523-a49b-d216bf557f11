import { Model, Context } from '@/types';

type Props = {
  sourceModelKey: string;
  modelVersion?: string;
};

const sqlText = `
    select
      m.id as "id"
      , m.name as "name"
      , m.origin as "origin"
      , m.version as "version"
      , m.schema as "schema"
      , m.transformations as "transformations"
      , m.metadata as "metadata"
    from dynamic_transformation.config.models m
    where lower(m.origin) = lower(:1)
    and m.version = :2
    order by m.name
`;

export default async function getSourceModels(props: Props, ctx: Context): Promise<Model[]> {
  const { sourceModelKey, modelVersion = '1.00.0000' } = props;
  const { db } = ctx;

  try {
    return await db.execute({
      sqlText,
      binds: [sourceModelKey, modelVersion],
    });
  } catch (error: any) {
    console.error('Error querying source models', { error, stack: error.stack });

    throw error;
  }
}
