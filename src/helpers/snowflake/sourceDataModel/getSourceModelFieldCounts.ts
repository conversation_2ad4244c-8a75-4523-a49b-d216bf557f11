import { Context } from '@/types';
import { genInParams } from '@/utils/sql';

type Props = {
  modelIds: string[];
};

type ResultRow = {
  model_id: string;
  count: number;
};

export default async function getSourceModelFieldCounts(
  props: Props,
  ctx: Context,
): Promise<ResultRow[]> {
  const { modelIds } = props;
  const { db } = ctx;

  try {
    const sqlText = `
      select
        model_id as "model_id"
        , count(*) as "count"
      from dynamic_transformation.config.source_data_dictionary
      where model_id in ${genInParams(modelIds)}
      group by 1
  `;

    return await db.execute({
      sqlText,
      binds: modelIds,
    });
  } catch (error: any) {
    console.error('Error querying source model field counts', { error, stack: error.stack });

    throw error;
  }
}
