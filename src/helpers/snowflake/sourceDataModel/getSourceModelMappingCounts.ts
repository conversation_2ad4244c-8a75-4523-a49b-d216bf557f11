import { Context } from '@/types';
import { genInParams } from '@/utils/sql';

type Props = {
  sourceModelKey: string;
  modelNames: string[];
  modelVersion?: string;
};

type ResultRow = {
  model_name: string;
  count: number;
};

export default async function getSourceModelMappingCounts(
  props: Props,
  ctx: Context,
): Promise<ResultRow[]> {
  const { sourceModelKey, modelNames, modelVersion = '1.00.0000' } = props;
  const { db } = ctx;

  try {
    const sqlText = `
      select
        split_part(s.value:name, '__', 2) as "model_name"
        , count(*) as "count"
      from dynamic_transformation.config.models m
      , lateral flatten(input => transformations) t
      , lateral flatten(input => get_path(t.value, 'sources')) s
      , lateral flatten(input => get_path(s.value, 'mappings')) maps
      where m.origin = 'GESTALT'
      and m.version = ?
      and lower(t.key) = lower(?)
      and split_part(s.value:name, '__', 2) in ${genInParams(modelNames)}
      and maps.value:source_field is not null
      group by 1
    `;

    const lowerModelNames = modelNames.map((name) => name.toLowerCase());

    return await db.execute({
      sqlText,
      binds: [modelVersion, sourceModelKey, ...lowerModelNames],
    });
  } catch (error: any) {
    console.error('Error querying source model mapping counts', { error, stack: error.stack });

    throw error;
  }
}
