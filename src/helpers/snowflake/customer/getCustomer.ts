import { Context, Customer } from '@/types';

type Props = {
  authOrgId: string;
};

const sqlText = `
  select 
    id as "customerId"
    , name as "customerName"
    , slug as "customerSlug"
    , key as "customerKey"
    , auth_id as "customerAuthId"
    , dashboards as "customerDashboards"
    , config:provision_fivetran_destination::STRING as "customerFivetranDestId"
    , fivetran_transformation_id as "customerFivetranTransformationId"
    , config as "customerConfig"
  from config.customers
  where auth_id = :1
`;

export default async function getCustomer(props: Props, ctx: Context): Promise<Customer> {
  const { authOrgId } = props;
  const { db } = ctx;

  const [customer] = await db.execute({ sqlText, binds: [authOrgId] });

  return customer;
}
