import { Context, Model } from '@/types';

type Props = {
  tableName: string;
  version?: string;
};

const sqlText = `
  select
    id as "id",
    name as "name",
    schema as "schema",
    transformations as "transformations",
    metadata:description as "description",
    metadata:type as "type"
  from dynamic_transformation.config.models
  where name = :1
  and version = :2
  and origin = 'GESTALT'
`;

// As we move forward, we should continue to move towards using ids to avoid string-matching
// with names and the somewhat fragile transformation key comparison. This function should be deleted
// once we are fully using ids
export default async function getTargetModel(props: Props, ctx: Context): Promise<Model | null> {
  const { tableName, version = '1.00.0000' } = props;
  const { db } = ctx;

  const qTableName = tableName.toUpperCase();

  const [model] = await db.execute({
    sqlText,
    binds: [qTableName, version],
  });

  if (!model) {
    return null;
  }

  return {
    ...model,
    description: model.description || `A Gestalt model for ${model.name.toLowerCase()}.`,
  };
}
