import { sortBy } from 'lodash';
import { Context, SourceModel } from '@/types';
import { findGeneralType } from '@/utils/data';

type Props = {
  customerId: string;
  modelVersion?: string;
};

const sqlText = `
  select
    src.id_model as "id"
    , src.text_name as "name"
    , src.text_warehouse_name as "warehouseName"
    , src.text_origin as "origin"
    , src.var_dictionary as "dictionary"
    , src.num_field_count as "fieldCount"
    , src.num_mapping_count as "mappingCount"
  from config.customer_source_models_dictionary as src
  where src.id_customer = :1
  and src.text_version = :2
  and src.num_mapping_count > 0
`;

export default async function getSourceDataDictionaries(
  props: Props,
  ctx: Context,
): Promise<SourceModel[]> {
  const { customerId, modelVersion = 'v1.0.0' } = props;
  const { db } = ctx;

  const models = await db.execute({ sqlText, binds: [customerId, modelVersion] });

  const enhanced = models.map((model) => ({
    ...model,
    dictionary: model.dictionary.map((field: any) => ({
      ...field,
      gen_field_type: findGeneralType(field.field_type, field.field_name),
    })),
  }));

  return sortBy(enhanced, 'name');
}
