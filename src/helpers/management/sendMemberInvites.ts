import { Context, MemberInvite } from '@/types';
import { snakeCase } from 'lodash';

type Props = {
  invites: MemberInvite[];
  orgId: string;
  inviterName: string;
  customerKey: string;
};

export default async function sendMemberInvites(props: Props, ctx: Context) {
  const { invites, orgId, inviterName, customerKey } = props;
  const { auth } = ctx;

  const connections = await auth.getEnabledConnections(orgId);

  // We're determining the right database connection based on customer key, so naming convention is important
  const dbConnection = connections.find(
    (conn) => snakeCase(conn.connection.name) === snakeCase(customerKey),
  );

  await auth.sendOrgInvites({
    orgId,
    connectionId: dbConnection?.connection_id,
    invites,
    inviterName,
  });
}
