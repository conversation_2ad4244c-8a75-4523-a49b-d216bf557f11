import { useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useUserStore, useDictionaryStore } from '@/stores';
import { useCustomer } from '@/hooks';

// used to initialize global stores on app load
export default function useAppInitialization() {
  const { isLoading } = useUser();
  const { fetchUserInfo } = useUserStore();
  const { fetchModels } = useDictionaryStore();
  const { customerKey, customerId } = useCustomer();

  useEffect(() => {
    if (isLoading || !customerKey || !customerId) {
      return;
    }

    fetchUserInfo();
    fetchModels({ customerKey, customerId });
  }, [isLoading, fetchUserInfo, fetchModels, customerKey, customerId]);
}
