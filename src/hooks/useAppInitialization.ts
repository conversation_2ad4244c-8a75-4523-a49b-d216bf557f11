import { useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useParams } from 'next/navigation';
import { useUserStore, useDictionaryStore, useDataSourceStore } from '@/stores';
import { useCustomer } from '@/hooks';

// used to initialize global stores on app load
export default function useAppInitialization() {
  const { isLoading } = useUser();
  const { fetchUserInfo } = useUserStore();
  const { fetchTargetModels, fetchSourceModels } = useDictionaryStore();
  const { fetchDataSources, setCurrentDataSource, dataSources } = useDataSourceStore();
  const { customerKey, customerId } = useCustomer();
  const params = useParams();

  useEffect(() => {
    if (isLoading || !customerKey || !customerId) {
      return;
    }

    fetchUserInfo();
    fetchTargetModels({ customerKey, customerId });
    fetchSourceModels({ customerId });
    fetchDataSources({ customerId });
  }, [
    isLoading,
    fetchUserInfo,
    fetchTargetModels,
    fetchSourceModels,
    customerKey,
    customerId,
    fetchDataSources,
  ]);

  useEffect(() => {
    if (!params?.id || !dataSources) {
      return;
    }

    setCurrentDataSource(params.id as string);
  }, [params, dataSources, setCurrentDataSource]);
}
