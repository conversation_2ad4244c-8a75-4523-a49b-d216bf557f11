import { useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useUserStore, useDictionaryStore } from '@/stores';
import { useCustomer } from '@/hooks';

// used to initialize global stores on app load
export default function useAppInitialization() {
  const { isLoading } = useUser();
  const { fetchUserInfo } = useUserStore();
  const { fetchTargetModels, fetchSourceModels } = useDictionaryStore();
  const { customerKey, customerId } = useCustomer();

  useEffect(() => {
    if (isLoading || !customerKey || !customerId) {
      return;
    }

    fetchUserInfo();
    fetchTargetModels({ customerKey, customerId });
    fetchSourceModels({ customerId });
  }, [isLoading, fetchUserInfo, fetchTargetModels, fetchSourceModels, customerKey, customerId]);
}
