import { useEffect, RefObject } from 'react';

// Use CMD/Ctrl + K to focus an input, useful standard for quick search
export default function useFocusInput(ref: RefObject<HTMLInputElement | undefined>) {
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        ref?.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => document.removeEventListener('keydown', handleKeyDown);
  });
}
