import { useEffect } from 'react';
import { useParams, usePathname } from 'next/navigation';
import { useDataSourceStore } from '@/stores';
import { usePage } from '@/hooks';

// This is a convenience hook to keep store and provider properties in sync with the route params.
export default function useRouteSync() {
  const { setCurrentDataSource, dataSources } = useDataSourceStore();
  const { setPageProps } = usePage();
  const params = useParams();
  const pathname = usePathname();

  useEffect(() => {
    if (!params?.id || !dataSources?.length) {
      return;
    }

    setCurrentDataSource(params.id as string);
  }, [params, dataSources, setCurrentDataSource]);

  useEffect(() => {
    if (!pathname) {
      return;
    }

    if (!pathname.includes('insights')) {
      setPageProps({ sidebarOpen: true });
    }
  }, [pathname, setPageProps]);
}
