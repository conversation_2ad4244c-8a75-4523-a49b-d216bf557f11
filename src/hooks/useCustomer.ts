import { Customer } from '@/types';
import { useCustomerStore } from '@/stores';

const DEFAULT_CUSTOMER: Customer = {
  customerId: '',
  customerName: '',
  customerSlug: '',
  customerKey: '',
  customerAuthId: '',
  customerDashboards: [],
  customerConfig: {},
};

// convenience hook to avoid large refactor – behaves like provider
export default function useCustomer() {
  const { customer } = useCustomerStore();

  return customer || DEFAULT_CUSTOMER;
}
