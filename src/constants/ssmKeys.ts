export const SSM_KEYS = [
  {
    path: '/customer-advanced-financial/omni/prod/credentials/secret',
    envVar: 'ADVANCED_FINANCIAL_OMNI_SECRET',
  },
  {
    path: '/customer-arra-finance/omni/prod/credentials/secret',
    envVar: 'ARRA_FINANCE_OMNI_SECRET',
  },
  {
    path: '/customer-equity-sales-finance/omni/prod/credentials/secret',
    envVar: 'EQUITY_SALES_FINANCE_OMNI_SECRET',
  },
  {
    path: '/customer-first-step-homes/omni/prod/credentials/secret',
    envVar: 'FIRST_STEP_HOMES_OMNI_SECRET',
  },
  { path: '/customer-marine-one/omni/prod/credentials/secret', envVar: 'MARINE_ONE_OMNI_SECRET' },
  { path: '/customer-on-the-road/omni/prod/credentials/secret', envVar: 'ON_THE_ROAD_OMNI_SECRET' },
  {
    path: '/customer-source-one-financial/omni/prod/credentials/secret',
    envVar: 'SOURCE_ONE_OMNI_SECRET',
  },
  {
    path: '/gestalt/client/auth0/gestalt-provision-tools/credentials/api-client-id',
    envVar: 'AUTH0_MANAGEMENT_API_CLIENT_ID',
  },
  {
    path: '/gestalt/client/auth0/gestalt-provision-tools/credentials/api-client-secret',
    envVar: 'AUTH0_MANAGEMENT_API_CLIENT_SECRET',
  },
  { path: '/gestalt/client/auth0/prod/credentials/api-client-id', envVar: 'AUTH0_API_CLIENT_ID' },
  {
    path: '/gestalt/client/auth0/prod/credentials/api-client-secret',
    envVar: 'AUTH0_API_CLIENT_SECRET',
  },
  { path: '/gestalt/client/auth0/prod/credentials/client-secret', envVar: 'AUTH0_CLIENT_SECRET' },
  { path: '/gestalt/client/auth0/prod/credentials/secret', envVar: 'AUTH0_SECRET' },
  { path: '/gestalt/client/aws/prod/credentials/account-id', envVar: 'AWS_ACCOUNT_ID' },
  { path: '/gestalt/client/aws/prod/credentials/app-id', envVar: 'AWS_APPCONFIG_APP_ID' },
  { path: '/gestalt/client/aws/prod/credentials/config-id', envVar: 'AWS_APPCONFIG_CONFIG_ID' },
  { path: '/gestalt/client/aws/prod/credentials/env-id', envVar: 'AWS_APPCONFIG_ENV_ID' },
  {
    path: '/gestalt/client/fingoal/prod/credentials/api-client-id',
    envVar: 'FINGOAL_API_CLIENT_ID',
  },
  {
    path: '/gestalt/client/fingoal/prod/credentials/api-client-secret',
    envVar: 'FINGOAL_API_CLIENT_SECRET',
  },
  { path: '/gestalt/client/fivetran/prod/credentials/api-id', envVar: 'FIVETRAN_API_ID' },
  { path: '/gestalt/client/fivetran/prod/credentials/api-secret', envVar: 'FIVETRAN_API_SECRET' },
  { path: '/ai/smartmapper/production/api-keys/ui', envVar: 'MAPPER_API_KEY' },
  { path: '/gestalt/client/omni/prod/credentials/api-key', envVar: 'OMNI_API_KEY' },
  { path: '/gestalt/client/omni/prod/credentials/secret', envVar: 'OMNI_SECRET' },
  { path: '/gestalt/client/panda-doc/prod/credentials/api-key', envVar: 'PANDA_DOC_API_KEY' },
  { path: '/gestalt/client/slack/prod/credentials/api-token', envVar: 'SLACK_API_TOKEN' },
  { path: '/gestalt/client/snowflake/prod/credentials/account', envVar: 'SNOWFLAKE_ACCOUNT' },
  { path: '/gestalt/client/snowflake/prod/credentials/password', envVar: 'SNOWFLAKE_PASSWORD' },
  { path: '/gestalt/client/snowflake/prod/credentials/role', envVar: 'SNOWFLAKE_ROLE' },
  { path: '/gestalt/client/snowflake/prod/credentials/username', envVar: 'SNOWFLAKE_USERNAME' },
  { path: '/gestalt/client/snowflake/prod/credentials/warehouse', envVar: 'SNOWFLAKE_WAREHOUSE' },
  { path: '/gestalt/client/auth0/prod/credentials/app-client-id', envVar: 'AUTH0_APP_CLIENT_ID' },
];
