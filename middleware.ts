import { NextRequest, NextResponse } from 'next/server';

function isValidHost(host: string | null): boolean {
  if (!host) {
    return false;
  }

  // Allow localhost for development
  if (host.startsWith('localhost')) {
    return true;
  }

  // Allow any subdomain of gestalttech.com (but not the root domain)
  if (host.endsWith('.gestalttech.com')) {
    return true;
  }

  return false;
}

export function middleware(request: NextRequest) {
  const host = request.headers.get('host');

  // Block requests with invalid host headers
  if (!isValidHost(host)) {
    return new NextResponse('Invalid Host Header', { status: 400 });
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};