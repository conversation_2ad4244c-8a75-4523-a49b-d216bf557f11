apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: gestalt-client-aws-secrets
  namespace: default
spec:
  provider: aws

  secretObjects:
    - secretName: gestalt-client-ssm-secrets
      type: Opaque
      data:
        - objectName: auth0Secret # from parameters.objects.objectAlias
          key: AUTH0_SECRET # env var name
        - objectName: auth0ClientSecret
          key: AUTH0_CLIENT_SECRET
        - objectName: awsAppConfigAppId
          key: AWS_APPCONFIG_APP_ID
        - objectName: awsAppConfigConfigId
          key: AWS_APPCONFIG_CONFIG_ID
        - objectName: awsAppConfigEnvId
          key: AWS_APPCONFIG_ENV_ID
        - objectName: fivetranApiId
          key: FIVETRAN_API_ID
        - objectName: fivetranApiSecret
          key: FIVETRAN_API_SECRET
        - objectName: omniSecret
          key: OMNI_SECRET
        - objectName: sourceOneOmniSecret
          key: SOURCE_ONE_OMNI_SECRET
        - objectName: marineOneOmniSecret
          key: MARINE_ONE_OMNI_SECRET
        - objectName: snowflakeAccount
          key: SNOWFLAKE_ACCOUNT
        - objectName: snowflakePw
          key: SNOWFLAKE_PASSWORD
        - objectName: snowflakeRole
          key: SNOWFLAKE_ROLE
        - objectName: snowflakeUser
          key: SNOWFLAKE_USERNAME
        - objectName: snowflakeWarehouse
          key: SNOWFLAKE_WAREHOUSE
        - objectName: omniApiKey
          key: OMNI_API_KEY
        - objectName: auth0ApiClientId
          key: AUTH0_API_CLIENT_ID
        - objectName: auth0ApiClientSecret
          key: AUTH0_API_CLIENT_SECRET
        - objectName: pandaDocApiKey
          key: PANDA_DOC_API_KEY
        - objectName: slackApiToken
          key: SLACK_API_TOKEN
        - objectName: onTheRoadOmniSecret
          key: ON_THE_ROAD_OMNI_SECRET
        - objectName: arraFinanceOmniSecret
          key: ARRA_FINANCE_OMNI_SECRET
        - objectName: advancedFinancialOmniSecret
          key: ADVANCED_FINANCIAL_OMNI_SECRET
        - objectName: equitySalesFinanceOmniSecret
          key: EQUITY_SALES_FINANCE_OMNI_SECRET
        - objectName: awsAccountId
          key: AWS_ACCOUNT_ID
        - objectName: mapperApiKey
          key: MAPPER_API_KEY
        - objectName: auth0ManagementApiClientId
          key: AUTH0_MANAGEMENT_API_CLIENT_ID
        - objectName: auth0ManagementApiClientSecret
          key: AUTH0_MANAGEMENT_API_CLIENT_SECRET
        - objectName: fingoalApiClientId
          key: FINGOAL_API_CLIENT_ID
        - objectName: fingoalApiClientSecret
          key: FINGOAL_API_CLIENT_SECRET
        - objectName: auth0AppClientId
          key: AUTH0_APP_CLIENT_ID
        - objectName: hondaCanadaOmniSecret
          key: HONDA_CANADA_OMNI_SECRET

  parameters:
    objects: |
      - objectName: "/customer-advanced-financial/omni/prod/credentials/secret"
        objectAlias: advancedFinancialOmniSecret
        objectType: "ssmparameter"
      - objectName: "/customer-arra-finance/omni/prod/credentials/secret"
        objectAlias: arraFinanceOmniSecret
        objectType: "ssmparameter"
      - objectName: "/customer-marine-one/omni/prod/credentials/secret"
        objectAlias: marineOneOmniSecret
        objectType: "ssmparameter"
      - objectName: "/customer-on-the-road/omni/prod/credentials/secret"
        objectAlias: onTheRoadOmniSecret
        objectType: "ssmparameter"
      - objectName: "/customer-source-one-financial/omni/prod/credentials/secret"
        objectAlias: sourceOneOmniSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/gestalt-provision-tools/credentials/api-client-id"
        objectAlias: auth0ManagementApiClientId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/gestalt-provision-tools/credentials/api-client-secret"
        objectAlias: auth0ManagementApiClientSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/prod/credentials/api-client-id"
        objectAlias: auth0ApiClientId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/prod/credentials/api-client-secret"
        objectAlias: auth0ApiClientSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/prod/credentials/client-secret"
        objectAlias: auth0ClientSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/prod/credentials/secret"
        objectAlias: auth0Secret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/aws/prod/credentials/account-id"
        objectAlias: awsAccountId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/aws/prod/credentials/app-id"
        objectAlias: awsAppConfigAppId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/aws/prod/credentials/config-id"
        objectAlias: awsAppConfigConfigId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/aws/prod/credentials/env-id"
        objectAlias: awsAppConfigEnvId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/fingoal/prod/credentials/api-client-id"
        objectAlias: fingoalApiClientId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/fingoal/prod/credentials/api-client-secret"
        objectAlias: fingoalApiClientSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/fivetran/prod/credentials/api-id"
        objectAlias: fivetranApiId
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/fivetran/prod/credentials/api-secret"
        objectAlias: fivetranApiSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/smart-mapper/prod/credentials/api-key"
        objectAlias: mapperApiKey
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/omni/prod/credentials/api-key"
        objectAlias: omniApiKey
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/omni/prod/credentials/secret"
        objectAlias: omniSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/panda-doc/prod/credentials/api-key"
        objectAlias: pandaDocApiKey
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/slack/prod/credentials/api-token"
        objectAlias: slackApiToken
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/snowflake/prod/credentials/account"
        objectAlias: snowflakeAccount
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/snowflake/prod/credentials/password"
        objectAlias: snowflakePw
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/snowflake/prod/credentials/role"
        objectAlias: snowflakeRole
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/snowflake/prod/credentials/username"
        objectAlias: snowflakeUser
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/snowflake/prod/credentials/warehouse"
        objectAlias: snowflakeWarehouse
        objectType: "ssmparameter"
      - objectName: "/customer-equity-sales-finance/omni/prod/credentials/secret"
        objectAlias: equitySalesFinanceOmniSecret
        objectType: "ssmparameter"
      - objectName: "/gestalt/client/auth0/prod/credentials/app-client-id"
        objectAlias: auth0AppClientId
        objectType: "ssmparameter"
      - objectName: "/customer-honda-canada/omni/prod/credentials/secret"
        objectAlias: hondaCanadaOmniSecret
        objectType: "ssmparameter"
