apiVersion: v1
kind: ServiceAccount
metadata:
  name: gestalt-client-sa
  namespace: default
  annotations:
    # We should move this to a pod specific role like gestalt-client-pod-role with trust policy for IRSA
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-node-group-general
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: gestalt-client
  name: gestalt-client
  annotations:
    # Automatically restart pods when secrets change
    secret.reloader.stakater.com/auto: "true"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gestalt-client
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0 # Never allow all pods to be unavailable
      maxSurge: 1       # Allow 1 extra pod during update
  template:
    metadata:
      labels:
        app: gestalt-client
    spec:
      serviceAccountName: gestalt-client-sa
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: 'gestalt-client-aws-secrets'
      containers:
        - name: appconfig-agent
          image: public.ecr.aws/aws-appconfig/aws-appconfig-agent:2.x
          resources: {}
          ports:
            - name: http
              containerPort: 2772
              protocol: TCP
          env:
            - name: SERVICE_REGION
              value: us-east-2
          imagePullPolicy: IfNotPresent
        - name: gestalt-client
          image: ************.dkr.ecr.us-east-2.amazonaws.com/gestalt-client-app:latest
          resources: {}
          volumeMounts:
            - name: secrets-store-inline
              mountPath: '/mnt/secrets-store'
              readOnly: true
          envFrom:
            - secretRef:
                name: gestalt-client-ssm-secrets
---
apiVersion: v1
kind: Service
metadata:
  name: gestalt-client-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:us-east-2:************:certificate/2e62f9fa-16e9-4705-831a-bc98e542e3da
spec:
  selector:
    app: gestalt-client
  type: NodePort
  ports:
    - name: nginx
      protocol: TCP
      port: 80
      targetPort: 3000
---
apiVersion: eks.amazonaws.com/v1
kind: IngressClassParams
metadata:
  name: alb
spec:
  scheme: internet-facing
---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: alb
  annotations:
    # Use this annotation to set an IngressClass as Default
    # If an Ingress doesn't specify a class, it will use the Default
    ingressclass.kubernetes.io/is-default-class: 'true'
spec:
  # Configures the IngressClass to use EKS Auto Mode
  controller: eks.amazonaws.com/alb
  parameters:
    apiGroup: eks.amazonaws.com
    kind: IngressClassParams
    # Use the name of the IngressClassParams set in the previous step
    name: alb
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-2:************:certificate/2e62f9fa-16e9-4705-831a-bc98e542e3da
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/group: gestalt-client-service
    alb.ingress.kubernetes.io/ssl-policy: 'ELBSecurityPolicy-TLS13-1-2-2021-06'
    alb.ingress.kubernetes.io/wafv2-acl-arn: arn:aws:wafv2:us-east-2:************:regional/webacl/gestalttechACL/a22bd95c-63df-484a-8516-d3634a62fc01
    alb.ingress.kubernetes.io/load-balancer-attributes: >
      routing.http.drop_invalid_header_fields.enabled=true,
      idle_timeout.timeout_seconds=60,
      routing.http2.enabled=true
    alb.ingress.kubernetes.io/listener-attributes.HTTPS-443: >
      routing.http.response.strict_transport_security.header_value=max-age=********; includeSubDomains; preload;,
      routing.http.response.x_frame_options.header_value=DENY,
      routing.http.response.content_security_policy.header_value=upgrade-insecure-requests; frame-ancestors none;,
      routing.http.response.x_content_type_options.header_value=nosniff,
  name: gestalt-client-ingress
  namespace: default
spec:
  ingressClassName: alb
  rules:
    - host: '*.gestalttech.com'
      http:
        paths:
          - backend:
              service:
                name: gestalt-client-service
                port:
                  number: 80
            path: /
            pathType: Prefix
