apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../prod

patches:
  - path: staging-ingress.yaml
    target:
      kind: Ingress
      name: gestalt-client-ingress

namespace: staging

replicas:
  - name: gestalt-client
    count: 1

images:
  - name: 505912074795.dkr.ecr.us-east-2.amazonaws.com/gestalt-client-app
    newTag: image_tag  # Replaced during deployment with staging-{git-sha}

# Resources have same names as production but in staging namespace:
# gestalt-client (in staging namespace)
# gestalt-client-service (in staging namespace)
# gestalt-client-aws-secrets (in staging namespace)