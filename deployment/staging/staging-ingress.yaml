apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gestalt-client-ingress
  annotations:
    alb.ingress.kubernetes.io/group: gestalt-client-staging
spec:
  rules:
    - host: 'staging.gestalttech.com'
      http:
        paths:
          - backend:
              service:
                name: gestalt-client-service
                port:
                  number: 80
            path: /
            pathType: Prefix