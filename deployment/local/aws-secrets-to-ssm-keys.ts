import * as fs from 'fs';
import * as yaml from 'js-yaml';
import * as path from 'path';

interface SecretObject {
  objectName: string;
  key: string;
}

interface SecretProviderClass {
  kind: string;
  spec: {
    parameters: {
      objects: string;
    };
    secretObjects: Array<{
      data: SecretObject[];
    }>;
  };
}

interface SsmObject {
  objectName: string;
  objectAlias: string;
  objectType: string;
}

interface SsmKey {
  path: string;
  envVar: string;
}

function generateSsmKeys(yamlPath: string): Record<string, string> {
  try {
    console.info(`Generating SSM keys from YAML file: ${yamlPath}`);

    if (!fs.existsSync(yamlPath)) {
      throw new Error(`YAML file not found at: ${yamlPath}`);
    }

    const yamlContent = fs.readFileSync(yamlPath, 'utf8');
    const docs = yaml.loadAll(yamlContent) as any[];

    const secretProviderClass = docs.find(doc =>
      doc && doc.kind === 'SecretProviderClass'
    ) as SecretProviderClass;

    if (!secretProviderClass) {
      throw new Error('SecretProviderClass not found in YAML file');
    }

    const objectsStr = secretProviderClass.spec.parameters.objects;
    const objects = yaml.load(objectsStr) as SsmObject[];

    const ssmKeysMap: Record<string, string> = {};

    objects.forEach(obj => {
      const secretObj = secretProviderClass.spec.secretObjects[0].data.find(
        item => item.objectName === obj.objectAlias
      );

      if (secretObj) {
        ssmKeysMap[secretObj.key] = obj.objectName;
      }
    });

    return ssmKeysMap;

  } catch (error) {
    console.error('Error generating SSM keys:', error);
    return {};
  }
}

function upsertEnvLocal(ssmKeys: Record<string, string>) {
  const envPath = path.join(__dirname, '../../.env.local');
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }
  
  // Remove all existing SSMKEY_ lines and generated comment
  const envLines = envContent.split('\n')
    .filter(line => !line.startsWith('SSMKEY_') && !line.includes('Generated SSMKEY_ mappings'));
  
  // Add comment and new SSMKEY_ prefixed env vars (sorted by env key)
  envLines.push('');
  envLines.push('# Generated SSMKEY_ mappings from aws-secrets.yaml');
  Object.entries(ssmKeys)
    .sort(([a], [b]) => a.localeCompare(b))
    .forEach(([envKey, ssmPath]) => {
      envLines.push(`SSMKEY_${envKey}=${ssmPath}`);
    });
  
  fs.writeFileSync(envPath, envLines.join('\n'));
  console.log(`Updated .env.local with ${Object.keys(ssmKeys).length} SSMKEY_ mappings`);
}

const yamlPath = path.join(__dirname, '../aws-secrets.yaml');
const ssmKeys = generateSsmKeys(yamlPath);
upsertEnvLocal(ssmKeys);