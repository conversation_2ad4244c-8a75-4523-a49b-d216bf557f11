AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::LanguageExtensions'
Description: 'CloudFormation template for Gestalt Client AppConfig setup'

Resources:
  GestaltClientApplication:
    Type: 'AWS::AppConfig::Application'
    Properties:
      Name: 'Gestalt Client'
      Description: 'App Configuration for Gestalt Client'

  ProductionEnvironment:
    Type: 'AWS::AppConfig::Environment'
    Properties:
      ApplicationId: !Ref GestaltClientApplication
      Name: 'production'
      Description: 'Production environment for Gestalt Client'

  GestaltClientConfigProfile:
    Type: 'AWS::AppConfig::ConfigurationProfile'
    Properties:
      ApplicationId: !Ref GestaltClientApplication
      Name: 'gestalt-client-config'
      Description: 'Configuration profile for Gestalt Client'
      LocationUri: 'hosted'
      Type: 'AWS.AppConfig.FeatureFlags'

  InitialConfiguration:
    Type: 'AWS::AppConfig::HostedConfigurationVersion'
    Properties:
      ApplicationId: !Ref GestaltClientApplication
      ConfigurationProfileId: !Ref GestaltClientConfigProfile
      ContentType: 'application/json'
      Content:
        Fn::ToJsonString:
          version: "1"
          flags:
            can-run-smart-mapper:
              name: "can-run-smart-mapper"
              description: "Flag for allowing the smart mapper flow (used for a new source)"
              attributes:
                enabled-orgs:
                  constraints:
                    type: "array"
                    elements:
                      type: "string"
            can-edit-mappings:
              name: "can-edit-mappings"
              description: "Flag for allowing data table mapping editing"
              attributes:
                enabled-orgs:
                  constraints:
                    type: "array"
                    elements:
                      type: "string"
            can-view-anomalies:
              name: "can-view-anomalies"
              description: "Flag for enabling viewing of table anomalies"
              attributes:
                enabled-users:
                  constraints:
                    type: "array"
                    elements:
                      type: "string"
            can-embed-workbook:
              name: "can-embed-workbook"
              description: "Allows users to embed the full Omni application into the reports page."
              attributes:
                enabled-orgs:
                  constraints:
                    type: "array"
                    elements:
                      type: "string"
          values:
            can-run-smart-mapper:
              enabled: true
              enabled-orgs:
                - "org_VTSxPO6vcHXsmcD5"
            can-edit-mappings:
              enabled: false
              enabled-orgs:
                - "org_VTSxPO6vcHXsmcD5"
            can-view-anomalies:
              enabled: false
              enabled-users:
                  - auth0|6569fe64a19599c9209796e8
                  - auth0|64b95b1245f59384f659bd07
                  - auth0|650227d76ea825ef4f5c075a
                  - auth0|65849bb2e51d952579e9b616
            can-embed-workbook:
              enabled: true
              enabled-orgs:
                - "org_VTSxPO6vcHXsmcD5"

Outputs:
  ApplicationId:
    Description: 'AppConfig Application ID'
    Value: !Ref GestaltClientApplication

  EnvironmentId:
    Description: 'Production Environment ID'
    Value: !Ref ProductionEnvironment

  ConfigurationProfileId:
    Description: 'Configuration Profile ID'
    Value: !Ref GestaltClientConfigProfile

  ConfigurationVersion:
    Description: 'Configuration Version'
    Value: !Ref InitialConfiguration
