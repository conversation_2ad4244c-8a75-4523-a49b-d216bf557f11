AWSTemplateFormatVersion: '2010-09-09'
Parameters:
  RepositoryName:
    Type: String
    Description: Name of the ECR repository

Resources:
  ECRRepository:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: !Ref RepositoryName
      ImageScanningConfiguration:
        ScanOnPush: true
      LifecyclePolicy:
        LifecyclePolicyText: |
          {
            "rules": [
              {
                "rulePriority": 1,
                "description": "Keep 3 most recent prod-* images",
                "selection": {
                  "tagStatus": "tagged",
                  "tagPrefixList": ["prod-"],
                  "countType": "imageCountMoreThan",
                  "countNumber": 3
                },
                "action": {
                  "type": "expire"
                }
              },
              {
                "rulePriority": 2,
                "description": "Keep 3 most recent staging-* images",
                "selection": {
                  "tagStatus": "tagged",
                  "tagPrefixList": ["staging-"],
                  "countType": "imageCountMoreThan",
                  "countNumber": 3
                },
                "action": {
                  "type": "expire"
                }
              },
              {
                "rulePriority": 3,
                "description": "Remove untagged images older than 1 day",
                "selection": {
                  "tagStatus": "untagged",
                  "countType": "sinceImagePushed",
                  "countUnit": "days",
                  "countNumber": 1
                },
                "action": {
                  "type": "expire"
                }
              }
            ]
          }
